{"$schema": "https://json.schemastore.org/tsconfig", "display": "NestJS", "extends": "./base.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "dist"]}