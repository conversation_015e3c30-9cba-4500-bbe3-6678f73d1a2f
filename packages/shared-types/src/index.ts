// User types
export interface User {
  id: string;
  email: string;
  name: string;
  domain?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreateDto {
  email: string;
  password: string;
  name: string;
}

export interface UserLoginDto {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

// Domain types
export interface Domain {
  id: string;
  domain: string;
  title?: string;
  description?: string;
  userId: string;
  verified: boolean;
  verificationMethod?: "dns" | "meta" | "file";
  verificationCode?: string;
  verifiedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DomainCreateDto {
  domain: string;
  description?: string;
  keywords?: string[];
}

export interface DomainVerifyDto {
  domain: string;
  verificationMethod: "dns" | "meta" | "file";
  dnsValue?: string;
  metaContent?: string;
  fileContent?: string;
}

// Target domain types
export interface TargetDomain {
  id: string;
  domain: string;
  title?: string;
  description?: string;
  relevanceScore?: number;
  domainAuthority?: number;
  backlinks?: number;
  traffic?: number;
  contactEmail?: string;
  contactPage?: string;
  categories?: string[];
  keywords?: string[];
  lastUpdated?: Date;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TargetDomainCreateDto {
  domain: string;
  title?: string;
  description?: string;
  categories?: string[];
  notes?: string;
}

export interface TargetDomainBulkCreateDto {
  domains: TargetDomainCreateDto[];
}

// Analysis types
export interface DomainAnalysis {
  id: string;
  targetDomainId: string;
  userDomainId: string;
  relevanceScore: number;
  domainAuthority?: number;
  backlinks?: number;
  traffic?: number;
  trafficTrend?: "upward" | "downward" | "stable";
  topKeywords?: string[];
  topPages?: string[];
  industry?: string;
  category?: string;
  topCountry?: string;
  websiteAge?: number;
  language?: string;
  contactEmail?: string;
  contactPage?: string;
  categories?: string[];
  keywords?: string[];
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalysisResultDto {
  id: string;
  domain: string;
  title?: string;
  description?: string;
  relevanceScore: number;
  domainAuthority?: number;
  backlinks?: number;
  traffic?: number;
  trafficTrend?: "upward" | "downward" | "stable";
  topKeywords?: string[];
  topPages?: string[];
  industry?: string;
  category?: string;
  topCountry?: string;
  websiteAge?: number;
  language?: string;
  contactEmail?: string;
  contactPage?: string;
  categories?: string[];
  keywords?: string[];
  lastUpdated: Date;
}

// Email types
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables?: string[];
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailTemplateCreateDto {
  name: string;
  subject: string;
  body: string;
  variables?: string[];
}

export interface OutreachEmail {
  id: string;
  targetDomainId: string;
  userId: string;
  subject: string;
  body: string;
  recipient: string;
  status: "pending" | "sent" | "opened" | "failed";
  sentAt?: Date;
  openedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface OutreachEmailCreateDto {
  targetDomainIds: string[];
  templateId?: string;
  subject: string;
  body: string;
}

export interface OutreachEmailStatusDto {
  id: string;
  status: "pending" | "sent" | "opened" | "failed";
  sentAt?: Date;
  openedAt?: Date;
}
