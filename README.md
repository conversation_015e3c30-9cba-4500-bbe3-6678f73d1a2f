# BackLink - Domain Analysis & Outreach Platform

A full-stack web application for domain analysis and outreach, built with Next.js, NestJS, and PostgreSQL.

## Features

- User authentication and management
- Domain verification and management
- CSV upload for target domains
- Domain analysis and compatibility assessment
- Contact information retrieval
- Outreach email functionality
- Modern UI with light/dark theme

## Tech Stack

### Frontend

- Next.js 14
- React
- Tailwind CSS
- shadcn/ui components

### Backend

- NestJS
- TypeORM
- PostgreSQL
- JWT Authentication

### Monorepo

- Turborepo
- pnpm workspaces

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 8+
- PostgreSQL 14+

### Installation

1. Clone the repository:
   \`\`\`bash
   git clone https://github.com/yourusername/backlink.git
   cd backlink
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   pnpm install
   \`\`\`

3. Set up environment variables:

Create a `.env.local` file in the root directory:
\`\`\`

# Database

DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=backlink

# JWT

JWT_SECRET=your-secret-key

# Frontend

NEXT_PUBLIC_API_URL=http://localhost:3001

# Feature Flags

BYPASS_DOMAIN_VERIFICATION=false # Set to 'true' to bypass domain verification (for development/testing)
\`\`\`

4. Start the development servers:
   \`\`\`bash
   pnpm dev
   \`\`\`

This will start both the frontend (http://localhost:3000) and backend (http://localhost:3001) servers.

## Project Structure

\`\`\`
backlink/
├── apps/
│ ├── api/ # NestJS backend
│ │ ├── src/
│ │ │ ├── auth/ # Authentication module
│ │ │ ├── users/ # Users module
│ │ │ ├── domains/ # Domains module
│ │ │ ├── target-domains/ # Target domains module
│ │ │ ├── analysis/ # Analysis module
│ │ │ ├── emails/ # Emails module
│ │ │ ├── app.module.ts
│ │ │ └── main.ts
│ │ └── package.json
│ └── web/ # Next.js frontend
│ ├── src/
│ │ ├── app/ # Next.js app router
│ │ ├── components/ # React components
│ │ ├── lib/ # Utility functions
│ │ └── styles/ # Global styles
│ └── package.json
├── packages/
│ ├── shared-types/ # Shared TypeScript types
│ └── tsconfig/ # Shared TypeScript configs
├── turbo.json
└── package.json
\`\`\`

## Deployment

### Backend (NestJS)

1. Build the backend:
   \`\`\`bash
   cd apps/api
   pnpm build
   \`\`\`

2. Start the production server:
   \`\`\`bash
   pnpm start:prod
   \`\`\`

### Frontend (Next.js)

1. Build the frontend:
   \`\`\`bash
   cd apps/web
   pnpm build
   \`\`\`

2. Start the production server:
   \`\`\`bash
   pnpm start
   \`\`\`

Alternatively, you can deploy the frontend to Vercel:
\`\`\`bash
vercel
\`\`\`

## Development Features

### Domain Verification Bypass

For development and testing purposes, you can bypass the domain verification process by setting the `BYPASS_DOMAIN_VERIFICATION` environment variable to `true` in your `.env.local` file:

```
BYPASS_DOMAIN_VERIFICATION=true
```

When this feature is enabled:

- All domains will be automatically verified without checking DNS records, meta tags, or verification files
- A log message will be generated indicating that verification was bypassed
- This should only be used in development/testing environments, not in production

## License

This project is licensed under the MIT License - see the LICENSE file for details.
