import { NestFactory } from "@nestjs/core";
import { ValidationPipe, ClassSerializerInterceptor } from "@nestjs/common";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import * as cookieParser from "cookie-parser";
import * as compression from "compression";
import helmet from "helmet";
import { Reflector } from "@nestjs/core";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global middleware
  app.use(cookieParser());
  app.use(compression());
  app.use(helmet());

  // Enable CORS
  app.enableCors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    })
  );

  // Global class serializer interceptor to apply @Exclude() decorators
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  // Enhanced Swagger documentation
  const config = new DocumentBuilder()
    .setTitle("BackLink API")
    .setDescription(
      "The BackLink API documentation - Comprehensive API for managing backlinks, domains, and target domains"
    )
    .setVersion("1.0")
    .addBearerAuth(
      {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        name: "Authorization",
        description: "Enter JWT token",
        in: "header",
      },
      "JWT-auth"
    )
    // .addTag('Auth', 'Authentication endpoints')
    // .addTag('Users', 'User management endpoints')
    // .addTag('Domains', 'Domain management endpoints')
    // .addTag('Target Domains', 'Target domain management endpoints')
    // .addTag('Analysis', 'Domain analysis endpoints')
    // .addTag('Backlinks', 'Backlink management endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("api/docs", app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: "list",
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
    },
  });

  // Start the server
  const port = process.env.PORT || 3050;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(
    `Swagger documentation available at: http://localhost:${port}/api/docs`
  );
}
bootstrap();
