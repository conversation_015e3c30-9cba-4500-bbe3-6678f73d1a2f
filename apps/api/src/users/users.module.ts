import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { User } from "./entities/user.entity";
import { TeamMember } from "./entities/team-member.entity";
import { UserSettings } from "./entities/user-settings.entity";

@Module({
  imports: [TypeOrmModule.forFeature([User, TeamMember, UserSettings])],
  providers: [UsersService],
  controllers: [UsersController],
  exports: [UsersService],
})
export class UsersModule {}
