import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from "typeorm";
import { User } from "./user.entity";

@Entity("user_settings")
export class UserSettings {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column("uuid")
  userId: string;

  @OneToOne(() => User)
  @JoinColumn({ name: "userId" })
  user: User;

  @Column("jsonb", { default: () => "'{ \"newDomainAnalysis\": true, \"emailOpened\": true, \"emailClicked\": true, \"emailReplied\": true, \"weeklyReport\": true }'" })
  emailNotifications: {
    newDomainAnalysis: boolean;
    emailOpened: boolean;
    emailClicked: boolean;
    emailReplied: boolean;
    weeklyReport: boolean;
  };

  @Column("jsonb", { default: () => "'{ \"mozApiKey\": \"\", \"semrushApiKey\": \"\", \"ahrefsApiKey\": \"\" }'" })
  apiKeys: {
    mozApiKey?: string;
    semrushApiKey?: string;
    ahrefsApiKey?: string;
  };

  @Column("jsonb", { default: () => "'{ \"fromName\": \"\", \"signature\": \"\", \"replyTo\": \"\" }'" })
  emailSettings: {
    fromName: string;
    signature: string;
    replyTo: string;
  };

  @Column("jsonb", { default: () => "'{ \"theme\": \"system\", \"compactMode\": false }'" })
  appearance: {
    theme: "light" | "dark" | "system";
    compactMode: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
