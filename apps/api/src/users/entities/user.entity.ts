import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Exclude } from "class-transformer";
import { Domain } from "../../domains/entities/domain.entity";
import { TargetDomain } from "../../target-domains/entities/target-domain.entity";
import { EmailTemplate } from "../../emails/entities/email-template.entity";
import { OutreachEmail } from "../../emails/entities/outreach-email.entity";
import { EmailCampaign } from "../../emails/entities/email-campaign.entity";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  name: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ nullable: true })
  domain?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Domain, (domain) => domain.user)
  domains: Domain[];

  @OneToMany(() => TargetDomain, (targetDomain) => targetDomain.user)
  targetDomains: TargetDomain[];

  @OneToMany(() => EmailTemplate, (emailTemplate) => emailTemplate.user)
  emailTemplates: EmailTemplate[];

  @OneToMany(() => OutreachEmail, (outreachEmail) => outreachEmail.user)
  outreachEmails: OutreachEmail[];

  @OneToMany(() => EmailCampaign, (emailCampaign) => emailCampaign.user)
  emailCampaigns: EmailCampaign[];
}
