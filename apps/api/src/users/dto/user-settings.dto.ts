import { ApiProperty } from "@nestjs/swagger";
import { IsObject, IsOptional, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

class EmailNotificationsDto {
  @ApiProperty({ example: true })
  newDomainAnalysis: boolean;

  @ApiProperty({ example: true })
  emailOpened: boolean;

  @ApiProperty({ example: true })
  emailClicked: boolean;

  @ApiProperty({ example: true })
  emailReplied: boolean;

  @ApiProperty({ example: true })
  weeklyReport: boolean;
}

class ApiKeysDto {
  @ApiProperty({ example: "", required: false })
  @IsOptional()
  mozApiKey?: string;

  @ApiProperty({ example: "", required: false })
  @IsOptional()
  semrushApiKey?: string;

  @ApiProperty({ example: "", required: false })
  @IsOptional()
  ahrefsApiKey?: string;
}

class EmailSettingsDto {
  @ApiProperty({ example: "John Doe" })
  fromName: string;

  @ApiProperty({ example: "Best regards,\nJohn <PERSON>e" })
  signature: string;

  @ApiProperty({ example: "<EMAIL>" })
  replyTo: string;
}

class AppearanceDto {
  @ApiProperty({ example: "system", enum: ["light", "dark", "system"] })
  theme: "light" | "dark" | "system";

  @ApiProperty({ example: false })
  compactMode: boolean;
}

export class UserSettingsDto {
  @ApiProperty({ type: EmailNotificationsDto })
  @IsObject()
  @ValidateNested()
  @Type(() => EmailNotificationsDto)
  emailNotifications: EmailNotificationsDto;

  @ApiProperty({ type: ApiKeysDto })
  @IsObject()
  @ValidateNested()
  @Type(() => ApiKeysDto)
  apiKeys: ApiKeysDto;

  @ApiProperty({ type: EmailSettingsDto })
  @IsObject()
  @ValidateNested()
  @Type(() => EmailSettingsDto)
  emailSettings: EmailSettingsDto;

  @ApiProperty({ type: AppearanceDto })
  @IsObject()
  @ValidateNested()
  @Type(() => AppearanceDto)
  appearance: AppearanceDto;
}
