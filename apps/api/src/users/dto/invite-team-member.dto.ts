import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsS<PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class InviteTeamMemberDto {
  @ApiProperty({
    description: "Email of the team member to invite",
    example: "<EMAIL>",
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: "Role of the team member",
    example: "editor",
    enum: ["admin", "editor", "viewer"],
  })
  @IsString()
  @IsNotEmpty()
  role: string;
}
