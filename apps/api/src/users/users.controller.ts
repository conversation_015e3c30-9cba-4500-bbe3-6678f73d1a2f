import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  UseGuards,
  Inject,
  BadRequestException,
  ClassSerializerInterceptor,
  UseInterceptors,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from "@nestjs/swagger";
import { UsersService } from "./users.service";
import type { User } from "./entities/user.entity";
import type { UserSettings } from "./entities/user-settings.entity";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { GetUser } from "../auth/decorators/get-user.decorator";
import { InviteTeamMemberDto } from "./dto/invite-team-member.dto";
import { UserSettingsDto } from "./dto/user-settings.dto";

@ApiTags("users")
@Controller("users")
@UseInterceptors(ClassSerializerInterceptor)
export class UsersController {
  constructor(
    @Inject(UsersService) private readonly usersService: UsersService
  ) {}

  @Get("me")
  @ApiBearerAuth("JWT-auth")
  @ApiOperation({ summary: "Get current user" })
  @ApiResponse({ status: 200, description: "Returns the current user" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  getMe(@GetUser() user: User): User {
    return user;
  }

  @ApiOperation({ summary: "Update current user" })
  @ApiResponse({ status: 200, description: "User updated successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @Patch("me")
  @ApiBearerAuth("JWT-auth")
  @UseGuards(JwtAuthGuard)
  @ApiResponse({ status: 401, description: "Unauthorized" })
  updateMe(
    @GetUser() user: User,
    @Body() updateUserDto: Partial<User>
  ): Promise<User> {
    return this.usersService.update(user.id, updateUserDto);
  }

  @Get("team")
  @ApiBearerAuth("JWT-auth")
  @ApiOperation({ summary: "Get team members" })
  @ApiResponse({ status: 200, description: "Returns team members" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getTeamMembers(@GetUser() user: User) {
    return this.usersService.getTeamMembers(user.id);
  }

  @Post("invite")
  @ApiBearerAuth("JWT-auth")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: "Invite a team member" })
  @ApiBody({ type: InviteTeamMemberDto })
  @ApiResponse({ status: 201, description: "Team member invited successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 400, description: "Bad request" })
  async inviteTeamMember(
    @GetUser() user: User,
    @Body() inviteDto: InviteTeamMemberDto
  ) {
    if (!inviteDto.email || !inviteDto.role) {
      throw new BadRequestException("Email and role are required");
    }

    return this.usersService.inviteTeamMember(
      user.id,
      inviteDto.email,
      inviteDto.role
    );
  }

  @Get("settings")
  @ApiBearerAuth("JWT-auth")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: "Get user settings" })
  @ApiResponse({ status: 200, description: "Returns user settings" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getUserSettings(@GetUser() user: User): Promise<UserSettings> {
    return this.usersService.getUserSettings(user.id);
  }

  @Patch("settings")
  @ApiBearerAuth("JWT-auth")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: "Update user settings" })
  @ApiBody({ type: UserSettingsDto })
  @ApiResponse({ status: 200, description: "Settings updated successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async updateUserSettings(
    @GetUser() user: User,
    @Body() settingsDto: UserSettingsDto
  ): Promise<UserSettings> {
    return this.usersService.updateUserSettings(user.id, settingsDto);
  }
}
