import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import * as bcrypt from "bcrypt";
import { User } from "./entities/user.entity";
import { TeamMember } from "./entities/team-member.entity";
import { UserSettings } from "./entities/user-settings.entity";
import type { UserCreateDto } from "shared-types";
import { UserSettingsDto } from "./dto/user-settings.dto";

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
    @InjectRepository(UserSettings)
    private userSettingsRepository: Repository<UserSettings>
  ) {}

  async findAll(): Promise<User[]> {
    return this.usersRepository.find();
  }

  async findOne(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async create(userCreateDto: UserCreateDto): Promise<User> {
    const existingUser = await this.findByEmail(userCreateDto.email);
    if (existingUser) {
      throw new ConflictException("Email already exists");
    }

    const hashedPassword = await bcrypt.hash(userCreateDto.password, 10);
    const user = this.usersRepository.create({
      ...userCreateDto,
      password: hashedPassword,
    });

    return this.usersRepository.save(user);
  }

  async update(id: string, updateUserDto: Partial<User>): Promise<User> {
    const user = await this.findOne(id);

    // If updating password, hash it
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    Object.assign(user, updateUserDto);
    return this.usersRepository.save(user);
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.usersRepository.remove(user);
  }

  async getTeamMembers(userId: string) {
    // In a real application, this would fetch actual team members from the database
    // For now, we'll return mock data to match the frontend expectations
    return [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        role: "Admin",
        avatar: "https://ui-avatars.com/api/?name=John+Doe",
        lastActive: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
      },
      {
        id: "2",
        name: "Jane Smith",
        email: "<EMAIL>",
        role: "Editor",
        avatar: "https://ui-avatars.com/api/?name=Jane+Smith",
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      },
      {
        id: "3",
        name: "Mike Johnson",
        email: "<EMAIL>",
        role: "Viewer",
        avatar: "https://ui-avatars.com/api/?name=Mike+Johnson",
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
      },
    ];
  }

  async inviteTeamMember(userId: string, email: string, role: string) {
    // In a real application, this would:
    // 1. Check if the user has permission to invite team members
    // 2. Create a team member record in the database
    // 3. Send an invitation email
    // 4. Return the created team member

    // For now, we'll just return a success message
    return {
      success: true,
      message: `Invitation sent to ${email} with role ${role}`,
    };
  }

  /**
   * Get user settings
   */
  async getUserSettings(userId: string): Promise<UserSettings> {
    // Try to find existing settings
    let settings = await this.userSettingsRepository.findOne({
      where: { userId },
    });

    // If no settings exist, create default settings
    if (!settings) {
      settings = this.userSettingsRepository.create({
        userId,
        emailNotifications: {
          newDomainAnalysis: true,
          emailOpened: true,
          emailClicked: true,
          emailReplied: true,
          weeklyReport: true,
        },
        apiKeys: {
          mozApiKey: "",
          semrushApiKey: "",
          ahrefsApiKey: "",
        },
        emailSettings: {
          fromName: "",
          signature: "",
          replyTo: "",
        },
        appearance: {
          theme: "system",
          compactMode: false,
        },
      });
      settings = await this.userSettingsRepository.save(settings);
    }

    return settings;
  }

  /**
   * Update user settings
   */
  async updateUserSettings(
    userId: string,
    settingsDto: UserSettingsDto
  ): Promise<UserSettings> {
    // Get existing settings or create new ones
    const settings = await this.getUserSettings(userId);

    // Update settings with new values
    Object.assign(settings, settingsDto);

    // Save and return updated settings
    return this.userSettingsRepository.save(settings);
  }
}
