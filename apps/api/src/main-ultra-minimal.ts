import "reflect-metadata";
import { NestFactory } from "@nestjs/core";
import { AppUltraMinimalModule } from "./app-ultra-minimal.module";
import { ValidationPipe } from "@nestjs/common";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

async function bootstrap() {
  try {
    console.log("🚀 Starting RankMesh Backend (Ultra Minimal)...");

    // Set environment defaults
    process.env.NODE_ENV = process.env.NODE_ENV || "development";
    process.env.DB_ENABLED = "false";
    process.env.ENABLE_MOCK_DATA = "true";

    console.log("Environment:", {
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT || 3001,
      DB_ENABLED: process.env.DB_ENABLED,
      MOCK_DATA: process.env.ENABLE_MOCK_DATA,
    });

    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: ["error", "warn", "log"],
    });

    // Enable CORS
    app.enableCors({
      origin: [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",
      ],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );

    // Swagger setup
    const config = new DocumentBuilder()
      .setTitle("RankMesh Backlink Intelligence API (Ultra Minimal)")
      .setDescription(
        "Ultra minimal version of the RankMesh API for development"
      )
      .setVersion("1.0")
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup("api", app, document);

    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log(
      `✅ SUCCESS! RankMesh API is running on: http://localhost:${port}`
    );
    console.log(`❤️ Health Check: http://localhost:${port}/health`);
    console.log(`🏠 Home: http://localhost:${port}/`);
    console.log(`📚 Swagger documentation: http://localhost:${port}/api`);
  } catch (error) {
    console.error("❌ Failed to start the application:", error);
    console.error("Stack:", error.stack);
    process.exit(1);
  }
}

bootstrap();
