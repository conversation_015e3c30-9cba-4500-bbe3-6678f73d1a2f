import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { CacheService } from "../../cache/cache.service";

export interface WhoisData {
  domain: string;
  registrar: string;
  creation_date: string;
  expiration_date: string;
  updated_date: string;
  name_servers: string[];
  status: string[];
  registrant_name?: string;
  registrant_organization?: string;
  registrant_country?: string;
  admin_email?: string;
  tech_email?: string;
  domain_age_days: number;
  domain_age_years: number;
}

export interface DomainAvailabilityData {
  domain: string;
  available: boolean;
  premium: boolean;
  price?: number;
}

@Injectable()
export class WhoisXmlService {
  private readonly logger = new Logger(WhoisXmlService.name);
  private readonly baseUrl =
    "https://www.whoisxmlapi.com/whoisserver/WhoisService";
  private readonly apiKey: string;
  private readonly rateLimit: number;
  private lastRequestTime = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly cacheService: CacheService
  ) {
    this.apiKey = this.configService.get<string>("WHOISXML_API_KEY") || "";
    this.rateLimit = this.configService.get<number>(
      "WHOISXML_RATE_LIMIT",
      1000
    );

    if (!this.apiKey) {
      this.logger.warn("WhoisXML API key not configured");
    }
  }

  /**
   * Get WHOIS data for a domain
   */
  async getWhoisData(domain: string): Promise<WhoisData | null> {
    if (!this.apiKey) {
      this.logger.warn("WhoisXML API key not configured, returning mock data");
      return this.generateMockWhoisData(domain);
    }

    const cacheKey = this.cacheService.generateWhoisKey(domain);

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const response = await firstValueFrom(
            this.httpService.get(this.baseUrl, {
              params: {
                apiKey: this.apiKey,
                domainName: domain,
                outputFormat: "JSON",
                da: 2, // Include domain availability check
              },
              timeout: 10000,
            })
          );

          if (response.data && response.data.WhoisRecord) {
            const whoisRecord = response.data.WhoisRecord;
            const registryData = whoisRecord.registryData || {};
            const registrarData = whoisRecord.registrarData || {};

            // Calculate domain age
            const creationDate = new Date(
              registryData.createdDate ||
                registrarData.createdDate ||
                whoisRecord.createdDate
            );
            const now = new Date();
            const domainAgeDays = Math.floor(
              (now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24)
            );
            const domainAgeYears = Math.floor(domainAgeDays / 365);

            return {
              domain: whoisRecord.domainName,
              registrar: whoisRecord.registrarName || "Unknown",
              creation_date:
                registryData.createdDate ||
                registrarData.createdDate ||
                whoisRecord.createdDate,
              expiration_date:
                registryData.expiresDate ||
                registrarData.expiresDate ||
                whoisRecord.expiresDate,
              updated_date:
                registryData.updatedDate ||
                registrarData.updatedDate ||
                whoisRecord.updatedDate,
              name_servers: whoisRecord.nameServers?.hostNames || [],
              status: registryData.status || registrarData.status || [],
              registrant_name:
                registryData.registrant?.name || registrarData.registrant?.name,
              registrant_organization:
                registryData.registrant?.organization ||
                registrarData.registrant?.organization,
              registrant_country:
                registryData.registrant?.country ||
                registrarData.registrant?.country,
              admin_email:
                registryData.administrativeContact?.email ||
                registrarData.administrativeContact?.email,
              tech_email:
                registryData.technicalContact?.email ||
                registrarData.technicalContact?.email,
              domain_age_days: domainAgeDays,
              domain_age_years: domainAgeYears,
            };
          }

          return null;
        } catch (error) {
          this.logger.error(
            `Failed to fetch WHOIS data for ${domain}: ${error.message}`
          );
          return null;
        }
      },
      86400 // Cache for 24 hours
    );
  }

  /**
   * Check domain availability
   */
  async checkDomainAvailability(
    domain: string
  ): Promise<DomainAvailabilityData | null> {
    if (!this.apiKey) {
      this.logger.warn("WhoisXML API key not configured, returning null");
      return null;
    }

    const cacheKey = `whoisxml_availability:${domain}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const response = await firstValueFrom(
            this.httpService.get(
              "https://domain-availability.whoisxmlapi.com/api/v1",
              {
                params: {
                  apiKey: this.apiKey,
                  domainName: domain,
                },
                timeout: 10000,
              }
            )
          );

          if (response.data && response.data.DomainInfo) {
            const domainInfo = response.data.DomainInfo;

            return {
              domain: domainInfo.domainName,
              available: domainInfo.domainAvailability === "AVAILABLE",
              premium: domainInfo.premium || false,
              price: domainInfo.price || undefined,
            };
          }

          return null;
        } catch (error) {
          this.logger.error(
            `Failed to check domain availability for ${domain}: ${error.message}`
          );
          return null;
        }
      },
      3600 // Cache for 1 hour
    );
  }

  /**
   * Get domain age in years
   */
  async getDomainAge(domain: string): Promise<number> {
    const whoisData = await this.getWhoisData(domain);
    return whoisData?.domain_age_years || 0;
  }

  /**
   * Get domain creation date
   */
  async getDomainCreationDate(domain: string): Promise<Date | null> {
    const whoisData = await this.getWhoisData(domain);
    if (whoisData?.creation_date) {
      return new Date(whoisData.creation_date);
    }
    return null;
  }

  /**
   * Get domain registrar information
   */
  async getDomainRegistrar(domain: string): Promise<string | null> {
    const whoisData = await this.getWhoisData(domain);
    return whoisData?.registrar || null;
  }

  /**
   * Get domain name servers
   */
  async getDomainNameServers(domain: string): Promise<string[]> {
    const whoisData = await this.getWhoisData(domain);
    return whoisData?.name_servers || [];
  }

  /**
   * Check if domain is expired
   */
  async isDomainExpired(domain: string): Promise<boolean> {
    const whoisData = await this.getWhoisData(domain);
    if (whoisData?.expiration_date) {
      const expirationDate = new Date(whoisData.expiration_date);
      return expirationDate < new Date();
    }
    return false;
  }

  /**
   * Get domain contact information
   */
  async getDomainContacts(domain: string): Promise<{
    registrant?: string;
    admin_email?: string;
    tech_email?: string;
    organization?: string;
    country?: string;
  }> {
    const whoisData = await this.getWhoisData(domain);
    if (whoisData) {
      return {
        registrant: whoisData.registrant_name,
        admin_email: whoisData.admin_email,
        tech_email: whoisData.tech_email,
        organization: whoisData.registrant_organization,
        country: whoisData.registrant_country,
      };
    }
    return {};
  }

  /**
   * Respect rate limiting
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = (60 * 1000) / this.rateLimit;

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting: waiting ${delay}ms`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Check if API is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate mock WHOIS data for testing
   */
  private generateMockWhoisData(domain: string): WhoisData {
    const hash = this.hashString(domain);
    const ageYears = Math.floor((hash % 15) + 1); // 1-15 years
    const ageDays = ageYears * 365 + (hash % 365);

    const creationDate = new Date();
    creationDate.setDate(creationDate.getDate() - ageDays);

    const expirationDate = new Date(creationDate);
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);

    const updatedDate = new Date(creationDate);
    updatedDate.setMonth(updatedDate.getMonth() + (hash % 12));

    return {
      domain,
      registrar: this.getMockRegistrar(hash),
      creation_date: creationDate.toISOString(),
      expiration_date: expirationDate.toISOString(),
      updated_date: updatedDate.toISOString(),
      name_servers: [`ns1.${domain}`, `ns2.${domain}`],
      status: ["clientTransferProhibited"],
      registrant_name: "Domain Owner",
      registrant_organization: "Example Organization",
      registrant_country: this.getMockCountry(hash),
      admin_email: `admin@${domain}`,
      tech_email: `tech@${domain}`,
      domain_age_days: ageDays,
      domain_age_years: ageYears,
    };
  }

  /**
   * Get mock registrar based on hash
   */
  private getMockRegistrar(hash: number): string {
    const registrars = [
      "GoDaddy.com, LLC",
      "Namecheap, Inc.",
      "Google Domains LLC",
      "Network Solutions, LLC",
      "Tucows Domains Inc.",
    ];
    return registrars[hash % registrars.length];
  }

  /**
   * Get mock country based on hash
   */
  private getMockCountry(hash: number): string {
    const countries = [
      "United States",
      "United Kingdom",
      "Canada",
      "Germany",
      "France",
      "Australia",
      "Netherlands",
    ];
    return countries[hash % countries.length];
  }

  /**
   * Simple hash function for consistent mock data
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }
}
