import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";

export interface WhoisData {
  domain: string;
  registrar: string;
  creation_date: string;
  expiration_date: string;
  updated_date: string;
  name_servers: string[];
  status: string[];
  registrant_name: string;
  registrant_organization: string;
  registrant_country: string;
  admin_email: string;
  tech_email: string;
  domain_age_days: number;
  domain_age_years: number;
}

@Injectable()
export class WhoisXmlSimpleService {
  private readonly logger = new Logger(WhoisXmlSimpleService.name);
  private readonly baseUrl = "https://www.whoisxmlapi.com/whoisserver/WhoisService";
  private readonly apiKey: string;
  private readonly rateLimit: number;
  private lastRequestTime = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.apiKey = this.configService.get<string>("WHOISXML_API_KEY") || "";
    this.rateLimit = this.configService.get<number>("WHOISXML_RATE_LIMIT", 1000);

    if (!this.apiKey) {
      this.logger.warn("WhoisXML API key not configured - using mock data");
    }
  }

  /**
   * Get WHOIS data for a domain
   */
  async getWhoisData(domain: string): Promise<WhoisData | null> {
    if (!this.apiKey) {
      this.logger.warn("WhoisXML API key not configured, returning mock data");
      return this.generateMockWhoisData(domain);
    }

    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(this.baseUrl, {
          params: {
            apiKey: this.apiKey,
            domainName: domain,
            outputFormat: "JSON",
          },
          timeout: 10000,
        })
      );

      if (response.data && response.data.WhoisRecord) {
        const record = response.data.WhoisRecord;
        const creationDate = new Date(record.createdDate);
        const now = new Date();
        const ageDays = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
        const ageYears = Math.floor(ageDays / 365);

        return {
          domain,
          registrar: record.registrarName || "Unknown",
          creation_date: record.createdDate || "",
          expiration_date: record.expiresDate || "",
          updated_date: record.updatedDate || "",
          name_servers: record.nameServers?.hostNames || [],
          status: record.status || [],
          registrant_name: record.registrant?.name || "",
          registrant_organization: record.registrant?.organization || "",
          registrant_country: record.registrant?.country || "",
          admin_email: record.administrativeContact?.email || "",
          tech_email: record.technicalContact?.email || "",
          domain_age_days: ageDays,
          domain_age_years: ageYears,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Failed to fetch WHOIS data for ${domain}: ${error.message}`
      );
      return this.generateMockWhoisData(domain);
    }
  }

  /**
   * Get domain age in years
   */
  async getDomainAge(domain: string): Promise<number> {
    const whoisData = await this.getWhoisData(domain);
    return whoisData?.domain_age_years || 0;
  }

  /**
   * Respect rate limiting
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = (60 * 1000) / this.rateLimit;

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting: waiting ${delay}ms`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Check if API is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate mock WHOIS data for testing
   */
  private generateMockWhoisData(domain: string): WhoisData {
    const hash = this.hashString(domain);
    const ageYears = Math.floor((hash % 15) + 1);
    const ageDays = ageYears * 365 + (hash % 365);
    
    const creationDate = new Date();
    creationDate.setDate(creationDate.getDate() - ageDays);
    
    const expirationDate = new Date(creationDate);
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);
    
    const updatedDate = new Date(creationDate);
    updatedDate.setMonth(updatedDate.getMonth() + (hash % 12));

    return {
      domain,
      registrar: this.getMockRegistrar(hash),
      creation_date: creationDate.toISOString(),
      expiration_date: expirationDate.toISOString(),
      updated_date: updatedDate.toISOString(),
      name_servers: [`ns1.${domain}`, `ns2.${domain}`],
      status: ["clientTransferProhibited"],
      registrant_name: "Domain Owner",
      registrant_organization: "Example Organization",
      registrant_country: this.getMockCountry(hash),
      admin_email: `admin@${domain}`,
      tech_email: `tech@${domain}`,
      domain_age_days: ageDays,
      domain_age_years: ageYears,
    };
  }

  /**
   * Get mock registrar based on hash
   */
  private getMockRegistrar(hash: number): string {
    const registrars = [
      "GoDaddy.com, LLC",
      "Namecheap, Inc.",
      "Google Domains LLC",
      "Network Solutions, LLC",
      "Tucows Domains Inc.",
    ];
    return registrars[hash % registrars.length];
  }

  /**
   * Get mock country based on hash
   */
  private getMockCountry(hash: number): string {
    const countries = [
      "United States",
      "United Kingdom",
      "Canada",
      "Germany",
      "France",
      "Australia",
      "Netherlands",
    ];
    return countries[hash % countries.length];
  }

  /**
   * Simple hash function for consistent mock data
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }
}
