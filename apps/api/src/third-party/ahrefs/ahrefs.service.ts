import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";

export interface AhrefsDomainRating {
  domain: string;
  domain_rating: number;
  ahrefs_rank: number;
  backlinks: number;
  referring_domains: number;
  organic_keywords: number;
  organic_traffic: number;
}

export interface AhrefsBacklinkData {
  url_from: string;
  url_to: string;
  ahrefs_rank: number;
  domain_rating: number;
  url_rating: number;
  anchor: string;
  first_seen: string;
  last_seen: string;
  link_type: string;
  nofollow: boolean;
}

export interface AhrefsKeywordData {
  keyword: string;
  position: number;
  search_volume: number;
  keyword_difficulty: number;
  cpc: number;
  url: string;
  traffic: number;
}

@Injectable()
export class AhrefsService {
  private readonly logger = new Logger(AhrefsService.name);
  private readonly baseUrl = "https://apiv2.ahrefs.com";
  private readonly apiKey: string;
  private readonly rateLimit: number;
  private lastRequestTime = 0;

  constructor(
    
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.apiKey = this.configService.get<string>("AHREFS_API_KEY")!;//TODO: work on it
    this.rateLimit = this.configService.get<number>("AHREFS_RATE_LIMIT", 100);

    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured - using mock data");
    }
  }

  /**
   * Get domain rating and basic metrics for a domain
   */
  async getDomainRating(domain: string): Promise<AhrefsDomainRating | null> {
    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured, returning mock data");
      return this.generateMockDomainRating(domain);
    }

    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/domain-rating`, {
          params: {
            target: domain,
            token: this.apiKey,
          },
          timeout: 10000,
        })
      );

      if (response.data && response.data.domain) {
        const data = response.data.domain[0];
        return {
          domain: data.target,
          domain_rating: data.domain_rating || 0,
          ahrefs_rank: data.ahrefs_rank || 0,
          backlinks: data.backlinks || 0,
          referring_domains: data.referring_domains || 0,
          organic_keywords: data.organic_keywords || 0,
          organic_traffic: data.organic_traffic || 0,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Failed to fetch domain rating for ${domain}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Get backlink data for a domain
   */
  async getBacklinks(
    domain: string,
    limit: number = 100
  ): Promise<AhrefsBacklinkData[]> {
    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured, returning empty array");
      return [];
    }

    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/backlinks`, {
          params: {
            target: domain,
            token: this.apiKey,
            limit,
            mode: "domain",
          },
          timeout: 15000,
        })
      );

      if (response.data && response.data.backlinks) {
        return response.data.backlinks.map((backlink: any) => ({
          url_from: backlink.url_from,
          url_to: backlink.url_to,
          ahrefs_rank: backlink.ahrefs_rank || 0,
          domain_rating: backlink.domain_rating || 0,
          url_rating: backlink.url_rating || 0,
          anchor: backlink.anchor || "",
          first_seen: backlink.first_seen,
          last_seen: backlink.last_seen,
          link_type: backlink.link_type || "unknown",
          nofollow: backlink.nofollow || false,
        }));
      }

      return [];
    } catch (error) {
      this.logger.error(
        `Failed to fetch backlinks for ${domain}: ${error.message}`
      );
      return [];
    }
  }

  /**
   * Get organic keywords for a domain
   */
  async getOrganicKeywords(
    domain: string,
    limit: number = 100
  ): Promise<AhrefsKeywordData[]> {
    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured, returning empty array");
      return [];
    }

    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/organic-keywords`, {
          params: {
            target: domain,
            token: this.apiKey,
            limit,
            mode: "domain",
          },
          timeout: 15000,
        })
      );

      if (response.data && response.data.keywords) {
        return response.data.keywords.map((keyword: any) => ({
          keyword: keyword.keyword,
          position: keyword.position || 0,
          search_volume: keyword.search_volume || 0,
          keyword_difficulty: keyword.keyword_difficulty || 0,
          cpc: keyword.cpc || 0,
          url: keyword.url || "",
          traffic: keyword.traffic || 0,
        }));
      }

      return [];
    } catch (error) {
      this.logger.error(
        `Failed to fetch organic keywords for ${domain}: ${error.message}`
      );
      return [];
    }
  }

  /**
   * Respect rate limiting
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = (60 * 1000) / this.rateLimit; // Convert rate limit to milliseconds

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting: waiting ${delay}ms`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Check if API is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate mock domain rating data for testing
   */
  private generateMockDomainRating(domain: string): AhrefsDomainRating {
    // Generate consistent mock data based on domain name
    const hash = this.hashString(domain);
    const domainRating = Math.floor((hash % 80) + 20); // 20-99
    const ahrefsRank = Math.floor((hash % 100000) + 1000);
    const backlinks = Math.floor((hash % 50000) + 100);
    const referringDomains = Math.floor(backlinks / 10);
    const organicKeywords = Math.floor((hash % 5000) + 100);
    const organicTraffic = Math.floor((hash % 10000) + 50);

    return {
      domain,
      domain_rating: domainRating,
      ahrefs_rank: ahrefsRank,
      backlinks,
      referring_domains: referringDomains,
      organic_keywords: organicKeywords,
      organic_traffic: organicTraffic,
    };
  }

  /**
   * Simple hash function for consistent mock data
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
