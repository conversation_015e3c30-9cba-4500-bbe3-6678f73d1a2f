import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";

export interface AhrefsDomainRating {
  domain: string;
  domain_rating: number;
  ahrefs_rank: number;
  backlinks: number;
  referring_domains: number;
  organic_keywords: number;
  organic_traffic: number;
}

@Injectable()
export class AhrefsSimpleService {
  private readonly logger = new Logger(AhrefsSimpleService.name);
  private readonly baseUrl = "https://apiv2.ahrefs.com";
  private readonly apiKey: string;
  private readonly rateLimit: number;
  private lastRequestTime = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.apiKey = this.configService.get<string>("AHREFS_API_KEY") || "";
    this.rateLimit = this.configService.get<number>("AHREFS_RATE_LIMIT", 100);

    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured - using mock data");
    }
  }

  /**
   * Get domain rating and basic metrics for a domain
   */
  async getDomainRating(domain: string): Promise<AhrefsDomainRating | null> {
    if (!this.apiKey) {
      this.logger.warn("Ahrefs API key not configured, returning mock data");
      return this.generateMockDomainRating(domain);
    }

    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/domain-rating`, {
          params: {
            target: domain,
            token: this.apiKey,
          },
          timeout: 10000,
        })
      );

      if (response.data && response.data.domain) {
        const data = response.data.domain[0];
        return {
          domain: data.target,
          domain_rating: data.domain_rating || 0,
          ahrefs_rank: data.ahrefs_rank || 0,
          backlinks: data.backlinks || 0,
          referring_domains: data.referring_domains || 0,
          organic_keywords: data.organic_keywords || 0,
          organic_traffic: data.organic_traffic || 0,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Failed to fetch domain rating for ${domain}: ${error.message}`
      );
      return this.generateMockDomainRating(domain);
    }
  }

  /**
   * Respect rate limiting
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = (60 * 1000) / this.rateLimit;

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting: waiting ${delay}ms`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Check if API is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate mock domain rating data for testing
   */
  private generateMockDomainRating(domain: string): AhrefsDomainRating {
    const hash = this.hashString(domain);
    const domainRating = Math.floor((hash % 80) + 20); // 20-99
    const ahrefsRank = Math.floor((hash % 100000) + 1000);
    const backlinks = Math.floor((hash % 50000) + 100);
    const referringDomains = Math.floor(backlinks / 10);
    const organicKeywords = Math.floor((hash % 5000) + 100);
    const organicTraffic = Math.floor((hash % 10000) + 50);

    return {
      domain,
      domain_rating: domainRating,
      ahrefs_rank: ahrefsRank,
      backlinks,
      referring_domains: referringDomains,
      organic_keywords: organicKeywords,
      organic_traffic: organicTraffic,
    };
  }

  /**
   * Simple hash function for consistent mock data
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }
}
