import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { AhrefsService } from './ahrefs.service';
import { CacheService } from '../../cache/cache.service';

describe('AhrefsService', () => {
  let service: AhrefsService;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;
  let cacheService: jest.Mocked<CacheService>;

  const mockHttpService = {
    get: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockCacheService = {
    getOrSet: jest.fn(),
    generateSeoMetricsKey: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AhrefsService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<AhrefsService>(AhrefsService);
    httpService = module.get(HttpService);
    configService = module.get(ConfigService);
    cacheService = module.get(CacheService);

    // Setup default config values
    mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
      const config = {
        AHREFS_API_KEY: 'test-api-key',
        AHREFS_RATE_LIMIT: 100,
      };
      return config[key] || defaultValue;
    });

    mockCacheService.generateSeoMetricsKey.mockReturnValue('ahrefs:example.com');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDomainRating', () => {
    const mockDomainRatingResponse = {
      data: {
        domain: [
          {
            target: 'example.com',
            domain_rating: 75,
            ahrefs_rank: 1000,
            backlinks: 50000,
            referring_domains: 1000,
            organic_keywords: 5000,
            organic_traffic: 10000,
          },
        ],
      },
    };

    it('should return domain rating data when API call succeeds', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(of(mockDomainRatingResponse));

      const result = await service.getDomainRating('example.com');

      expect(result).toEqual({
        domain: 'example.com',
        domain_rating: 75,
        ahrefs_rank: 1000,
        backlinks: 50000,
        referring_domains: 1000,
        organic_keywords: 5000,
        organic_traffic: 10000,
      });

      expect(mockHttpService.get).toHaveBeenCalledWith(
        'https://apiv2.ahrefs.com/domain-rating',
        {
          params: {
            target: 'example.com',
            token: 'test-api-key',
          },
          timeout: 10000,
        },
      );
    });

    it('should return null when API key is not configured', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'AHREFS_API_KEY') return undefined;
        return 100; // default rate limit
      });

      // Create new service instance with no API key
      const moduleWithoutKey = await Test.createTestingModule({
        providers: [
          AhrefsService,
          {
            provide: HttpService,
            useValue: mockHttpService,
          },
          {
            provide: ConfigService,
            useValue: mockConfigService,
          },
          {
            provide: CacheService,
            useValue: mockCacheService,
          },
        ],
      }).compile();

      const serviceWithoutKey = moduleWithoutKey.get<AhrefsService>(AhrefsService);
      const result = await serviceWithoutKey.getDomainRating('example.com');

      expect(result).toBeNull();
      expect(mockHttpService.get).not.toHaveBeenCalled();
    });

    it('should return null when API call fails', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getDomainRating('example.com');

      expect(result).toBeNull();
    });

    it('should return null when API response is invalid', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(of({ data: {} }));

      const result = await service.getDomainRating('example.com');

      expect(result).toBeNull();
    });

    it('should use cache when available', async () => {
      const cachedData = {
        domain: 'example.com',
        domain_rating: 80,
        ahrefs_rank: 500,
        backlinks: 75000,
        referring_domains: 1500,
        organic_keywords: 7500,
        organic_traffic: 15000,
      };

      mockCacheService.getOrSet.mockResolvedValue(cachedData);

      const result = await service.getDomainRating('example.com');

      expect(result).toEqual(cachedData);
      expect(mockCacheService.getOrSet).toHaveBeenCalledWith(
        'ahrefs:example.com',
        expect.any(Function),
        3600,
      );
    });
  });

  describe('getBacklinks', () => {
    const mockBacklinksResponse = {
      data: {
        backlinks: [
          {
            url_from: 'https://source.com/page',
            url_to: 'https://example.com/',
            ahrefs_rank: 1000,
            domain_rating: 70,
            url_rating: 50,
            anchor: 'example link',
            first_seen: '2023-01-01',
            last_seen: '2023-12-01',
            link_type: 'text',
            nofollow: false,
          },
        ],
      },
    };

    it('should return backlinks data when API call succeeds', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(of(mockBacklinksResponse));

      const result = await service.getBacklinks('example.com', 50);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        url_from: 'https://source.com/page',
        url_to: 'https://example.com/',
        ahrefs_rank: 1000,
        domain_rating: 70,
        url_rating: 50,
        anchor: 'example link',
        first_seen: '2023-01-01',
        last_seen: '2023-12-01',
        link_type: 'text',
        nofollow: false,
      });

      expect(mockHttpService.get).toHaveBeenCalledWith(
        'https://apiv2.ahrefs.com/backlinks',
        {
          params: {
            target: 'example.com',
            token: 'test-api-key',
            limit: 50,
            mode: 'domain',
          },
          timeout: 15000,
        },
      );
    });

    it('should return empty array when API key is not configured', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'AHREFS_API_KEY') return undefined;
        return 100;
      });

      const moduleWithoutKey = await Test.createTestingModule({
        providers: [
          AhrefsService,
          {
            provide: HttpService,
            useValue: mockHttpService,
          },
          {
            provide: ConfigService,
            useValue: mockConfigService,
          },
          {
            provide: CacheService,
            useValue: mockCacheService,
          },
        ],
      }).compile();

      const serviceWithoutKey = moduleWithoutKey.get<AhrefsService>(AhrefsService);
      const result = await serviceWithoutKey.getBacklinks('example.com');

      expect(result).toEqual([]);
      expect(mockHttpService.get).not.toHaveBeenCalled();
    });

    it('should return empty array when API call fails', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getBacklinks('example.com');

      expect(result).toEqual([]);
    });
  });

  describe('getOrganicKeywords', () => {
    const mockKeywordsResponse = {
      data: {
        keywords: [
          {
            keyword: 'example keyword',
            position: 5,
            search_volume: 1000,
            keyword_difficulty: 45,
            cpc: 2.5,
            url: 'https://example.com/page',
            traffic: 100,
          },
        ],
      },
    };

    it('should return organic keywords data when API call succeeds', async () => {
      mockCacheService.getOrSet.mockImplementation(async (key, factory) => {
        return await factory();
      });
      mockHttpService.get.mockReturnValue(of(mockKeywordsResponse));

      const result = await service.getOrganicKeywords('example.com', 25);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        keyword: 'example keyword',
        position: 5,
        search_volume: 1000,
        keyword_difficulty: 45,
        cpc: 2.5,
        url: 'https://example.com/page',
        traffic: 100,
      });

      expect(mockHttpService.get).toHaveBeenCalledWith(
        'https://apiv2.ahrefs.com/organic-keywords',
        {
          params: {
            target: 'example.com',
            token: 'test-api-key',
            limit: 25,
            mode: 'domain',
          },
          timeout: 15000,
        },
      );
    });
  });

  describe('isConfigured', () => {
    it('should return true when API key is configured', () => {
      expect(service.isConfigured()).toBe(true);
    });

    it('should return false when API key is not configured', () => {
      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'AHREFS_API_KEY') return undefined;
        return 100;
      });

      const moduleWithoutKey = Test.createTestingModule({
        providers: [
          AhrefsService,
          {
            provide: HttpService,
            useValue: mockHttpService,
          },
          {
            provide: ConfigService,
            useValue: mockConfigService,
          },
          {
            provide: CacheService,
            useValue: mockCacheService,
          },
        ],
      }).compile();

      const serviceWithoutKey = moduleWithoutKey.get<AhrefsService>(AhrefsService);
      expect(serviceWithoutKey.isConfigured()).toBe(false);
    });
  });
});
