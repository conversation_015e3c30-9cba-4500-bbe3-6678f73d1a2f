import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { CacheService } from "src/cache/cache.service";

export interface SimilarWebTrafficData {
  domain: string;
  visits: number;
  unique_visitors: number;
  page_views: number;
  bounce_rate: number;
  pages_per_visit: number;
  avg_visit_duration: number;
  global_rank: number;
  country_rank: number;
  category_rank: number;
}

export interface SimilarWebTrafficTrend {
  date: string;
  visits: number;
  change_percentage: number;
}

export interface SimilarWebGeoData {
  country: string;
  country_code: string;
  traffic_share: number;
}

export interface SimilarWebCategoryData {
  category: string;
  score: number;
}

@Injectable()
export class SimilarWebService {
  private readonly logger = new Logger(SimilarWebService.name);
  private readonly baseUrl = "https://api.similarweb.com/v1";
  private readonly apiKey: string;
  private readonly rateLimit: number;
  private lastRequestTime = 0;

  constructor(
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(HttpService)
    private readonly httpService: HttpService,
    @Inject(CacheService)
    private readonly cacheService: CacheService
  ) {
    this.apiKey = this.configService.get<string>("SIMILARWEB_API_KEY") || "";
    this.rateLimit = this.configService.get<number>(
      "SIMILARWEB_RATE_LIMIT",
      1000
    );

    if (!this.apiKey) {
      this.logger.warn("SimilarWeb API key not configured");
    }
  }

  /**
   * Get traffic data for a domain
   */
  async getTrafficData(domain: string): Promise<SimilarWebTrafficData | null> {
    if (!this.apiKey) {
      this.logger.warn(
        "SimilarWeb API key not configured, returning mock data"
      );
      return this.generateMockTrafficData(domain);
    }

    const cacheKey = this.cacheService.generateSeoMetricsKey(
      domain,
      "similarweb"
    );

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const response = await firstValueFrom(
            this.httpService.get(
              `${this.baseUrl}/website/${domain}/total-traffic-and-engagement/visits`,
              {
                params: {
                  api_key: this.apiKey,
                  start_date: this.getLastMonthDate(),
                  end_date: this.getCurrentDate(),
                  main_domain_only: false,
                  granularity: "monthly",
                },
                timeout: 10000,
              }
            )
          );

          if (response.data && response.data.visits) {
            const latestData =
              response.data.visits[response.data.visits.length - 1];

            // Get additional metrics
            const [engagementData, rankingData] = await Promise.all([
              this.getEngagementMetrics(domain),
              this.getRankingData(domain),
            ]);

            return {
              domain,
              visits: latestData.visits || 0,
              unique_visitors: engagementData?.unique_visitors || 0,
              page_views: engagementData?.page_views || 0,
              bounce_rate: engagementData?.bounce_rate || 0,
              pages_per_visit: engagementData?.pages_per_visit || 0,
              avg_visit_duration: engagementData?.avg_visit_duration || 0,
              global_rank: rankingData?.global_rank || 0,
              country_rank: rankingData?.country_rank || 0,
              category_rank: rankingData?.category_rank || 0,
            };
          }

          return null;
        } catch (error) {
          this.logger.error(
            `Failed to fetch traffic data for ${domain}: ${error.message}`
          );
          return null;
        }
      },
      3600 // Cache for 1 hour
    );
  }

  /**
   * Get traffic trends for a domain
   */
  async getTrafficTrends(
    domain: string,
    months: number = 6
  ): Promise<SimilarWebTrafficTrend[]> {
    if (!this.apiKey) {
      this.logger.warn(
        "SimilarWeb API key not configured, returning empty array"
      );
      return [];
    }

    const cacheKey = `similarweb_trends:${domain}:${months}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const startDate = this.getDateMonthsAgo(months);
          const response = await firstValueFrom(
            this.httpService.get(
              `${this.baseUrl}/website/${domain}/total-traffic-and-engagement/visits`,
              {
                params: {
                  api_key: this.apiKey,
                  start_date: startDate,
                  end_date: this.getCurrentDate(),
                  main_domain_only: false,
                  granularity: "monthly",
                },
                timeout: 10000,
              }
            )
          );

          if (response.data && response.data.visits) {
            return response.data.visits.map(
              (item: any, index: number, array: any[]) => {
                const previousVisits =
                  index > 0 ? array[index - 1].visits : item.visits;
                const changePercentage =
                  previousVisits > 0
                    ? ((item.visits - previousVisits) / previousVisits) * 100
                    : 0;

                return {
                  date: item.date,
                  visits: item.visits || 0,
                  change_percentage: Math.round(changePercentage * 100) / 100,
                };
              }
            );
          }

          return [];
        } catch (error) {
          this.logger.error(
            `Failed to fetch traffic trends for ${domain}: ${error.message}`
          );
          return [];
        }
      },
      7200 // Cache for 2 hours
    );
  }

  /**
   * Get geographic distribution of traffic
   */
  async getGeoDistribution(domain: string): Promise<SimilarWebGeoData[]> {
    if (!this.apiKey) {
      this.logger.warn(
        "SimilarWeb API key not configured, returning empty array"
      );
      return [];
    }

    const cacheKey = `similarweb_geo:${domain}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const response = await firstValueFrom(
            this.httpService.get(
              `${this.baseUrl}/website/${domain}/geo/traffic-shares`,
              {
                params: {
                  api_key: this.apiKey,
                  start_date: this.getLastMonthDate(),
                  end_date: this.getCurrentDate(),
                  main_domain_only: false,
                },
                timeout: 10000,
              }
            )
          );

          if (response.data && response.data.records) {
            return response.data.records.map((record: any) => ({
              country: record.country,
              country_code: record.country_code,
              traffic_share: record.traffic_share || 0,
            }));
          }

          return [];
        } catch (error) {
          this.logger.error(
            `Failed to fetch geo distribution for ${domain}: ${error.message}`
          );
          return [];
        }
      },
      3600 // Cache for 1 hour
    );
  }

  /**
   * Get website category information
   */
  async getCategoryData(
    domain: string
  ): Promise<SimilarWebCategoryData | null> {
    if (!this.apiKey) {
      this.logger.warn("SimilarWeb API key not configured, returning null");
      return null;
    }

    const cacheKey = `similarweb_category:${domain}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          await this.respectRateLimit();

          const response = await firstValueFrom(
            this.httpService.get(`${this.baseUrl}/website/${domain}/category`, {
              params: {
                api_key: this.apiKey,
              },
              timeout: 10000,
            })
          );

          if (response.data && response.data.category) {
            return {
              category: response.data.category,
              score: response.data.score || 0,
            };
          }

          return null;
        } catch (error) {
          this.logger.error(
            `Failed to fetch category data for ${domain}: ${error.message}`
          );
          return null;
        }
      },
      86400 // Cache for 24 hours
    );
  }

  /**
   * Get engagement metrics (private method)
   */
  private async getEngagementMetrics(domain: string): Promise<any> {
    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/website/${domain}/total-traffic-and-engagement/engagement`,
          {
            params: {
              api_key: this.apiKey,
              start_date: this.getLastMonthDate(),
              end_date: this.getCurrentDate(),
              main_domain_only: false,
            },
            timeout: 10000,
          }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.debug(
        `Failed to fetch engagement metrics for ${domain}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Get ranking data (private method)
   */
  private async getRankingData(domain: string): Promise<any> {
    try {
      await this.respectRateLimit();

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/website/${domain}/global-rank/global-rank`,
          {
            params: {
              api_key: this.apiKey,
              start_date: this.getLastMonthDate(),
              end_date: this.getCurrentDate(),
              main_domain_only: false,
            },
            timeout: 10000,
          }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.debug(
        `Failed to fetch ranking data for ${domain}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Respect rate limiting
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = (60 * 1000) / this.rateLimit;

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting: waiting ${delay}ms`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Utility methods for date formatting
   */
  private getCurrentDate(): string {
    return new Date().toISOString().split("T")[0];
  }

  private getLastMonthDate(): string {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toISOString().split("T")[0];
  }

  private getDateMonthsAgo(months: number): string {
    const date = new Date();
    date.setMonth(date.getMonth() - months);
    return date.toISOString().split("T")[0];
  }

  /**
   * Check if API is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate mock traffic data for testing
   */
  private generateMockTrafficData(domain: string): SimilarWebTrafficData {
    const hash = this.hashString(domain);
    const visits = Math.floor((hash % 500000) + 1000); // 1K-500K visits
    const uniqueVisitors = Math.floor(visits * 0.7);
    const pageViews = Math.floor(visits * 2.5);
    const bounceRate = ((hash % 40) + 30) / 100; // 30-70%
    const pagesPerVisit = ((hash % 30) + 15) / 10; // 1.5-4.5
    const avgVisitDuration = (hash % 300) + 60; // 60-360 seconds
    const globalRank = Math.floor((hash % 1000000) + 1000);
    const countryRank = Math.floor(globalRank / 10);
    const categoryRank = Math.floor(globalRank / 100);

    return {
      domain,
      visits,
      unique_visitors: uniqueVisitors,
      page_views: pageViews,
      bounce_rate: bounceRate,
      pages_per_visit: pagesPerVisit,
      avg_visit_duration: avgVisitDuration,
      global_rank: globalRank,
      country_rank: countryRank,
      category_rank: categoryRank,
    };
  }

  /**
   * Simple hash function for consistent mock data
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }
}
