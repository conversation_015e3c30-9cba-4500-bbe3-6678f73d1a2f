import { Module, forwardRef } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";
import { ConfigModule } from "@nestjs/config";
import { AhrefsService } from "./ahrefs/ahrefs.service";
import { SimilarWebService } from "./similarweb/similarweb.service";
import { WhoisXmlService } from "./whoisxml/whoisxml.service";
import { CacheModule } from "../cache/cache.module";

@Module({
  imports: [
    ConfigModule,
    HttpModule.register({
      timeout: 15000,
      maxRedirects: 3,
    }),
    forwardRef(() => CacheModule),
  ],
  providers: [AhrefsService, SimilarWebService, WhoisXmlService],
  exports: [AhrefsService, SimilarWebService, WhoisXmlService],
})
export class ThirdPartyModule {}
