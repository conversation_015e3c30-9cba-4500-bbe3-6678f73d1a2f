import { Modu<PERSON> } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";
import { ConfigModule } from "@nestjs/config";
import { AhrefsSimpleService } from "./ahrefs/ahrefs-simple.service";
import { SimilarWebSimpleService } from "./similarweb/similarweb-simple.service";
import { WhoisXmlSimpleService } from "./whoisxml/whoisxml-simple.service";

@Module({
  imports: [
    HttpModule.register({
      timeout: 15000,
      maxRedirects: 3,
    }),
    ConfigModule,
  ],
  providers: [
    AhrefsSimpleService,
    SimilarWebSimpleService,
    WhoisXmlSimpleService,
  ],
  exports: [
    AhrefsSimpleService,
    SimilarWebSimpleService,
    WhoisXmlSimpleService,
  ],
})
export class ThirdPartySimpleModule {}
