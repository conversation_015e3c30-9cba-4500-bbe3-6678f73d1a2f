import { Modu<PERSON> } from "@nestjs/common";
import { TerminusModule } from "@nestjs/terminus";
import { HealthController } from "./health.controller";
import { HealthService } from "./health.service";
// import { QueueModule } from "../queue/queue.module"; // Temporarily disabled
import { CacheModule } from "../cache/cache.module";

@Module({
  imports: [TerminusModule, /* QueueModule, */ CacheModule], // QueueModule temporarily disabled
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
