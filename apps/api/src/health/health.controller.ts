import { Controller, Get } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from "@nestjs/terminus";
import { HealthService } from "./health.service";

@ApiTags("Health")
@Controller("health")
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly healthService: HealthService
  ) {}

  @Get()
  @ApiOperation({ summary: "Get application health status" })
  @ApiResponse({ 
    status: 200, 
    description: "Health check successful",
    schema: {
      type: "object",
      properties: {
        status: { type: "string", enum: ["ok", "error", "shutting_down"] },
        info: { type: "object" },
        error: { type: "object" },
        details: { type: "object" }
      }
    }
  })
  @ApiResponse({ status: 503, description: "Health check failed" })
  @HealthCheck()
  check() {
    return this.health.check([
      // Database health check
      () => this.db.pingCheck("database"),
      
      // Memory health check (heap should not use more than 300MB)
      () => this.memory.checkHeap("memory_heap", 300 * 1024 * 1024),
      
      // Memory health check (RSS should not use more than 300MB)
      () => this.memory.checkRSS("memory_rss", 300 * 1024 * 1024),
      
      // Disk health check (should have at least 250MB free)
      () => this.disk.checkStorage("storage", { 
        path: "/", 
        thresholdPercent: 0.9 
      }),
    ]);
  }

  @Get("detailed")
  @ApiOperation({ summary: "Get detailed health status including external services" })
  @ApiResponse({ 
    status: 200, 
    description: "Detailed health check successful",
    schema: {
      type: "object",
      properties: {
        status: { type: "string" },
        timestamp: { type: "string" },
        uptime: { type: "number" },
        version: { type: "string" },
        environment: { type: "string" },
        services: { type: "object" },
        system: { type: "object" },
        dependencies: { type: "object" }
      }
    }
  })
  async getDetailedHealth() {
    return this.healthService.getDetailedHealthStatus();
  }

  @Get("ready")
  @ApiOperation({ summary: "Check if application is ready to serve requests" })
  @ApiResponse({ status: 200, description: "Application is ready" })
  @ApiResponse({ status: 503, description: "Application is not ready" })
  async readiness() {
    return this.healthService.checkReadiness();
  }

  @Get("live")
  @ApiOperation({ summary: "Check if application is alive" })
  @ApiResponse({ status: 200, description: "Application is alive" })
  @ApiResponse({ status: 503, description: "Application is not alive" })
  async liveness() {
    return this.healthService.checkLiveness();
  }

  @Get("metrics")
  @ApiOperation({ summary: "Get application metrics" })
  @ApiResponse({ 
    status: 200, 
    description: "Application metrics retrieved successfully",
    schema: {
      type: "object",
      properties: {
        requests: { type: "object" },
        performance: { type: "object" },
        errors: { type: "object" },
        queues: { type: "object" },
        cache: { type: "object" }
      }
    }
  })
  async getMetrics() {
    return this.healthService.getApplicationMetrics();
  }
}
