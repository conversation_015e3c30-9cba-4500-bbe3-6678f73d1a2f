import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>, Controller, Get, Injectable } from '@nestjs/common';

@Injectable()
class TestService {
  getHello(): string {
    return 'Hello from test service!';
  }
}

@Controller()
class TestController {
  constructor(private readonly testService: TestService) {}

  @Get()
  getHello(): string {
    return this.testService.getHello();
  }

  @Get('health')
  getHealth() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }
}

@Module({
  controllers: [TestController],
  providers: [TestService],
})
class TestModule {}

async function bootstrap() {
  try {
    console.log('Creating NestJS application...');
    const app = await NestFactory.create(TestModule);
    
    console.log('Enabling CORS...');
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    });

    const port = process.env.PORT || 3001;
    console.log(`Starting server on port ${port}...`);
    await app.listen(port);
    
    console.log(`🚀 Test server is running on: http://localhost:${port}`);
    console.log(`✅ Health check: http://localhost:${port}/health`);
  } catch (error) {
    console.error('❌ Failed to start the application:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

bootstrap();
