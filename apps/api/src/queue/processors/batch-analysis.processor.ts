import {
  Processor,
  Process,
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
  InjectQueue,
} from "@nestjs/bull";
import { Injectable, Logger, Inject } from "@nestjs/common";
import { Job, Queue } from "bull";
import { BATCH_ANALYSIS_QUEUE, DOMAIN_ANALYSIS_QUEUE } from "../queue.module";
import { DomainAnalysisJobData, BatchAnalysisJobData } from "../queue.service";
import { CacheService } from "../../cache/cache.service";

export interface BatchAnalysisResult {
  batchId: string;
  userId: string;
  totalDomains: number;
  processedDomains: number;
  failedDomains: number;
  results: {
    targetDomainId: string;
    status: "completed" | "failed" | "pending";
    score?: number;
    error?: string;
    jobId?: string;
  }[];
  startedAt: Date;
  completedAt?: Date;
  processingTimeMs?: number;
  averageScore?: number;
  topDomains?: {
    targetDomainId: string;
    score: number;
  }[];
}

@Injectable()
@Processor(BATCH_ANALYSIS_QUEUE)
export class BatchAnalysisProcessor {
  private readonly logger = new Logger(BatchAnalysisProcessor.name);

  constructor(
    @InjectQueue(DOMAIN_ANALYSIS_QUEUE)
    private readonly domainAnalysisQueue: Queue<DomainAnalysisJobData>,
    @Inject(CacheService)
    private readonly cacheService: CacheService
  ) {}

  @Process("analyze-batch")
  async handleBatchAnalysis(
    job: Job<BatchAnalysisJobData>
  ): Promise<BatchAnalysisResult> {
    const startTime = Date.now();
    const { targetDomainIds, userId, batchId, userDomainId, options } =
      job.data;

    this.logger.log(
      `Processing batch analysis job ${job.id} for batch ${batchId} with ${targetDomainIds.length} domains`
    );

    try {
      // Initialize result
      const result: BatchAnalysisResult = {
        batchId,
        userId,
        totalDomains: targetDomainIds.length,
        processedDomains: 0,
        failedDomains: 0,
        results: targetDomainIds.map((domainId) => ({
          targetDomainId: domainId,
          status: "pending" as const,
        })),
        startedAt: new Date(),
      };

      await job.progress(5);

      // Determine concurrency limit
      const maxConcurrency =
        options?.maxConcurrency || Math.min(5, targetDomainIds.length);

      // Process domains in batches
      const batches = this.chunkArray(targetDomainIds, maxConcurrency);
      let processedCount = 0;

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        this.logger.log(
          `Processing batch ${batchIndex + 1}/${batches.length} with ${
            batch.length
          } domains`
        );

        // Create jobs for this batch
        const batchJobs = await Promise.allSettled(
          batch.map(async (targetDomainId) => {
            const domainJobData: DomainAnalysisJobData = {
              targetDomainId,
              userId,
              userDomainId,
              options: {
                includeContactInfo: options?.includeContactInfo,
                includeKeywords: options?.includeKeywords,
                includeTrafficData: options?.includeTrafficData,
              },
            };

            // Add individual domain analysis job
            const domainJob = await this.domainAnalysisQueue.add(
              "analyze-domain",
              domainJobData,
              {
                priority: 1, // Lower priority than individual requests
                attempts: 2, // Fewer retries for batch jobs
                backoff: {
                  type: "exponential",
                  delay: 2000,
                },
              }
            );

            return {
              targetDomainId,
              jobId: domainJob.id.toString(),
            };
          })
        );

        // Update results with job IDs
        batchJobs.forEach((jobResult, index) => {
          const targetDomainId = batch[index];
          const resultIndex = result.results.findIndex(
            (r) => r.targetDomainId === targetDomainId
          );

          if (jobResult.status === "fulfilled") {
            result.results[resultIndex].jobId = jobResult.value.jobId;
          } else {
            result.results[resultIndex].status = "failed";
            result.results[resultIndex].error =
              jobResult.reason?.message || "Failed to create job";
            result.failedDomains++;
          }
        });

        // Wait for batch jobs to complete (with timeout)
        const batchJobIds = batchJobs
          .filter((job) => job.status === "fulfilled")
          .map((job) => (job as any).value.jobId);

        await this.waitForBatchCompletion(batchJobIds, result, 300000); // 5 minute timeout per batch

        processedCount += batch.length;
        const progress = Math.min(
          95,
          (processedCount / targetDomainIds.length) * 90 + 5
        );
        await job.progress(progress);

        // Add delay between batches to avoid overwhelming the system
        if (batchIndex < batches.length - 1) {
          await this.delay(2000); // 2 second delay between batches
        }
      }

      // Calculate final statistics
      const completedResults = result.results.filter(
        (r) => r.status === "completed" && r.score !== undefined
      );

      if (completedResults.length > 0) {
        result.averageScore =
          completedResults.reduce((sum, r) => sum + (r.score || 0), 0) /
          completedResults.length;

        // Get top 10 domains by score
        result.topDomains = completedResults
          .sort((a, b) => (b.score || 0) - (a.score || 0))
          .slice(0, 10)
          .map((r) => ({
            targetDomainId: r.targetDomainId,
            score: r.score || 0,
          }));
      }

      result.completedAt = new Date();
      result.processingTimeMs = Date.now() - startTime;
      result.processedDomains = result.results.filter(
        (r) => r.status === "completed"
      ).length;
      result.failedDomains = result.results.filter(
        (r) => r.status === "failed"
      ).length;

      await job.progress(100);

      // Cache the batch result
      const cacheKey = `batch_analysis:${batchId}`;
      await this.cacheService.set(cacheKey, result, 7200); // Cache for 2 hours

      this.logger.log(
        `Completed batch analysis job ${job.id} for batch ${batchId}. ` +
          `Processed: ${result.processedDomains}, Failed: ${result.failedDomains}, ` +
          `Average Score: ${result.averageScore?.toFixed(2) || "N/A"}`
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to process batch analysis job ${job.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<BatchAnalysisJobData>) {
    this.logger.log(
      `Processing batch job ${job.id} for batch ${job.data.batchId} with ${job.data.targetDomainIds.length} domains`
    );
  }

  @OnQueueCompleted()
  onCompleted(job: Job<BatchAnalysisJobData>, result: BatchAnalysisResult) {
    this.logger.log(
      `Completed batch job ${job.id} for batch ${job.data.batchId}. ` +
        `Processed: ${result.processedDomains}/${result.totalDomains} domains`
    );
  }

  @OnQueueFailed()
  onFailed(job: Job<BatchAnalysisJobData>, error: Error) {
    this.logger.error(
      `Failed batch job ${job.id} for batch ${job.data.batchId}: ${error.message}`
    );
  }

  /**
   * Wait for a batch of jobs to complete
   */
  private async waitForBatchCompletion(
    jobIds: string[],
    result: BatchAnalysisResult,
    timeoutMs: number
  ): Promise<void> {
    const startTime = Date.now();
    const checkInterval = 5000; // Check every 5 seconds

    while (Date.now() - startTime < timeoutMs) {
      let allCompleted = true;

      for (const jobId of jobIds) {
        const job = await this.domainAnalysisQueue.getJob(jobId);
        if (!job) continue;

        const jobState = await job.getState();
        const jobStatus = {
          id: job.id.toString(),
          status: jobState === "stuck" ? "failed" : jobState,
          result: job.returnvalue,
          error: job.failedReason,
        };

        if (jobStatus) {
          const resultIndex = result.results.findIndex(
            (r) => r.jobId === jobId
          );

          if (jobStatus.status === "completed") {
            if (result.results[resultIndex].status === "pending") {
              result.results[resultIndex].status = "completed";
              result.results[resultIndex].score =
                (jobStatus.result as any)?.score || 0;
            }
          } else if (jobStatus.status === "failed") {
            if (result.results[resultIndex].status === "pending") {
              result.results[resultIndex].status = "failed";
              result.results[resultIndex].error =
                jobStatus.error || "Job failed";
            }
          } else {
            allCompleted = false;
          }
        } else {
          allCompleted = false;
        }
      }

      if (allCompleted) {
        break;
      }

      await this.delay(checkInterval);
    }

    // Mark any remaining pending jobs as failed due to timeout
    result.results.forEach((r) => {
      if (r.status === "pending") {
        r.status = "failed";
        r.error = "Timeout waiting for job completion";
      }
    });
  }

  /**
   * Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Delay execution
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
