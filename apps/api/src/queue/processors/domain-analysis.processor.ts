import {
  Processor,
  Process,
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
} from "@nestjs/bull";
import { Injectable, Logger, Inject } from "@nestjs/common";
import { Job } from "bull";
import { DomainAnalysisJobData } from "../queue.service";
import { DOMAIN_ANALYSIS_QUEUE } from "../queue.module";
import { CacheService } from "../../cache/cache.service";

export interface DomainAnalysisResult {
  targetDomainId: string;
  userId: string;
  score: number;
  factors: {
    domainRating: number;
    trafficVolume: number;
    keywordRelevance: number;
    websiteAge: number;
    industryMatch: number;
    countryLanguageMatch: number;
    contactInfoPresent: number;
  };
  breakdown: Record<
    string,
    { value: number; weight: number; contribution: number }
  >;
  domainInfo: {
    title?: string;
    description?: string;
    keywords?: string[];
    industry?: string;
    language?: string;
    country?: string;
    websiteAge?: number;
    trafficData?: {
      visits: number;
      trend: "upward" | "downward" | "stable";
    };
    contactInfo?: {
      emails: string[];
      contactPage: string | null;
      socialProfiles: string[];
      phoneNumbers: string[];
    };
  };
  processedAt: Date;
  processingTimeMs: number;
}

@Injectable()
@Processor(DOMAIN_ANALYSIS_QUEUE)
export class DomainAnalysisProcessor {
  private readonly logger = new Logger(DomainAnalysisProcessor.name);

  constructor(
    @Inject(CacheService)
    private readonly cacheService: CacheService
  ) {}

  @Process("analyze-domain")
  async handleDomainAnalysis(
    job: Job<DomainAnalysisJobData>
  ): Promise<DomainAnalysisResult> {
    const startTime = Date.now();
    const { targetDomainId, userId, userDomainId, options } = job.data;

    this.logger.log(
      `Processing domain analysis job ${job.id} for domain ${targetDomainId}`
    );

    try {
      // Update job progress
      await job.progress(10);

      // Get target domain info (this would typically come from database)
      const targetDomain = await this.getTargetDomainInfo(targetDomainId);
      if (!targetDomain) {
        throw new Error(`Target domain not found: ${targetDomainId}`);
      }

      await job.progress(20);

      // Get user domain info for comparison
      const userDomain = userDomainId
        ? await this.getUserDomainInfo(userDomainId)
        : await this.getDefaultUserDomain(userId);

      await job.progress(30);

      // Mock domain analysis (replace with actual analysis later)
      const domainInfo = await this.mockDomainAnalysis(targetDomain.domain);

      await job.progress(50);

      // Mock contact information extraction
      let contactInfo;
      if (options?.includeContactInfo !== false) {
        contactInfo = await this.mockContactExtraction(targetDomain.domain);
      }

      await job.progress(70);

      // Mock scoring calculation
      const scoringResult = await this.mockScoringCalculation(
        targetDomain.domain,
        userDomain.domain,
        domainInfo,
        contactInfo
      );

      await job.progress(90);

      // Prepare result
      const result: DomainAnalysisResult = {
        targetDomainId,
        userId,
        score: scoringResult.score,
        factors: scoringResult.factors,
        breakdown: scoringResult.breakdown,
        domainInfo: {
          title: domainInfo.title,
          description: domainInfo.description,
          keywords: domainInfo.keywords,
          industry: domainInfo.industry,
          language: domainInfo.language,
          country: domainInfo.country,
          websiteAge: domainInfo.websiteAge,
          trafficData: domainInfo.trafficData
            ? {
                visits: domainInfo.trafficData.visits,
                trend: domainInfo.trafficData.trend,
              }
            : undefined,
          contactInfo: contactInfo
            ? {
                emails: contactInfo.emails,
                contactPage: contactInfo.contactPage,
                socialProfiles: contactInfo.socialProfiles,
                phoneNumbers: contactInfo.phoneNumbers,
              }
            : undefined,
        },
        processedAt: new Date(),
        processingTimeMs: Date.now() - startTime,
      };

      await job.progress(100);

      // Cache the result
      const cacheKey = this.cacheService.generateDomainAnalysisKey(
        targetDomain.domain,
        userDomain.domain
      );
      await this.cacheService.set(cacheKey, result, 3600); // Cache for 1 hour

      this.logger.log(
        `Completed domain analysis job ${job.id} in ${result.processingTimeMs}ms`
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to process domain analysis job ${job.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<DomainAnalysisJobData>) {
    this.logger.log(
      `Processing job ${job.id} for domain ${job.data.targetDomainId}`
    );
  }

  @OnQueueCompleted()
  onCompleted(job: Job<DomainAnalysisJobData>, result: DomainAnalysisResult) {
    this.logger.log(
      `Completed job ${job.id} for domain ${job.data.targetDomainId} with score ${result.score}`
    );
  }

  @OnQueueFailed()
  onFailed(job: Job<DomainAnalysisJobData>, error: Error) {
    this.logger.error(
      `Failed job ${job.id} for domain ${job.data.targetDomainId}: ${error.message}`
    );
  }

  /**
   * Get target domain information (mock implementation)
   */
  private async getTargetDomainInfo(targetDomainId: string): Promise<{
    domain: string;
    title?: string;
    description?: string;
  } | null> {
    // In a real implementation, this would query the database
    // For now, we'll extract the domain from the ID or use a mock
    const domain = targetDomainId.includes(".")
      ? targetDomainId
      : `${targetDomainId}.com`;

    return {
      domain,
      title: `Domain ${domain}`,
      description: `Analysis target domain: ${domain}`,
    };
  }

  /**
   * Get user domain information (mock implementation)
   */
  private async getUserDomainInfo(userDomainId: string): Promise<{
    domain: string;
    keywords?: string[];
    industry?: string;
    language?: string;
    country?: string;
  }> {
    // In a real implementation, this would query the database
    const domain = userDomainId.includes(".")
      ? userDomainId
      : `${userDomainId}.com`;

    return {
      domain,
      keywords: ["business", "technology", "services"],
      industry: "Technology",
      language: "en",
      country: "US",
    };
  }

  /**
   * Get default user domain (mock implementation)
   */
  private async getDefaultUserDomain(userId: string): Promise<{
    domain: string;
    keywords?: string[];
    industry?: string;
    language?: string;
    country?: string;
  }> {
    // In a real implementation, this would query the user's primary domain
    return {
      domain: `user-${userId}.com`,
      keywords: ["business", "services"],
      industry: "Business",
      language: "en",
      country: "US",
    };
  }

  /**
   * Mock domain analysis
   */
  private async mockDomainAnalysis(domain: string): Promise<any> {
    return {
      title: `${domain} - Mock Title`,
      description: `Mock description for ${domain}`,
      keywords: ["business", "technology", "services"],
      industry: "Technology",
      language: "en",
      country: "US",
      websiteAge: 24, // months
      trafficData: {
        visits: 10000,
        trend: "upward" as const,
      },
    };
  }

  /**
   * Mock contact extraction
   */
  private async mockContactExtraction(domain: string): Promise<any> {
    return {
      emails: [`contact@${domain}`, `info@${domain}`],
      contactPage: `https://${domain}/contact`,
      socialProfiles: {
        twitter: `@${domain.split(".")[0]}`,
        linkedin: `https://linkedin.com/company/${domain.split(".")[0]}`,
      },
      phoneNumbers: ["******-0123"],
    };
  }

  /**
   * Mock scoring calculation
   */
  private async mockScoringCalculation(
    targetDomain: string,
    userDomain: string,
    domainInfo: any,
    contactInfo: any
  ): Promise<any> {
    const baseScore = Math.random() * 40 + 40; // Random score between 40-80

    return {
      score: Math.round(baseScore * 100) / 100,
      factors: {
        domainRating: 0.8,
        trafficVolume: 0.7,
        keywordRelevance: 0.6,
        websiteAge: 0.5,
        industryMatch: 0.9,
        countryLanguageMatch: 0.8,
        contactInfoPresent: contactInfo ? 1.0 : 0.0,
      },
      breakdown: {
        domainRating: { value: 65, weight: 0.3, contribution: 19.5 },
        trafficVolume: { value: 70, weight: 0.2, contribution: 14.0 },
        keywordRelevance: { value: 60, weight: 0.2, contribution: 12.0 },
        websiteAge: { value: 50, weight: 0.1, contribution: 5.0 },
        industryMatch: { value: 90, weight: 0.1, contribution: 9.0 },
        countryLanguageMatch: { value: 80, weight: 0.05, contribution: 4.0 },
        contactInfoPresent: {
          value: contactInfo ? 100 : 0,
          weight: 0.05,
          contribution: contactInfo ? 5.0 : 0,
        },
      },
    };
  }
}
