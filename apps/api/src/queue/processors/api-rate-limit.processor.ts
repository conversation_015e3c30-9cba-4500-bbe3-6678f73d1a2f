import {
  Processor,
  Process,
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
} from "@nestjs/bull";
import { Injectable, Logger, Inject } from "@nestjs/common";
import { Job } from "bull";
import { API_RATE_LIMIT_QUEUE } from "../queue.module";
import { ApiCallJobData } from "../queue.service";
import { AhrefsService } from "../../third-party/ahrefs/ahrefs.service";
import { SimilarWebService } from "../../third-party/similarweb/similarweb.service";
import { WhoisXmlService } from "../../third-party/whoisxml/whoisxml.service";
import { CacheService } from "../../cache/cache.service";

export interface ApiCallResult {
  provider: string;
  endpoint: string;
  domain: string;
  dataType: string;
  success: boolean;
  data?: any;
  error?: string;
  cached: boolean;
  processingTimeMs: number;
  rateLimitInfo?: {
    remaining: number;
    resetTime: Date;
    retryAfter?: number;
  };
}

@Injectable()
@Processor(API_RATE_LIMIT_QUEUE)
export class ApiRateLimitProcessor {
  private readonly logger = new Logger(ApiRateLimitProcessor.name);

  // Rate limiting state per provider
  private readonly rateLimitState = new Map<
    string,
    {
      lastRequestTime: number;
      requestCount: number;
      resetTime: number;
      minInterval: number;
    }
  >();

  constructor(
    @Inject(AhrefsService)
    private readonly ahrefsService: AhrefsService,
    @Inject(SimilarWebService)
    private readonly similarWebService: SimilarWebService,
    @Inject(WhoisXmlService)
    private readonly whoisXmlService: WhoisXmlService,
    @Inject(CacheService)
    private readonly cacheService: CacheService
  ) {
    // Initialize rate limit configurations
    this.initializeRateLimits();
  }

  @Process("api-call")
  async handleApiCall(job: Job<ApiCallJobData>): Promise<ApiCallResult> {
    const startTime = Date.now();
    const { provider, endpoint, params, domain, dataType } = job.data;

    this.logger.debug(
      `Processing API call job ${job.id} for ${provider}/${dataType} on domain ${domain}`
    );

    try {
      // Check cache first
      const cacheKey = this.cacheService.generateSeoMetricsKey(
        domain,
        `${provider}_${dataType}`
      );
      const cachedData = await this.cacheService.get(cacheKey);

      if (cachedData) {
        this.logger.debug(
          `Cache hit for ${provider}/${dataType} on domain ${domain}`
        );
        return {
          provider,
          endpoint,
          domain,
          dataType,
          success: true,
          data: cachedData,
          cached: true,
          processingTimeMs: Date.now() - startTime,
        };
      }

      // Apply rate limiting
      await this.applyRateLimit(provider);

      // Make the API call
      let data: any;
      let error: string | undefined;
      let success = false;

      try {
        switch (provider.toLowerCase()) {
          case "ahrefs":
            data = await this.makeAhrefsCall(dataType, domain, params);
            break;
          case "similarweb":
            data = await this.makeSimilarWebCall(dataType, domain, params);
            break;
          case "whoisxml":
            data = await this.makeWhoisXmlCall(dataType, domain, params);
            break;
          default:
            throw new Error(`Unknown provider: ${provider}`);
        }
        success = true;
      } catch (apiError) {
        error = apiError.message;
        this.logger.warn(
          `API call failed for ${provider}/${dataType} on domain ${domain}: ${error}`
        );
      }

      // Cache successful results
      if (success && data) {
        const ttl = this.getCacheTtl(provider, dataType);
        await this.cacheService.set(cacheKey, data, ttl);
      }

      const result: ApiCallResult = {
        provider,
        endpoint,
        domain,
        dataType,
        success,
        data,
        error,
        cached: false,
        processingTimeMs: Date.now() - startTime,
        rateLimitInfo: this.getRateLimitInfo(provider),
      };

      this.logger.debug(
        `Completed API call job ${job.id} for ${provider}/${dataType} on domain ${domain} ` +
          `(success: ${success}, time: ${result.processingTimeMs}ms)`
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to process API call job ${job.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<ApiCallJobData>) {
    this.logger.debug(
      `Processing API call job ${job.id} for ${job.data.provider}/${job.data.dataType} on domain ${job.data.domain}`
    );
  }

  @OnQueueCompleted()
  onCompleted(job: Job<ApiCallJobData>, result: ApiCallResult) {
    this.logger.debug(
      `Completed API call job ${job.id} for ${job.data.provider}/${job.data.dataType} ` +
        `(success: ${result.success}, cached: ${result.cached})`
    );
  }

  @OnQueueFailed()
  onFailed(job: Job<ApiCallJobData>, error: Error) {
    this.logger.error(
      `Failed API call job ${job.id} for ${job.data.provider}/${job.data.dataType}: ${error.message}`
    );
  }

  /**
   * Initialize rate limit configurations for each provider
   */
  private initializeRateLimits(): void {
    // Ahrefs: 100 requests per minute
    this.rateLimitState.set("ahrefs", {
      lastRequestTime: 0,
      requestCount: 0,
      resetTime: Date.now() + 60000,
      minInterval: 600, // 600ms between requests
    });

    // SimilarWeb: 1000 requests per month (conservative: 1 per minute)
    this.rateLimitState.set("similarweb", {
      lastRequestTime: 0,
      requestCount: 0,
      resetTime: Date.now() + 60000,
      minInterval: 60000, // 60s between requests
    });

    // WhoisXML: 1000 requests per month (conservative: 1 per minute)
    this.rateLimitState.set("whoisxml", {
      lastRequestTime: 0,
      requestCount: 0,
      resetTime: Date.now() + 60000,
      minInterval: 60000, // 60s between requests
    });
  }

  /**
   * Apply rate limiting for a provider
   */
  private async applyRateLimit(provider: string): Promise<void> {
    const state = this.rateLimitState.get(provider.toLowerCase());
    if (!state) {
      this.logger.warn(`No rate limit configuration for provider: ${provider}`);
      return;
    }

    const now = Date.now();

    // Reset counter if reset time has passed
    if (now >= state.resetTime) {
      state.requestCount = 0;
      state.resetTime = now + 60000; // Reset every minute
    }

    // Check if we need to wait
    const timeSinceLastRequest = now - state.lastRequestTime;
    if (timeSinceLastRequest < state.minInterval) {
      const waitTime = state.minInterval - timeSinceLastRequest;
      this.logger.debug(`Rate limiting ${provider}: waiting ${waitTime}ms`);
      await this.delay(waitTime);
    }

    // Update state
    state.lastRequestTime = Date.now();
    state.requestCount++;
  }

  /**
   * Make Ahrefs API call
   */
  private async makeAhrefsCall(
    dataType: string,
    domain: string,
    params: any
  ): Promise<any> {
    switch (dataType) {
      case "domain-rating":
        return await this.ahrefsService.getDomainRating(domain);
      case "backlinks":
        return await this.ahrefsService.getBacklinks(
          domain,
          params.limit || 100
        );
      case "keywords":
        return await this.ahrefsService.getOrganicKeywords(
          domain,
          params.limit || 100
        );
      default:
        throw new Error(`Unknown Ahrefs data type: ${dataType}`);
    }
  }

  /**
   * Make SimilarWeb API call
   */
  private async makeSimilarWebCall(
    dataType: string,
    domain: string,
    params: any
  ): Promise<any> {
    switch (dataType) {
      case "traffic":
        return await this.similarWebService.getTrafficData(domain);
      default:
        throw new Error(`Unknown SimilarWeb data type: ${dataType}`);
    }
  }

  /**
   * Make WhoisXML API call
   */
  private async makeWhoisXmlCall(
    dataType: string,
    domain: string,
    params: any
  ): Promise<any> {
    switch (dataType) {
      case "whois":
        return await this.whoisXmlService.getDomainAge(domain);
      default:
        throw new Error(`Unknown WhoisXML data type: ${dataType}`);
    }
  }

  /**
   * Get cache TTL for different data types
   */
  private getCacheTtl(provider: string, dataType: string): number {
    const ttlMap: Record<string, Record<string, number>> = {
      ahrefs: {
        "domain-rating": 3600, // 1 hour
        backlinks: 1800, // 30 minutes
        keywords: 3600, // 1 hour
      },
      similarweb: {
        traffic: 7200, // 2 hours
      },
      whoisxml: {
        whois: 86400, // 24 hours (domain age doesn't change often)
      },
    };

    return ttlMap[provider.toLowerCase()]?.[dataType] || 3600; // Default 1 hour
  }

  /**
   * Get rate limit information for a provider
   */
  private getRateLimitInfo(provider: string):
    | {
        remaining: number;
        resetTime: Date;
        retryAfter?: number;
      }
    | undefined {
    const state = this.rateLimitState.get(provider.toLowerCase());
    if (!state) return undefined;

    const maxRequests = provider.toLowerCase() === "ahrefs" ? 100 : 60; // Requests per hour
    const remaining = Math.max(0, maxRequests - state.requestCount);

    return {
      remaining,
      resetTime: new Date(state.resetTime),
      retryAfter:
        remaining === 0 ? Math.max(0, state.resetTime - Date.now()) : undefined,
    };
  }

  /**
   * Delay execution
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
