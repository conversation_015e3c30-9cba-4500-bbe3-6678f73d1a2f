import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  Query,
  HttpException,
  HttpStatus,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import {
  QueueService,
  DomainAnalysisJobData,
  BatchAnalysisJobData,
  QueueJobOptions,
} from "./queue.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { GetUser } from "../auth/decorators/get-user.decorator";
import { User } from "../users/entities/user.entity";

class CreateDomainAnalysisJobDto {
  targetDomainId: string;
  userDomainId?: string;
  options?: {
    includeContactInfo?: boolean;
    includeKeywords?: boolean;
    includeTrafficData?: boolean;
  };
  jobOptions?: QueueJobOptions;
}

class CreateBatchAnalysisJobDto {
  targetDomainIds: string[];
  batchId: string;
  userDomainId?: string;
  options?: {
    includeContactInfo?: boolean;
    includeKeywords?: boolean;
    includeTrafficData?: boolean;
    maxConcurrency?: number;
  };
  jobOptions?: QueueJobOptions;
}

class QueueActionDto {
  action: "pause" | "resume" | "clean";
  grace?: number;
  limit?: number;
}

@ApiTags("Queue Management")
@Controller("queue")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth("JWT-auth")
export class QueueController {
  constructor(private readonly queueService: QueueService) {}

  @Post("domain-analysis")
  @ApiOperation({ summary: "Add domain analysis job to queue" })
  @ApiResponse({ status: 201, description: "Job added successfully" })
  @ApiResponse({ status: 400, description: "Invalid request data" })
  async addDomainAnalysisJob(
    @Body() createJobDto: CreateDomainAnalysisJobDto,
    @GetUser() user: User
  ) {
    try {
      const jobData: DomainAnalysisJobData = {
        targetDomainId: createJobDto.targetDomainId,
        userId: user.id,
        userDomainId: createJobDto.userDomainId,
        options: createJobDto.options,
      };

      const job = await this.queueService.addDomainAnalysisJob(
        jobData,
        createJobDto.jobOptions
      );

      return {
        success: true,
        jobId: job?.id?.toString() || "unknown",
        queueName: "domain-analysis",
        message: "Domain analysis job added successfully",
      };
    } catch (error) {
      throw new HttpException(
        `Failed to add domain analysis job: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post("batch-analysis")
  @ApiOperation({ summary: "Add batch analysis job to queue" })
  @ApiResponse({ status: 201, description: "Batch job added successfully" })
  @ApiResponse({ status: 400, description: "Invalid request data" })
  async addBatchAnalysisJob(
    @Body() createBatchJobDto: CreateBatchAnalysisJobDto,
    @GetUser() user: User
  ) {
    try {
      const jobData: BatchAnalysisJobData = {
        targetDomainIds: createBatchJobDto.targetDomainIds,
        userId: user.id,
        batchId: createBatchJobDto.batchId,
        userDomainId: createBatchJobDto.userDomainId,
        options: createBatchJobDto.options,
      };

      const job = await this.queueService.addBatchAnalysisJob(
        jobData,
        createBatchJobDto.jobOptions
      );

      return {
        success: true,
        jobId: job?.id?.toString() || "unknown",
        queueName: "batch-analysis",
        batchId: createBatchJobDto.batchId,
        totalDomains: createBatchJobDto.targetDomainIds.length,
        message: "Batch analysis job added successfully",
      };
    } catch (error) {
      throw new HttpException(
        `Failed to add batch analysis job: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get("job/:queueName/:jobId")
  @ApiOperation({ summary: "Get job status" })
  @ApiParam({ name: "queueName", description: "Queue name" })
  @ApiParam({ name: "jobId", description: "Job ID" })
  @ApiResponse({
    status: 200,
    description: "Job status retrieved successfully",
  })
  @ApiResponse({ status: 404, description: "Job not found" })
  async getJobStatus(
    @Param("queueName") queueName: string,
    @Param("jobId") jobId: string
  ) {
    const jobStatus = await this.queueService.getJobStatus(queueName, jobId);

    if (!jobStatus) {
      throw new HttpException(
        `Job ${jobId} not found in queue ${queueName}`,
        HttpStatus.NOT_FOUND
      );
    }

    return {
      success: true,
      job: jobStatus,
    };
  }

  @Get("stats/:queueName")
  @ApiOperation({ summary: "Get queue statistics" })
  @ApiParam({ name: "queueName", description: "Queue name" })
  @ApiResponse({
    status: 200,
    description: "Queue statistics retrieved successfully",
  })
  @ApiResponse({ status: 404, description: "Queue not found" })
  async getQueueStats(@Param("queueName") queueName: string) {
    const stats = await this.queueService.getQueueStats(queueName);

    if (!stats) {
      throw new HttpException(
        `Queue ${queueName} not found`,
        HttpStatus.NOT_FOUND
      );
    }

    return {
      success: true,
      queueName,
      stats,
    };
  }

  @Get("stats")
  @ApiOperation({ summary: "Get all queue statistics" })
  @ApiResponse({
    status: 200,
    description: "All queue statistics retrieved successfully",
  })
  async getAllQueueStats() {
    const queueNames = this.queueService.getQueueNames();
    const allStats = await Promise.all(
      queueNames.map(async (queueName) => {
        const stats = await this.queueService.getQueueStats(queueName);
        return {
          queueName,
          stats,
        };
      })
    );

    return {
      success: true,
      queues: allStats,
    };
  }

  @Post("action/:queueName")
  @ApiOperation({ summary: "Perform queue action (pause, resume, clean)" })
  @ApiParam({ name: "queueName", description: "Queue name" })
  @ApiResponse({
    status: 200,
    description: "Queue action performed successfully",
  })
  @ApiResponse({ status: 400, description: "Invalid action or queue name" })
  async performQueueAction(
    @Param("queueName") queueName: string,
    @Body() actionDto: QueueActionDto
  ) {
    try {
      switch (actionDto.action) {
        case "pause":
          await this.queueService.pauseQueue(queueName);
          break;
        case "resume":
          await this.queueService.resumeQueue(queueName);
          break;
        case "clean":
          await this.queueService.cleanQueue(
            queueName,
            actionDto.grace,
            actionDto.limit
          );
          break;
        default:
          throw new Error(`Unknown action: ${actionDto.action}`);
      }

      return {
        success: true,
        queueName,
        action: actionDto.action,
        message: `Queue ${actionDto.action} action performed successfully`,
      };
    } catch (error) {
      throw new HttpException(
        `Failed to perform ${actionDto.action} action on queue ${queueName}: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get("health")
  @ApiOperation({ summary: "Get queue system health status" })
  @ApiResponse({
    status: 200,
    description: "Queue health status retrieved successfully",
  })
  async getQueueHealth() {
    try {
      const queueNames = this.queueService.getQueueNames();
      const healthChecks = await Promise.all(
        queueNames.map(async (queueName) => {
          try {
            const stats = await this.queueService.getQueueStats(queueName);
            return {
              queueName,
              healthy: stats !== null,
              stats,
              error: null,
            };
          } catch (error) {
            return {
              queueName,
              healthy: false,
              stats: null,
              error: error.message,
            };
          }
        })
      );

      const overallHealthy = healthChecks.every((check) => check.healthy);

      return {
        success: true,
        healthy: overallHealthy,
        queues: healthChecks,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new HttpException(
        `Failed to get queue health status: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get("jobs/:queueName")
  @ApiOperation({ summary: "Get jobs in queue with pagination" })
  @ApiParam({ name: "queueName", description: "Queue name" })
  @ApiQuery({
    name: "status",
    required: false,
    description: "Filter by job status",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of jobs to return",
  })
  @ApiQuery({
    name: "offset",
    required: false,
    description: "Number of jobs to skip",
  })
  @ApiResponse({ status: 200, description: "Jobs retrieved successfully" })
  async getQueueJobs(
    @Param("queueName") queueName: string,
    @Query("status") status?: string,
    @Query("limit") limit?: string,
    @Query("offset") offset?: string
  ) {
    // This would require additional implementation in QueueService
    // For now, return a placeholder response
    return {
      success: true,
      queueName,
      jobs: [],
      pagination: {
        limit: parseInt(limit || "10"),
        offset: parseInt(offset || "0"),
        total: 0,
      },
      message: "Job listing feature coming soon",
    };
  }
}
