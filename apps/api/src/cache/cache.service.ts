import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * Get a value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);
      if (value) {
        this.logger.debug(`Cache hit for key: ${key}`);
      } else {
        this.logger.debug(`Cache miss for key: ${key}`);
      }
      return value || null;
    } catch (error) {
      this.logger.error(`Error getting cache key ${key}: ${error.message}`);
      return null;
    }
  }

  /**
   * Set a value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
      this.logger.debug(`Cache set for key: ${key}, TTL: ${ttl || 'default'}`);
    } catch (error) {
      this.logger.error(`Error setting cache key ${key}: ${error.message}`);
    }
  }

  /**
   * Delete a value from cache
   */
  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Cache deleted for key: ${key}`);
    } catch (error) {
      this.logger.error(`Error deleting cache key ${key}: ${error.message}`);
    }
  }

  /**
   * Clear all cache
   */
  async reset(): Promise<void> {
    try {
      await this.cacheManager.reset();
      this.logger.debug('Cache cleared');
    } catch (error) {
      this.logger.error(`Error clearing cache: ${error.message}`);
    }
  }

  /**
   * Get or set a value in cache
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    try {
      let value = await this.get<T>(key);
      
      if (value === null) {
        this.logger.debug(`Cache miss for key: ${key}, executing factory function`);
        value = await factory();
        await this.set(key, value, ttl);
      }
      
      return value;
    } catch (error) {
      this.logger.error(`Error in getOrSet for key ${key}: ${error.message}`);
      // If cache fails, still execute the factory function
      return await factory();
    }
  }

  /**
   * Generate cache key for domain analysis
   */
  generateDomainAnalysisKey(targetDomain: string, userDomain: string): string {
    return `domain_analysis:${targetDomain}:${userDomain}`;
  }

  /**
   * Generate cache key for SEO metrics
   */
  generateSeoMetricsKey(domain: string, provider: string): string {
    return `seo_metrics:${provider}:${domain}`;
  }

  /**
   * Generate cache key for domain info
   */
  generateDomainInfoKey(domain: string): string {
    return `domain_info:${domain}`;
  }

  /**
   * Generate cache key for contact info
   */
  generateContactInfoKey(domain: string): string {
    return `contact_info:${domain}`;
  }

  /**
   * Generate cache key for WHOIS data
   */
  generateWhoisKey(domain: string): string {
    return `whois:${domain}`;
  }
}
