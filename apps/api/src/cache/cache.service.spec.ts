import { Test, TestingModule } from '@nestjs/testing';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CacheService } from './cache.service';

describe('CacheService', () => {
  let service: CacheService;
  let cacheManager: any;

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CacheService,
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
      ],
    }).compile();

    service = module.get<CacheService>(CacheService);
    cacheManager = module.get(CACHE_MANAGER);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('get', () => {
    it('should return cached value when it exists', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      mockCacheManager.get.mockResolvedValue(testValue);

      const result = await service.get(testKey);

      expect(result).toEqual(testValue);
      expect(mockCacheManager.get).toHaveBeenCalledWith(testKey);
    });

    it('should return null when cache miss', async () => {
      const testKey = 'test-key';
      mockCacheManager.get.mockResolvedValue(undefined);

      const result = await service.get(testKey);

      expect(result).toBeNull();
      expect(mockCacheManager.get).toHaveBeenCalledWith(testKey);
    });

    it('should return null when cache throws error', async () => {
      const testKey = 'test-key';
      mockCacheManager.get.mockRejectedValue(new Error('Cache error'));

      const result = await service.get(testKey);

      expect(result).toBeNull();
      expect(mockCacheManager.get).toHaveBeenCalledWith(testKey);
    });
  });

  describe('set', () => {
    it('should set value in cache', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      const ttl = 3600;

      await service.set(testKey, testValue, ttl);

      expect(mockCacheManager.set).toHaveBeenCalledWith(testKey, testValue, ttl);
    });

    it('should handle cache set errors gracefully', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      mockCacheManager.set.mockRejectedValue(new Error('Cache error'));

      await expect(service.set(testKey, testValue)).resolves.not.toThrow();
      expect(mockCacheManager.set).toHaveBeenCalledWith(testKey, testValue, undefined);
    });
  });

  describe('del', () => {
    it('should delete value from cache', async () => {
      const testKey = 'test-key';

      await service.del(testKey);

      expect(mockCacheManager.del).toHaveBeenCalledWith(testKey);
    });

    it('should handle cache delete errors gracefully', async () => {
      const testKey = 'test-key';
      mockCacheManager.del.mockRejectedValue(new Error('Cache error'));

      await expect(service.del(testKey)).resolves.not.toThrow();
      expect(mockCacheManager.del).toHaveBeenCalledWith(testKey);
    });
  });

  describe('getOrSet', () => {
    it('should return cached value when it exists', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      mockCacheManager.get.mockResolvedValue(testValue);
      const factory = jest.fn();

      const result = await service.getOrSet(testKey, factory);

      expect(result).toEqual(testValue);
      expect(mockCacheManager.get).toHaveBeenCalledWith(testKey);
      expect(factory).not.toHaveBeenCalled();
      expect(mockCacheManager.set).not.toHaveBeenCalled();
    });

    it('should execute factory and cache result when cache miss', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      const ttl = 3600;
      mockCacheManager.get.mockResolvedValue(null);
      const factory = jest.fn().mockResolvedValue(testValue);

      const result = await service.getOrSet(testKey, factory, ttl);

      expect(result).toEqual(testValue);
      expect(mockCacheManager.get).toHaveBeenCalledWith(testKey);
      expect(factory).toHaveBeenCalled();
      expect(mockCacheManager.set).toHaveBeenCalledWith(testKey, testValue, ttl);
    });

    it('should execute factory when cache fails', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test' };
      mockCacheManager.get.mockRejectedValue(new Error('Cache error'));
      const factory = jest.fn().mockResolvedValue(testValue);

      const result = await service.getOrSet(testKey, factory);

      expect(result).toEqual(testValue);
      expect(factory).toHaveBeenCalled();
    });
  });

  describe('key generation methods', () => {
    it('should generate domain analysis key', () => {
      const targetDomain = 'example.com';
      const userDomain = 'user.com';
      const expected = 'domain_analysis:example.com:user.com';

      const result = service.generateDomainAnalysisKey(targetDomain, userDomain);

      expect(result).toBe(expected);
    });

    it('should generate SEO metrics key', () => {
      const domain = 'example.com';
      const provider = 'ahrefs';
      const expected = 'seo_metrics:ahrefs:example.com';

      const result = service.generateSeoMetricsKey(domain, provider);

      expect(result).toBe(expected);
    });

    it('should generate domain info key', () => {
      const domain = 'example.com';
      const expected = 'domain_info:example.com';

      const result = service.generateDomainInfoKey(domain);

      expect(result).toBe(expected);
    });

    it('should generate contact info key', () => {
      const domain = 'example.com';
      const expected = 'contact_info:example.com';

      const result = service.generateContactInfoKey(domain);

      expect(result).toBe(expected);
    });

    it('should generate WHOIS key', () => {
      const domain = 'example.com';
      const expected = 'whois:example.com';

      const result = service.generateWhoisKey(domain);

      expect(result).toBe(expected);
    });
  });
});
