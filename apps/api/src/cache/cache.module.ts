import { Modu<PERSON> } from "@nestjs/common";
import { CacheModule as NestCacheModule } from "@nestjs/cache-manager";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { CacheService } from "./cache.service";

@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        // Get cache configuration with proper type conversion and validation
        const cacheTtlRaw = configService.get("CACHE_TTL", "3600");
        const cacheMaxItemsRaw = configService.get("CACHE_MAX_ITEMS", "1000");

        const cacheTtl = parseInt(cacheTtlRaw, 10);
        const cacheMaxItems = parseInt(cacheMaxItemsRaw, 10);

        // Ensure values are valid positive integers with fallbacks
        const ttl = isNaN(cacheTtl) || cacheTtl <= 0 ? 3600 : cacheTtl;
        const max =
          isNaN(cacheMaxItems) || cacheMaxItems <= 0 ? 1000 : cacheMaxItems;

        // Log cache configuration for debugging
        console.log(
          `Cache Configuration: TTL=${ttl}s (${ttl * 1000}ms), MAX=${max} items`
        );

        return {
          ttl: ttl * 1000, // Convert to milliseconds
          max: max,
        };
      },
    }),
  ],
  providers: [CacheService],
  exports: [CacheService],
})
export class CacheModule {}
