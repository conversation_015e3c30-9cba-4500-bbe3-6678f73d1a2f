import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ThirdPartySimpleModule } from "./third-party/third-party-simple.module";

/**
 * Ultra-minimal app module for testing without any external dependencies
 * This module only includes:
 * - Basic configuration
 * - App controller and service
 * - Simplified third-party services (no cache dependencies)
 */
@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.development", ".env.local", ".env"],
    }),

    // Simplified third-party services (no cache dependencies)
    ThirdPartySimpleModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppMinimalModule {}
