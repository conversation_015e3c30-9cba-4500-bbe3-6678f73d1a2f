import { NestFactory } from "@nestjs/core";
import { ValidationPipe } from "@nestjs/common";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MockAuthModule } from "./mock-auth.module";

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.development", ".env.local", ".env"],
    }),

    // Mock auth module (no database required)
    MockAuthModule,
  ],
})
class AuthOnlyModule {}

async function bootstrap() {
  const app = await NestFactory.create(AuthOnlyModule);

  // Enable CORS
  app.enableCors({
    origin: ["http://localhost:3000", "http://localhost:3001"],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    })
  );

  // Swagger setup
  const config = new DocumentBuilder()
    .setTitle("RankMesh API (Auth Only)")
    .setDescription("Authentication-only version of the RankMesh API")
    .setVersion("1.0")
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("api", app, document);

  const port = process.env.PORT || 3050;
  await app.listen(port);

  console.log(
    `🚀 RankMesh API (Auth Only) is running on: http://localhost:${port}`
  );
  console.log(`📚 Swagger documentation: http://localhost:${port}/api`);
}

bootstrap().catch((error) => {
  console.error("Failed to start the application:", error);
  process.exit(1);
});
