import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpStatus,
  HttpException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { CampaignService } from "../services/campaign.service";
import { EmailCampaign } from "../entities/email-campaign.entity";
import { GetUser } from "../../auth/decorators/get-user.decorator";
import { User } from "../../users/entities/user.entity";
import { CreateCampaignDto, UpdateCampaignDto } from "../dto/campaign.dto";

@ApiTags("campaigns")
@Controller("campaigns")
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtAuthGuard)
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Get()
  @ApiOperation({ summary: "Get all email campaigns for the current user" })
  @ApiResponse({
    status: 200,
    description: "Returns all email campaigns",
    type: [EmailCampaign],
  })
  async findAll(@GetUser() user: User): Promise<EmailCampaign[]> {
    return this.campaignService.findAll(user.id);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get an email campaign by ID" })
  @ApiResponse({
    status: 200,
    description: "Returns the email campaign",
    type: EmailCampaign,
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async findOne(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.findOne(id, user.id);
  }

  @Post()
  @ApiOperation({ summary: "Create a new email campaign" })
  @ApiResponse({
    status: 201,
    description: "Campaign created successfully",
    type: EmailCampaign,
  })
  async create(
    @Body() createCampaignDto: CreateCampaignDto,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.create(user.id, createCampaignDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update an existing email campaign" })
  @ApiResponse({
    status: 200,
    description: "Campaign updated successfully",
    type: EmailCampaign,
  })
  async update(
    @Param("id") id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.update(id, user.id, updateCampaignDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete an email campaign" })
  @ApiResponse({
    status: 200,
    description: "Campaign deleted successfully",
    schema: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          example: true,
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async delete(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<{ success: boolean }> {
    try {
      await this.campaignService.delete(id, user.id);
      return { success: true };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post(":id/start")
  @ApiOperation({ summary: "Start an email campaign" })
  @ApiResponse({
    status: 200,
    description: "Campaign started successfully",
    type: EmailCampaign,
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async startCampaign(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.startCampaign(id, user.id);
  }

  @Post(":id/pause")
  @ApiOperation({ summary: "Pause an email campaign" })
  @ApiResponse({
    status: 200,
    description: "Campaign paused successfully",
    type: EmailCampaign,
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async pauseCampaign(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.pauseCampaign(id, user.id);
  }

  @Post(":id/resume")
  @ApiOperation({ summary: "Resume a paused email campaign" })
  @ApiResponse({
    status: 200,
    description: "Campaign resumed successfully",
    type: EmailCampaign,
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async resumeCampaign(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.resumeCampaign(id, user.id);
  }

  @Post(":id/complete")
  @ApiOperation({ summary: "Mark an email campaign as complete" })
  @ApiResponse({
    status: 200,
    description: "Campaign marked as complete",
    type: EmailCampaign,
  })
  @ApiResponse({ status: 404, description: "Campaign not found" })
  async completeCampaign(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailCampaign> {
    return this.campaignService.completeCampaign(id, user.id);
  }
}
