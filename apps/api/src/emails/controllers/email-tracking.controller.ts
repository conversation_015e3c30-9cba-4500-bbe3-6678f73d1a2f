import {
  <PERSON>,
  Get,
  Param,
  Query,
  Req,
  Res,
  HttpStatus,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { EmailsService } from '../emails.service';
import { Public } from '../../auth/decorators/public.decorator';

@Controller('track')
export class EmailTrackingController {
  constructor(private readonly emailsService: EmailsService) {}

  @Public()
  @Get('open/:id')
  async trackOpen(
    @Param('id') id: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Track the email open
      await this.emailsService.trackEmailOpen(id, {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip,
        referrer: req.headers.referer,
      });

      // Return a transparent 1x1 pixel GIF
      const transparentPixel = Buffer.from(
        'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        'base64',
      );
      
      res.setHeader('Content-Type', 'image/gif');
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.end(transparentPixel);
    } catch (error) {
      // Still return the pixel even if tracking fails
      const transparentPixel = Buffer.from(
        'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        'base64',
      );
      
      res.setHeader('Content-Type', 'image/gif');
      res.end(transparentPixel);
    }
  }

  @Public()
  @Get('click/:id')
  async trackClick(
    @Param('id') id: string,
    @Query('url') url: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Track the email click
      await this.emailsService.trackEmailClick(id, url, {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip,
        referrer: req.headers.referer,
      });

      // Redirect to the original URL
      res.redirect(HttpStatus.FOUND, url);
    } catch (error) {
      // Redirect to the original URL even if tracking fails
      res.redirect(HttpStatus.FOUND, url || '/');
    }
  }
}
