import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { ConfigService } from "@nestjs/config";
import { <PERSON><PERSON> } from "@nestjs/schedule";
import { EmailTemplate } from "./entities/email-template.entity";
import { OutreachEmail } from "./entities/outreach-email.entity";
import { EmailClick } from "./entities/email-click.entity";
import { EmailCampaign } from "./entities/email-campaign.entity";
import { TargetDomainsService } from "../target-domains/target-domains.service";
import {
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
} from "./dto/email-template.dto";
import type {
  OutreachEmailCreateDto,
  OutreachEmailStatusDto,
} from "shared-types";

@Injectable()
export class EmailsService {
  constructor(
    @InjectRepository(EmailTemplate)
    private emailTemplatesRepository: Repository<EmailTemplate>,
    @InjectRepository(OutreachEmail)
    private outreachEmailsRepository: Repository<OutreachEmail>,
    @InjectRepository(EmailClick)
    private emailClicksRepository: Repository<EmailClick>,
    @InjectRepository(EmailCampaign)
    private emailCampaignsRepository: Repository<EmailCampaign>,
    @Inject(TargetDomainsService)
    private targetDomainsService: TargetDomainsService,
    @Inject(ConfigService)
    private configService: ConfigService
  ) {}

  // Email Templates
  async findAllTemplates(userId: string): Promise<EmailTemplate[]> {
    return this.emailTemplatesRepository.find({ where: { userId } });
  }

  async findTemplateById(id: string, userId: string): Promise<EmailTemplate> {
    const template = await this.emailTemplatesRepository.findOne({
      where: { id, userId },
    });
    if (!template) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }
    return template;
  }

  async createTemplate(
    userId: string,
    createDto: CreateEmailTemplateDto
  ): Promise<EmailTemplate> {
    const template = this.emailTemplatesRepository.create({
      ...createDto,
      userId,
    });
    return this.emailTemplatesRepository.save(template);
  }

  async updateTemplate(
    id: string,
    userId: string,
    updateDto: UpdateEmailTemplateDto
  ): Promise<EmailTemplate> {
    const template = await this.findTemplateById(id, userId);
    Object.assign(template, updateDto);
    return this.emailTemplatesRepository.save(template);
  }

  async deleteTemplate(id: string, userId: string): Promise<void> {
    const template = await this.findTemplateById(id, userId);
    await this.emailTemplatesRepository.remove(template);
  }

  // Outreach Emails
  async findAllOutreachEmails(userId: string): Promise<OutreachEmail[]> {
    return this.outreachEmailsRepository.find({
      where: { userId },
      relations: ["targetDomain"],
      order: { createdAt: "DESC" },
    });
  }

  async findOutreachEmailById(
    id: string,
    userId: string
  ): Promise<OutreachEmail> {
    const email = await this.outreachEmailsRepository.findOne({
      where: { id, userId },
      relations: ["targetDomain"],
    });
    if (!email) {
      throw new NotFoundException(`Outreach email with ID ${id} not found`);
    }
    return email;
  }

  async createOutreachEmail(
    userId: string,
    createDto: OutreachEmailCreateDto
  ): Promise<OutreachEmail[]> {
    const createdEmails: OutreachEmail[] = [];

    // Get template if provided
    let templateSubject = createDto.subject;
    let templateBody = createDto.body;

    if (createDto.templateId) {
      const template = await this.findTemplateById(
        createDto.templateId,
        userId
      );
      templateSubject = template.subject;
      templateBody = template.body;
    }

    // Process each target domain
    for (const targetDomainId of createDto.targetDomainIds) {
      const targetDomain = await this.targetDomainsService.findOne(
        targetDomainId,
        userId
      );

      // Replace placeholders in subject and body
      const subject = this.replacePlaceholders(templateSubject, targetDomain);
      const body = this.replacePlaceholders(templateBody, targetDomain);

      // Create the outreach email
      const email = this.outreachEmailsRepository.create({
        targetDomainId,
        userId,
        subject,
        body,
        recipient:
          targetDomain.contactEmail || `contact@${targetDomain.domain}`,
        status: "pending",
      });

      const savedEmail = await this.outreachEmailsRepository.save(email);
      createdEmails.push(savedEmail);

      // Send the email (in a real app, this would use a proper email service)
      this.sendEmail(savedEmail);
    }

    return createdEmails;
  }

  async updateOutreachEmailStatus(
    id: string,
    userId: string,
    statusDto: OutreachEmailStatusDto
  ): Promise<OutreachEmail> {
    const email = await this.findOutreachEmailById(id, userId);

    email.status = statusDto.status;
    if (statusDto.sentAt) email.sentAt = statusDto.sentAt;
    if (statusDto.openedAt) email.openedAt = statusDto.openedAt;

    return this.outreachEmailsRepository.save(email);
  }

  private replacePlaceholders(text: string, targetDomain: any): string {
    // Get user domain (in a real app, this would be fetched from the database)
    const userDomain = "yourdomain.com";
    const userName = "Your Name";
    const userCompany = "Your Company";
    const userPosition = "Marketing Manager";
    const userEmail = "<EMAIL>";
    const userPhone = "+****************";

    // Get current date
    const now = new Date();
    const currentDate = now.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    const currentMonth = now.toLocaleDateString("en-US", { month: "long" });
    const currentYear = now.getFullYear().toString();

    // Format target domain data
    const targetDomainName = targetDomain.domain;
    const targetWebsite = `https://${targetDomain.domain}`;
    const targetIndustry = targetDomain.industry || "your industry";
    const targetRelevanceScore = targetDomain.relevanceScore || "high";

    // Replace basic placeholders
    let result = text
      .replace(/{{domain}}/g, targetDomainName)
      .replace(/{{name}}/g, targetDomain.contactName || "Site Owner")
      .replace(
        /{{position}}/g,
        targetDomain.contactPosition || "Marketing Manager"
      )
      .replace(/{{company}}/g, targetDomainName.split(".")[0])
      .replace(/{{website}}/g, targetWebsite)
      .replace(/{{industry}}/g, targetIndustry)
      .replace(/{{relevance_score}}/g, targetRelevanceScore)

      // Sender information
      .replace(/{{sender_name}}/g, userName)
      .replace(/{{sender_domain}}/g, userDomain)
      .replace(/{{sender_company}}/g, userCompany)
      .replace(/{{sender_position}}/g, userPosition)
      .replace(/{{sender_email}}/g, userEmail)
      .replace(/{{sender_phone}}/g, userPhone)

      // Date information
      .replace(/{{date}}/g, currentDate)
      .replace(/{{month}}/g, currentMonth)
      .replace(/{{year}}/g, currentYear);

    // Replace conditional blocks
    // Format: {{if condition}}content{{endif}}
    const conditionalPattern = /{{if\s+([^}]+)}}([\s\S]*?){{endif}}/g;
    result = result.replace(conditionalPattern, (match, condition, content) => {
      // Check if condition is true
      if (this.evaluateCondition(condition, targetDomain)) {
        return content;
      }
      return "";
    });

    return result;
  }

  /**
   * Evaluate a simple condition for template variables
   */
  private evaluateCondition(condition: string, targetDomain: any): boolean {
    // Simple condition evaluation
    const parts = condition.trim().split(/\s+/);

    if (parts.length < 3) {
      return false;
    }

    const [variable, operator, value] = parts;

    // Get variable value
    let variableValue: any;

    if (variable.startsWith("domain.")) {
      const prop = variable.substring(7);
      variableValue = targetDomain[prop];
    } else if (variable === "has_contact_name") {
      variableValue = !!targetDomain.contactName;
    } else if (variable === "has_contact_email") {
      variableValue = !!targetDomain.contactEmail;
    } else {
      return false;
    }

    // Evaluate condition
    switch (operator) {
      case "==":
        return variableValue == value;
      case "!=":
        return variableValue != value;
      case ">":
        return variableValue > parseFloat(value);
      case "<":
        return variableValue < parseFloat(value);
      case ">=":
        return variableValue >= parseFloat(value);
      case "<=":
        return variableValue <= parseFloat(value);
      default:
        return false;
    }
  }

  private async sendEmail(email: OutreachEmail): Promise<void> {
    try {
      // In a real app, this would use a proper email service like SendGrid or Mailgun
      console.log(
        `Sending email to ${email.recipient} with subject: ${email.subject}`
      );

      // Check if email body is HTML
      const isHtml = this.isHtmlContent(email.body);

      // Add tracking pixel if HTML email
      let emailBody = email.body;
      if (isHtml) {
        // Add tracking pixel at the end of the HTML body
        const trackingId = email.id;
        const trackingPixel = `<img src="${this.getTrackingUrl(
          trackingId
        )}" width="1" height="1" alt="" style="display:none;" />`;

        // Insert before closing body tag or at the end
        if (emailBody.includes("</body>")) {
          emailBody = emailBody.replace("</body>", `${trackingPixel}</body>`);
        } else {
          emailBody += trackingPixel;
        }

        // Add click tracking to links
        emailBody = this.addClickTracking(emailBody, trackingId);
      }

      // Prepare email data for sending
      const emailData = {
        to: email.recipient,
        from: "<EMAIL>",
        subject: email.subject,
        text: isHtml ? this.convertHtmlToText(emailBody) : emailBody,
        html: isHtml ? emailBody : null,
        trackingSettings: {
          clickTracking: { enable: true },
          openTracking: { enable: true },
        },
      };

      // Log email data for debugging
      console.log("Email data:", JSON.stringify(emailData, null, 2));

      // Simulate sending delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update email status to sent
      email.status = "sent";
      email.sentAt = new Date();
      await this.outreachEmailsRepository.save(email);

      // Simulate email open after some time (for demo purposes)
      if (Math.random() > 0.3) {
        setTimeout(async () => {
          email.status = "opened";
          email.openedAt = new Date();
          await this.outreachEmailsRepository.save(email);
        }, Math.random() * 10000 + 5000);
      }
    } catch (error) {
      console.error("Error sending email:", error);

      // Update email status to failed
      email.status = "failed";
      await this.outreachEmailsRepository.save(email);
    }
  }

  /**
   * Check if content is HTML
   */
  private isHtmlContent(content: string): boolean {
    // Check for common HTML tags
    return /<\/?[a-z][\s\S]*>/i.test(content);
  }

  /**
   * Get tracking pixel URL
   */
  private getTrackingUrl(emailId: string): string {
    // In a real app, this would be a URL to your tracking endpoint
    return `https://yourdomain.com/api/track/open/${emailId}`;
  }

  /**
   * Add click tracking to links in HTML content
   */
  private addClickTracking(html: string, emailId: string): string {
    // Replace all links with tracking links
    return html.replace(
      /<a\s+(?:[^>]*?\s+)?href=["']([^"']*)["']([^>]*)>/gi,
      (match, url, rest) => {
        // Skip if already a tracking link
        if (url.includes("/api/track/click/")) {
          return match;
        }

        // Create tracking URL
        const trackingUrl = `https://yourdomain.com/api/track/click/${emailId}?url=${encodeURIComponent(
          url
        )}`;

        // Return updated link
        return `<a href="${trackingUrl}"${rest}>`;
      }
    );
  }

  /**
   * Convert HTML to plain text
   */
  private convertHtmlToText(html: string): string {
    // Simple HTML to text conversion
    return html
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
      .replace(/<[^>]+>/g, "")
      .replace(/\s+/g, " ")
      .trim();
  }

  /**
   * Track email open
   */
  async trackEmailOpen(
    emailId: string,
    metadata: { userAgent?: string; ipAddress?: string; referrer?: string }
  ): Promise<void> {
    try {
      // Find the email
      const email = await this.outreachEmailsRepository.findOne({
        where: { id: emailId },
      });

      if (!email) {
        console.error(`Email with ID ${emailId} not found for tracking open`);
        return;
      }

      // Update email status if not already opened
      if (
        email.status !== "opened" &&
        email.status !== "clicked" &&
        email.status !== "replied"
      ) {
        email.status = "opened";
        email.openedAt = new Date();
        await this.outreachEmailsRepository.save(email);
      }

      // Increment open count
      email.openCount += 1;
      await this.outreachEmailsRepository.save(email);

      console.log(`Tracked open for email ${emailId}`);
    } catch (error) {
      console.error(`Error tracking email open: ${error.message}`);
    }
  }

  /**
   * Track email click
   */
  async trackEmailClick(
    emailId: string,
    url: string,
    metadata: { userAgent?: string; ipAddress?: string; referrer?: string }
  ): Promise<void> {
    try {
      // Find the email
      const email = await this.outreachEmailsRepository.findOne({
        where: { id: emailId },
      });

      if (!email) {
        console.error(`Email with ID ${emailId} not found for tracking click`);
        return;
      }

      // Check if this URL has been clicked before by this email
      const existingClick = await this.emailClicksRepository.findOne({
        where: { emailId, url },
      });

      // Create a new click record
      const click = this.emailClicksRepository.create({
        emailId,
        url,
        userAgent: metadata.userAgent,
        ipAddress: metadata.ipAddress,
        referrer: metadata.referrer,
        isUnique: !existingClick,
      });

      await this.emailClicksRepository.save(click);

      // Update email status if not already clicked or replied
      if (email.status !== "clicked" && email.status !== "replied") {
        email.status = "clicked";
        email.clickedAt = new Date();
      }

      // Increment click count
      email.clickCount += 1;
      await this.outreachEmailsRepository.save(email);

      console.log(`Tracked click for email ${emailId} to URL ${url}`);
    } catch (error) {
      console.error(`Error tracking email click: ${error.message}`);
    }
  }

  /**
   * Schedule an email for future delivery
   */
  async scheduleEmail(
    id: string,
    userId: string,
    scheduledFor: Date
  ): Promise<OutreachEmail> {
    const email = await this.findOutreachEmailById(id, userId);

    // Ensure the scheduled time is in the future
    const now = new Date();
    if (scheduledFor <= now) {
      throw new Error("Scheduled time must be in the future");
    }

    // Update email status and scheduled time
    email.status = "scheduled";
    email.scheduledFor = scheduledFor;

    return this.outreachEmailsRepository.save(email);
  }

  // Campaign methods
  async findAllCampaigns(userId: string) {
    return this.emailCampaignsRepository.find({
      where: { userId },
      order: { createdAt: "DESC" },
    });
  }

  async findCampaignById(id: string, userId: string) {
    const campaign = await this.emailCampaignsRepository.findOne({
      where: { id, userId },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    return campaign;
  }

  /**
   * Process scheduled emails that are due to be sent
   * Runs every minute via cron job
   */
  @Cron("0 * * * * *") // Run every minute
  async processScheduledEmails(): Promise<void> {
    try {
      const now = new Date();

      // Find all emails scheduled for now or earlier
      const scheduledEmails = await this.outreachEmailsRepository.find({
        where: {
          status: "scheduled",
          scheduledFor: now ? new Date(now.getTime()) : undefined,
        },
      });

      if (scheduledEmails.length > 0) {
        console.log(`Processing ${scheduledEmails.length} scheduled emails`);

        // Send each email
        for (const email of scheduledEmails) {
          await this.sendEmail(email);
        }
      }
    } catch (error) {
      console.error(`Error processing scheduled emails: ${error.message}`);
    }
  }
}
