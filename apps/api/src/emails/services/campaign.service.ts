import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { <PERSON>ron } from "@nestjs/schedule";
import {
  EmailCampaign,
  CampaignStatus,
  CampaignType,
} from "../entities/email-campaign.entity";
import { OutreachEmail } from "../entities/outreach-email.entity";
import { EmailsService } from "../emails.service";
import { TargetDomainsService } from "../../target-domains/target-domains.service";

interface CreateCampaignDto {
  name: string;
  description?: string;
  type: CampaignType;
  settings: {
    sendingInterval?: number;
    maxEmailsPerDay?: number;
    followUpDelay?: number;
    abTestVariants?: string[];
    abTestSplitRatio?: number[];
    targetDomainIds: string[];
    templateIds: string[];
    sendingTimeWindow?: {
      startHour: number;
      endHour: number;
      timezone: string;
      daysOfWeek: number[];
    };
  };
}

interface UpdateCampaignDto {
  name?: string;
  description?: string;
  status?: CampaignStatus;
  settings?: Partial<EmailCampaign["settings"]>;
}

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @InjectRepository(EmailCampaign)
    private campaignRepository: Repository<EmailCampaign>,
    @InjectRepository(OutreachEmail)
    private outreachEmailRepository: Repository<OutreachEmail>,
    private emailsService: EmailsService,
    private targetDomainsService: TargetDomainsService
  ) {}

  /**
   * Find all campaigns for a user
   */
  async findAll(userId: string): Promise<EmailCampaign[]> {
    return this.campaignRepository.find({
      where: { userId },
      order: { createdAt: "DESC" },
    });
  }

  /**
   * Find a campaign by ID
   */
  async findOne(id: string, userId: string): Promise<EmailCampaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { id, userId },
      relations: ["emails"],
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    return campaign;
  }

  /**
   * Create a new campaign
   */
  async create(
    userId: string,
    createDto: CreateCampaignDto
  ): Promise<EmailCampaign> {
    const campaign = this.campaignRepository.create({
      ...createDto,
      userId,
      status: "draft",
      totalEmails: 0,
      sentEmails: 0,
      openedEmails: 0,
      clickedEmails: 0,
      repliedEmails: 0,
      bouncedEmails: 0,
      analytics: {
        openRate: 0,
        clickRate: 0,
        replyRate: 0,
        bounceRate: 0,
      },
    });

    return this.campaignRepository.save(campaign);
  }

  /**
   * Update a campaign
   */
  async update(
    id: string,
    userId: string,
    updateDto: UpdateCampaignDto
  ): Promise<EmailCampaign> {
    const campaign = await this.findOne(id, userId);

    // Update campaign properties
    if (updateDto.name) campaign.name = updateDto.name;
    if (updateDto.description) campaign.description = updateDto.description;
    if (updateDto.status) {
      // Handle status transitions
      if (updateDto.status === "active" && campaign.status === "draft") {
        campaign.startDate = new Date();
      } else if (
        updateDto.status === "completed" &&
        campaign.status === "active"
      ) {
        campaign.endDate = new Date();
      }
      campaign.status = updateDto.status;
    }

    // Update settings if provided
    if (updateDto.settings) {
      campaign.settings = {
        ...campaign.settings,
        ...updateDto.settings,
      };
    }

    return this.campaignRepository.save(campaign);
  }

  /**
   * Delete a campaign
   */
  async delete(id: string, userId: string): Promise<void> {
    const campaign = await this.findOne(id, userId);
    await this.campaignRepository.remove(campaign);
  }

  /**
   * Start a campaign
   */
  async startCampaign(id: string, userId: string): Promise<EmailCampaign> {
    const campaign = await this.findOne(id, userId);

    if (campaign.status !== "draft" && campaign.status !== "paused") {
      throw new Error(
        `Campaign cannot be started from ${campaign.status} status`
      );
    }

    // Set campaign to active
    campaign.status = "active";
    campaign.startDate = new Date();

    // For one-time campaigns, create all emails immediately
    if (campaign.type === "one-time") {
      await this.createCampaignEmails(campaign);
    }

    return this.campaignRepository.save(campaign);
  }

  /**
   * Pause a campaign
   */
  async pauseCampaign(id: string, userId: string): Promise<EmailCampaign> {
    const campaign = await this.findOne(id, userId);

    if (campaign.status !== "active") {
      throw new Error("Only active campaigns can be paused");
    }

    campaign.status = "paused";
    return this.campaignRepository.save(campaign);
  }

  /**
   * Resume a paused campaign
   */
  async resumeCampaign(id: string, userId: string): Promise<EmailCampaign> {
    const campaign = await this.findOne(id, userId);

    if (campaign.status !== "paused") {
      throw new Error("Only paused campaigns can be resumed");
    }

    campaign.status = "active";
    return this.campaignRepository.save(campaign);
  }

  /**
   * Complete a campaign
   */
  async completeCampaign(id: string, userId: string): Promise<EmailCampaign> {
    const campaign = await this.findOne(id, userId);

    if (campaign.status !== "active" && campaign.status !== "paused") {
      throw new Error("Only active or paused campaigns can be completed");
    }

    campaign.status = "completed";
    campaign.endDate = new Date();
    return this.campaignRepository.save(campaign);
  }

  /**
   * Create emails for a campaign
   */
  private async createCampaignEmails(campaign: EmailCampaign): Promise<void> {
    try {
      const { targetDomainIds, templateIds } = campaign.settings;

      if (
        !targetDomainIds ||
        !templateIds ||
        targetDomainIds.length === 0 ||
        templateIds.length === 0
      ) {
        throw new Error("Campaign must have target domains and templates");
      }

      // Get the primary template (or handle A/B testing)
      const templateId = templateIds[0];

      // Create an email for each target domain
      for (const targetDomainId of targetDomainIds) {
        const targetDomain = await this.targetDomainsService.findOne(
          targetDomainId,
          campaign.userId
        );

        // Create the email using the EmailsService
        const email = await this.emailsService.createOutreachEmail(
          campaign.userId,
          {
            targetDomainIds: [targetDomainId],
            templateId,
            subject: "", // Will be populated from template
            body: "", // Will be populated from template
          }
        );

        // Update the campaign ID for the created emails
        if (email && email.length > 0) {
          for (const createdEmail of email) {
            createdEmail.campaignId = campaign.id;
            await this.outreachEmailRepository.save(createdEmail);
          }
        }

        // Increment total emails count
        campaign.totalEmails++;
      }

      // Save the updated campaign
      await this.campaignRepository.save(campaign);
    } catch (error) {
      this.logger.error(
        `Error creating campaign emails: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Process campaigns (run by cron job)
   */
  @Cron("0 */15 * * * *") // Run every 15 minutes
  async processCampaigns(): Promise<void> {
    try {
      // Find all active campaigns
      const activeCampaigns = await this.campaignRepository.find({
        where: { status: "active" },
      });

      this.logger.log(`Processing ${activeCampaigns.length} active campaigns`);

      for (const campaign of activeCampaigns) {
        await this.processCampaign(campaign);
      }
    } catch (error) {
      this.logger.error(
        `Error processing campaigns: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Process a single campaign
   */
  private async processCampaign(campaign: EmailCampaign): Promise<void> {
    try {
      // Handle different campaign types
      switch (campaign.type) {
        case "one-time":
          await this.processOneTimeCampaign(campaign);
          break;
        case "drip":
          await this.processDripCampaign(campaign);
          break;
        case "follow-up":
          await this.processFollowUpCampaign(campaign);
          break;
        case "ab-test":
          await this.processAbTestCampaign(campaign);
          break;
      }

      // Update campaign analytics
      await this.updateCampaignAnalytics(campaign);
    } catch (error) {
      this.logger.error(
        `Error processing campaign ${campaign.id}: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Process a one-time campaign
   */
  private async processOneTimeCampaign(campaign: EmailCampaign): Promise<void> {
    // For one-time campaigns, check if all emails have been sent
    const pendingEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "pending",
      },
    });

    if (pendingEmails === 0) {
      // All emails have been sent, check if campaign should be completed
      const totalEmails = await this.outreachEmailRepository.count({
        where: {
          campaignId: campaign.id,
        },
      });

      if (totalEmails > 0 && campaign.sentEmails === totalEmails) {
        // All emails have been sent, mark campaign as completed
        campaign.status = "completed";
        campaign.endDate = new Date();
        await this.campaignRepository.save(campaign);
      }
    }
  }

  /**
   * Process a drip campaign
   */
  private async processDripCampaign(campaign: EmailCampaign): Promise<void> {
    // Implementation for drip campaigns
    // This would create new emails over time based on the campaign settings
  }

  /**
   * Process a follow-up campaign
   */
  private async processFollowUpCampaign(
    campaign: EmailCampaign
  ): Promise<void> {
    // Implementation for follow-up campaigns
    // This would create follow-up emails based on previous email interactions
  }

  /**
   * Process an A/B test campaign
   */
  private async processAbTestCampaign(campaign: EmailCampaign): Promise<void> {
    // Implementation for A/B test campaigns
    // This would track performance of different email variants
  }

  /**
   * Update campaign analytics
   */
  private async updateCampaignAnalytics(
    campaign: EmailCampaign
  ): Promise<void> {
    // Get email statistics
    const totalEmails = await this.outreachEmailRepository.count({
      where: { campaignId: campaign.id },
    });

    const sentEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "sent",
      },
    });

    const openedEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "opened",
      },
    });

    const clickedEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "clicked",
      },
    });

    const repliedEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "replied",
      },
    });

    const bouncedEmails = await this.outreachEmailRepository.count({
      where: {
        campaignId: campaign.id,
        status: "bounced",
      },
    });

    // Update campaign statistics
    campaign.totalEmails = totalEmails;
    campaign.sentEmails = sentEmails;
    campaign.openedEmails = openedEmails;
    campaign.clickedEmails = clickedEmails;
    campaign.repliedEmails = repliedEmails;
    campaign.bouncedEmails = bouncedEmails;

    // Calculate rates
    campaign.analytics = {
      openRate: sentEmails > 0 ? (openedEmails / sentEmails) * 100 : 0,
      clickRate: openedEmails > 0 ? (clickedEmails / openedEmails) * 100 : 0,
      replyRate: sentEmails > 0 ? (repliedEmails / sentEmails) * 100 : 0,
      bounceRate: sentEmails > 0 ? (bouncedEmails / sentEmails) * 100 : 0,
    };

    await this.campaignRepository.save(campaign);
  }
}
