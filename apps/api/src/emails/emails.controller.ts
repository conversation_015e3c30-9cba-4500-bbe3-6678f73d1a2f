import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Inject,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { EmailsService } from "./emails.service";
import { EmailTemplate } from "./entities/email-template.entity";
import { OutreachEmail } from "./entities/outreach-email.entity";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { GetUser } from "../auth/decorators/get-user.decorator";
import { User } from "../users/entities/user.entity";
import {
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
} from "./dto/email-template.dto";
import type {
  OutreachEmailCreateDto,
  OutreachEmailStatusDto,
} from "shared-types";

@ApiTags("emails")
@Controller("emails")
@ApiBearerAuth("JWT-auth")
export class EmailsController {
  constructor(
    @Inject(EmailsService) private readonly emailsService: EmailsService
  ) {}

  // Email Templates
  @Get("templates")
  @ApiOperation({ summary: "Get all email templates for the current user" })
  @ApiResponse({ status: 200, description: "Returns all email templates" })
  findAllTemplates(@GetUser() user: User): Promise<EmailTemplate[]> {
    return this.emailsService.findAllTemplates(user.id);
  }

  @Get("templates/:id")
  @ApiOperation({ summary: "Get an email template by ID" })
  @ApiResponse({ status: 200, description: "Returns the email template" })
  @ApiResponse({ status: 404, description: "Email template not found" })
  findTemplateById(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<EmailTemplate> {
    return this.emailsService.findTemplateById(id, user.id);
  }

  @Post("templates")
  @ApiOperation({ summary: "Create a new email template" })
  @ApiResponse({
    status: 201,
    description: "Email template created successfully",
    type: EmailTemplate,
  })
  createTemplate(
    @Body() createDto: CreateEmailTemplateDto,
    @GetUser() user: User
  ): Promise<EmailTemplate> {
    return this.emailsService.createTemplate(user.id, createDto);
  }

  @Patch("templates/:id")
  @ApiOperation({ summary: "Update an email template" })
  @ApiResponse({
    status: 200,
    description: "Email template updated successfully",
    type: EmailTemplate,
  })
  @ApiResponse({ status: 404, description: "Email template not found" })
  updateTemplate(
    @Param("id") id: string,
    @Body() updateDto: UpdateEmailTemplateDto,
    @GetUser() user: User
  ): Promise<EmailTemplate> {
    return this.emailsService.updateTemplate(id, user.id, updateDto);
  }

  @Delete("templates/:id")
  @ApiOperation({ summary: "Delete an email template" })
  @ApiResponse({
    status: 200,
    description: "Email template deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Email template not found" })
  deleteTemplate(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<void> {
    return this.emailsService.deleteTemplate(id, user.id);
  }

  // Outreach Emails
  @Get("outreach")
  @ApiOperation({ summary: "Get all outreach emails for the current user" })
  @ApiResponse({ status: 200, description: "Returns all outreach emails" })
  findAllOutreachEmails(@GetUser() user: User): Promise<OutreachEmail[]> {
    return this.emailsService.findAllOutreachEmails(user.id);
  }

  @Get("outreach/:id")
  @ApiOperation({ summary: "Get an outreach email by ID" })
  @ApiResponse({ status: 200, description: "Returns the outreach email" })
  @ApiResponse({ status: 404, description: "Outreach email not found" })
  findOutreachEmailById(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<OutreachEmail> {
    return this.emailsService.findOutreachEmailById(id, user.id);
  }

  @Post("outreach")
  @ApiOperation({ summary: "Create and send outreach emails" })
  @ApiResponse({
    status: 201,
    description: "Outreach emails created and sent successfully",
  })
  createOutreachEmail(
    @Body() createDto: OutreachEmailCreateDto,
    @GetUser() user: User
  ): Promise<OutreachEmail[]> {
    return this.emailsService.createOutreachEmail(user.id, createDto);
  }

  @Patch("outreach/:id/status")
  @ApiOperation({ summary: "Update outreach email status" })
  @ApiResponse({
    status: 200,
    description: "Outreach email status updated successfully",
  })
  @ApiResponse({ status: 404, description: "Outreach email not found" })
  updateOutreachEmailStatus(
    @Param("id") id: string,
    @Body() statusDto: OutreachEmailStatusDto,
    @GetUser() user: User
  ): Promise<OutreachEmail> {
    return this.emailsService.updateOutreachEmailStatus(id, user.id, statusDto);
  }
}
