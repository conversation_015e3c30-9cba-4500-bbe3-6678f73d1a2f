import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsArra<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEmailTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Initial Outreach',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Email subject line',
    example: 'Collaboration opportunity with {{domain}}',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Email body content',
    example: 'Dear {{name}},\n\nI came across your website {{domain}} and was impressed by your content.',
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiPropertyOptional({
    description: 'Custom variables used in the template',
    example: ['signature', 'company', 'offer'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  variables?: string[];
}

export class UpdateEmailTemplateDto {
  @ApiPropertyOptional({
    description: 'Template name',
    example: 'Updated Outreach Template',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Email subject line',
    example: 'Updated: Collaboration opportunity with {{domain}}',
  })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiPropertyOptional({
    description: 'Email body content',
    example: 'Dear {{name}},\n\nI came across your website {{domain}} and was impressed by your content.',
  })
  @IsString()
  @IsOptional()
  body?: string;

  @ApiPropertyOptional({
    description: 'Custom variables used in the template',
    example: ['signature', 'company', 'offer'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  variables?: string[];
}
