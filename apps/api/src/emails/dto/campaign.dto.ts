import { IsS<PERSON>, <PERSON>NotEmpty, IsO<PERSON>al, IsArray, IsEnum, IsUUID, IsDateString, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum CampaignStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum CampaignType {
  ONE_TIME = 'one-time',
  DRIP = 'drip',
  FOLLOW_UP = 'follow-up',
  AB_TEST = 'ab-test',
}

export class SendingTimeWindowDto {
  @ApiProperty({ description: 'Start hour (0-23)', example: 9 })
  startHour: number;

  @ApiProperty({ description: 'End hour (0-23)', example: 17 })
  endHour: number;

  @ApiProperty({ description: 'Timezone', example: 'America/New_York' })
  timezone: string;

  @ApiProperty({ 
    description: 'Days of week (0 = Sunday, 6 = Saturday)', 
    example: [1, 2, 3, 4, 5],
    type: [Number]
  })
  daysOfWeek: number[];
}

export class CampaignSettingsDto {
  @ApiProperty({
    description: 'Array of target domain IDs to include in this campaign',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  targetDomainIds: string[];

  @ApiProperty({
    description: 'Array of email template IDs to use for this campaign',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  templateIds: string[];

  @ApiPropertyOptional({
    description: 'Time between emails in minutes',
    example: 60,
  })
  sendingInterval?: number;

  @ApiPropertyOptional({
    description: 'Maximum emails to send per day',
    example: 50,
  })
  maxEmailsPerDay?: number;

  @ApiPropertyOptional({
    description: 'Days to wait before follow-up',
    example: 3,
  })
  followUpDelay?: number;

  @ApiPropertyOptional({
    description: 'Template IDs for A/B testing',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  abTestVariants?: string[];

  @ApiPropertyOptional({
    description: 'Split ratio for A/B testing',
    example: [0.5, 0.5],
    type: [Number],
  })
  abTestSplitRatio?: number[];

  @ApiPropertyOptional({
    description: 'Sending time window',
    type: SendingTimeWindowDto,
  })
  @ValidateNested()
  @Type(() => SendingTimeWindowDto)
  sendingTimeWindow?: SendingTimeWindowDto;
}

export class CreateCampaignDto {
  @ApiProperty({
    description: 'Campaign name',
    example: 'Summer Outreach 2023',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Campaign description',
    example: 'Outreach campaign for summer 2023 targeting tech blogs',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Campaign type',
    enum: CampaignType,
    example: CampaignType.ONE_TIME,
  })
  @IsEnum(CampaignType)
  @IsNotEmpty()
  type: CampaignType;

  @ApiProperty({
    description: 'Campaign settings',
    type: CampaignSettingsDto,
  })
  @ValidateNested()
  @Type(() => CampaignSettingsDto)
  settings: CampaignSettingsDto;
}

export class UpdateCampaignDto {
  @ApiPropertyOptional({
    description: 'Campaign name',
    example: 'Summer Outreach 2023 - Updated',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Campaign description',
    example: 'Updated outreach campaign for summer 2023',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Campaign status',
    enum: CampaignStatus,
    example: CampaignStatus.ACTIVE,
  })
  @IsEnum(CampaignStatus)
  @IsOptional()
  status?: CampaignStatus;

  @ApiPropertyOptional({
    description: 'Campaign settings',
    type: CampaignSettingsDto,
  })
  @ValidateNested()
  @Type(() => CampaignSettingsDto)
  @IsOptional()
  settings?: Partial<CampaignSettingsDto>;
}
