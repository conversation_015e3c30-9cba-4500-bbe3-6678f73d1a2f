import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { EmailsService } from "./emails.service";
import { EmailsController } from "./emails.controller";
import { EmailTrackingController } from "./controllers/email-tracking.controller";
import { CampaignController } from "./controllers/campaign.controller";
import { EmailTemplate } from "./entities/email-template.entity";
import { OutreachEmail } from "./entities/outreach-email.entity";
import { EmailClick } from "./entities/email-click.entity";
import { EmailCampaign } from "./entities/email-campaign.entity";
import { CampaignService } from "./services/campaign.service";
import { TargetDomainsModule } from "../target-domains/target-domains.module";
import { ConfigModule } from "@nestjs/config";
import { ScheduleModule } from "@nestjs/schedule";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EmailTemplate,
      OutreachEmail,
      EmailClick,
      EmailCampaign,
    ]),
    TargetDomainsModule,
    ConfigModule,
    ScheduleModule.forRoot(),
  ],
  providers: [EmailsService, CampaignService],
  controllers: [EmailsController, EmailTrackingController, CampaignController],
  exports: [EmailsService, CampaignService],
})
export class EmailsModule {}
