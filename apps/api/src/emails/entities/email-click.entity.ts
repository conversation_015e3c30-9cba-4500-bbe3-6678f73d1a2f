import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { OutreachEmail } from "./outreach-email.entity";

@Entity("email_clicks")
export class EmailClick {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column("uuid")
  emailId: string;

  @Column()
  url: string;

  @Column({ nullable: true })
  userAgent?: string;

  @Column({ nullable: true })
  ipAddress?: string;

  @Column({ nullable: true })
  referrer?: string;

  @Column({ default: false })
  isUnique: boolean;

  @ManyToOne(() => OutreachEmail, (email) => email.clicks)
  @JoinColumn({ name: "emailId" })
  email: OutreachEmail;

  @CreateDateColumn()
  createdAt: Date;
}
