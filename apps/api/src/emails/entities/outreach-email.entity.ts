import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { TargetDomain } from "../../target-domains/entities/target-domain.entity";
import { EmailClick } from "./email-click.entity";
import { EmailCampaign } from "./email-campaign.entity";

@Entity("outreach_emails")
export class OutreachEmail {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column("uuid")
  targetDomainId: string;

  @Column("uuid")
  userId: string;

  @Column({ nullable: true, type: "uuid" })
  campaignId: string;

  @Column()
  subject: string;

  @Column({ type: "text" })
  body: string;

  @Column()
  recipient: string;

  @Column({
    enum: [
      "draft",
      "scheduled",
      "pending",
      "sent",
      "opened",
      "clicked",
      "replied",
      "bounced",
      "failed",
    ],
    default: "draft",
  })
  status:
    | "draft"
    | "scheduled"
    | "pending"
    | "sent"
    | "opened"
    | "clicked"
    | "replied"
    | "bounced"
    | "failed";

  @Column({ default: false })
  isHtml: boolean;

  @Column({ nullable: true })
  scheduledFor?: Date;

  @Column({ nullable: true })
  sentAt?: Date;

  @Column({ nullable: true })
  openedAt?: Date;

  @Column({ nullable: true })
  clickedAt?: Date;

  @Column({ nullable: true })
  repliedAt?: Date;

  @Column({ default: 0 })
  openCount: number;

  @Column({ default: 0 })
  clickCount: number;

  @Column({ nullable: true })
  templateId?: string;

  @Column({ nullable: true, type: "text" })
  notes?: string;

  @ManyToOne(() => User, (user) => user.outreachEmails)
  @JoinColumn({ name: "userId" })
  user: User;

  @ManyToOne(() => TargetDomain, (targetDomain) => targetDomain.outreachEmails)
  @JoinColumn({ name: "targetDomainId" })
  targetDomain: TargetDomain;

  @OneToMany(() => EmailClick, (emailClick) => emailClick.email)
  clicks: EmailClick[];

  @ManyToOne(() => EmailCampaign, (campaign) => campaign.emails)
  @JoinColumn({ name: "campaignId" })
  campaign: EmailCampaign;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
