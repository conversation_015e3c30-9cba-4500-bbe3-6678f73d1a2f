import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { OutreachEmail } from "./outreach-email.entity";

export type CampaignStatus = 
  | "draft" 
  | "active" 
  | "paused" 
  | "completed" 
  | "failed";

export type CampaignType = 
  | "one-time" 
  | "drip" 
  | "follow-up" 
  | "ab-test";

@Entity("email_campaigns")
export class EmailCampaign {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column("uuid")
  userId: string;

  @Column()
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({
    type: "enum",
    enum: ["draft", "active", "paused", "completed", "failed"],
    default: "draft",
  })
  status: CampaignStatus;

  @Column({
    type: "enum",
    enum: ["one-time", "drip", "follow-up", "ab-test"],
    default: "one-time",
  })
  type: CampaignType;

  @Column({ nullable: true })
  startDate: Date;

  @Column({ nullable: true })
  endDate: Date;

  @Column({ default: 0 })
  totalEmails: number;

  @Column({ default: 0 })
  sentEmails: number;

  @Column({ default: 0 })
  openedEmails: number;

  @Column({ default: 0 })
  clickedEmails: number;

  @Column({ default: 0 })
  repliedEmails: number;

  @Column({ default: 0 })
  bouncedEmails: number;

  @Column({ type: "jsonb", nullable: true })
  settings: {
    sendingInterval?: number; // Time between emails in minutes
    maxEmailsPerDay?: number;
    followUpDelay?: number; // Days to wait before follow-up
    abTestVariants?: string[]; // Template IDs for A/B testing
    abTestSplitRatio?: number[]; // e.g., [0.5, 0.5] for 50/50 split
    targetDomainIds?: string[]; // Target domains for this campaign
    templateIds?: string[]; // Email templates used in this campaign
    sendingTimeWindow?: {
      startHour: number;
      endHour: number;
      timezone: string;
      daysOfWeek: number[]; // 0 = Sunday, 6 = Saturday
    };
  };

  @Column({ type: "jsonb", nullable: true })
  analytics: {
    openRate?: number;
    clickRate?: number;
    replyRate?: number;
    bounceRate?: number;
    bestPerformingTemplate?: string;
    bestPerformingSubject?: string;
    bestSendingTime?: string;
    conversionRate?: number;
  };

  @ManyToOne(() => User, (user) => user.emailCampaigns)
  @JoinColumn({ name: "userId" })
  user: User;

  @OneToMany(() => OutreachEmail, (email) => email.campaign)
  emails: OutreachEmail[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
