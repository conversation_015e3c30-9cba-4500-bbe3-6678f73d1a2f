import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { Reflector } from "@nestjs/core";
import { IS_PUBLIC_KEY } from "../decorators/public.decorator";
import { Observable } from "rxjs";
import { catchError } from "rxjs/operators";

@Injectable()
export class JwtAuthGuard extends AuthGuard("jwt") {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Get the result from the parent canActivate method
    const result = super.canActivate(context);

    // Handle different return types
    if (result instanceof Observable) {
      // If it's an Observable, we can use pipe
      return result.pipe(
        catchError((error) => {
          if (error instanceof Error) {
            throw new UnauthorizedException("Invalid or expired token");
          }
          throw error;
        })
      );
    } else if (result instanceof Promise) {
      // If it's a Promise, convert to async/await
      return this.handlePromiseResult(result);
    }

    // If it's a boolean, just return it
    return result;
  }

  // Helper method to handle Promise results
  private async handlePromiseResult(
    result: Promise<boolean>
  ): Promise<boolean> {
    try {
      return await result;
    } catch (error) {
      if (error instanceof Error) {
        throw new UnauthorizedException("Invalid or expired token");
      }
      throw error;
    }
  }

  // Override handleRequest to provide better error messages
  handleRequest(err: any, user: any, info: any) {
    if (err || !user) {
      throw new UnauthorizedException(
        err?.message || info?.message || "Invalid or expired token"
      );
    }
    return user;
  }
}
