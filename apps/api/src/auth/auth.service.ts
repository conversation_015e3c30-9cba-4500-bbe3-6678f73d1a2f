import { Inject, Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcrypt";
import { UsersService } from "../users/users.service";
import type { User } from "../users/entities/user.entity";
import type { UserCreateDto, UserLoginDto } from "shared-types";

// Define a more complete AuthResponse type for our implementation
interface ExtendedAuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

// Store refresh tokens in memory (in production, use Redis or a database)
const refreshTokens: Map<string, { userId: string; expiresAt: Date }> =
  new Map();

@Injectable()
export class AuthService {
  constructor(
    @Inject(UsersService)
    private usersService: UsersService,
    @Inject(JwtService)
    private jwtService: JwtService
  ) {}

  async validateUser(email: string, password: string): Promise<User> {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      throw new UnauthorizedException("Invalid credentials");
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException("Invalid credentials");
    }

    return user;
  }

  async login(userLoginDto: UserLoginDto): Promise<ExtendedAuthResponse> {
    const user = await this.validateUser(
      userLoginDto.email,
      userLoginDto.password
    );

    const { accessToken, refreshToken } = this.generateTokens(user);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  async register(userCreateDto: UserCreateDto): Promise<ExtendedAuthResponse> {
    const user = await this.usersService.create(userCreateDto);
    const { accessToken, refreshToken } = this.generateTokens(user);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  async refreshToken(
    token: string
  ): Promise<{ accessToken: string; refreshToken: string }> {
    // Check if the refresh token exists and is valid
    const refreshTokenData = refreshTokens.get(token);
    if (!refreshTokenData) {
      throw new UnauthorizedException("Invalid refresh token");
    }

    // Check if the token is expired
    if (refreshTokenData.expiresAt < new Date()) {
      refreshTokens.delete(token);
      throw new UnauthorizedException("Refresh token expired");
    }

    // Get the user
    const user = await this.usersService.findOne(refreshTokenData.userId);
    if (!user) {
      refreshTokens.delete(token);
      throw new UnauthorizedException("User not found");
    }

    // Invalidate the old refresh token
    refreshTokens.delete(token);

    // Generate new tokens
    const tokens = this.generateTokens(user);

    return tokens;
  }

  async logout(token: string): Promise<void> {
    // Remove the refresh token
    refreshTokens.delete(token);
  }

  generateTokens(user: User): { accessToken: string; refreshToken: string } {
    // Generate access token
    const accessTokenPayload = { email: user.email, sub: user.id };
    const accessToken = this.jwtService.sign(accessTokenPayload, {
      expiresIn: "15m", // Short-lived access token
    });

    // Generate refresh token
    const refreshTokenPayload = { sub: user.id };
    const refreshToken = this.jwtService.sign(refreshTokenPayload, {
      expiresIn: "7d", // Longer-lived refresh token
    });

    // Store refresh token
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now
    refreshTokens.set(refreshToken, {
      userId: user.id,
      expiresAt,
    });

    return { accessToken, refreshToken };
  }
}
