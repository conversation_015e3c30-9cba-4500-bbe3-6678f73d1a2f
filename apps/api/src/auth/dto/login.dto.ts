import { ApiProperty } from "@nestjs/swagger"
import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsString, MinLength } from "class-validator"

export class LoginDto {
  @ApiProperty({
    description: "User's email address",
    example: "<EMAIL>",
    required: true,
  })
  @IsEmail({}, { message: "Please provide a valid email address" })
  @IsNotEmpty({ message: "Email is required" })
  email: string

  @ApiProperty({
    description: "User's password (minimum 8 characters)",
    example: "password123",
    required: true,
    minLength: 8,
  })
  @IsString()
  @IsNotEmpty({ message: "Password is required" })
  @MinLength(8, { message: "Password must be at least 8 characters long" })
  password: string
}