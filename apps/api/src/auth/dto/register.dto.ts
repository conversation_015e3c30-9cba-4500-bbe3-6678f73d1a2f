import { ApiProperty } from "@nestjs/swagger"
import { <PERSON>E<PERSON>, IsNotEmpty, IsS<PERSON>, Min<PERSON>ength, Matches } from "class-validator"

export class RegisterDto {
  @ApiProperty({
    description: "User's full name",
    example: "<PERSON>",
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: "Name is required" })
  name: string

  @ApiProperty({
    description: "User's email address",
    example: "<EMAIL>",
    required: true,
  })
  @IsEmail({}, { message: "Please provide a valid email address" })
  @IsNotEmpty({ message: "Email is required" })
  email: string

  @ApiProperty({
    description: "User's password (minimum 8 characters, must include at least one uppercase letter, one lowercase letter, and one number)",
    example: "Password123",
    required: true,
    minLength: 8,
  })
  @IsString()
  @IsNotEmpty({ message: "Password is required" })
  @MinLength(8, { message: "Password must be at least 8 characters long" })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, {
    message: "Password must contain at least one uppercase letter, one lowercase letter, and one number",
  })
  password: string
}