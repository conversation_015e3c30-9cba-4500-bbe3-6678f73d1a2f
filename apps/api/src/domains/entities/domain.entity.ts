import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from "typeorm"
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { User } from "../../users/entities/user.entity"

@Entity("domains")
export class Domain {
  @ApiProperty({
    description: "Unique identifier (UUID)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @PrimaryGeneratedColumn("uuid")
  id: string

  @ApiProperty({
    description: "Domain name (without http/https)",
    example: "example.com",
  })
  @Column()
  domain: string

  @ApiPropertyOptional({
    description: "Domain title",
    example: "My Company Website",
    nullable: true,
  })
  @Column({ nullable: true })
  title?: string

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is my company website focused on digital marketing services",
    nullable: true,
  })
  @Column({ nullable: true, type: "text" })
  description?: string

  @ApiProperty({
    description: "Whether the domain has been verified",
    example: false,
    default: false,
  })
  @Column({ type: "boolean", default: false })
  verified: boolean

  @ApiPropertyOptional({
    description: "Method used to verify domain ownership",
    example: "dns",
    enum: ["dns", "meta", "file"],
    nullable: true,
  })
  @Column({ nullable: true, enum: ["dns", "meta", "file"] })
  verificationMethod?: "dns" | "meta" | "file"

  @ApiPropertyOptional({
    description: "Unique code used for domain verification",
    example: "a1b2c3d4e5f6g7h8i9j0",
    nullable: true,
  })
  @Column({ nullable: true })
  verificationCode?: string

  @ApiPropertyOptional({
    description: "Date when the domain was verified",
    example: "2023-01-01T00:00:00Z",
    nullable: true,
  })
  @Column({ nullable: true })
  verifiedAt?: Date

  @ApiPropertyOptional({
    description: "Keywords relevant to the domain",
    example: ["marketing", "digital", "seo", "content"],
    nullable: true,
    type: [String],
  })
  @Column({ type: "simple-array", nullable: true })
  keywords?: string[]

  @ApiProperty({
    description: "User ID who owns this domain",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @Column("uuid")
  userId: string

  @ApiPropertyOptional({
    description: "User who owns this domain",
    type: () => User,
  })
  @ManyToOne(
    () => User,
    (user) => user.domains,
  )
  @JoinColumn({ name: "userId" })
  user: User

  @ApiProperty({
    description: "Date when the domain was created",
    example: "2023-01-01T00:00:00Z",
  })
  @CreateDateColumn()
  createdAt: Date

  @ApiProperty({
    description: "Date when the domain was last updated",
    example: "2023-01-02T00:00:00Z",
  })
  @UpdateDateColumn()
  updatedAt: Date
}
