import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { DomainsService } from "./domains.service"
import { DomainsController } from "./domains.controller"
import { Domain } from "./entities/domain.entity"

@Module({
  imports: [TypeOrmModule.forFeature([Domain])],
  providers: [DomainsService],
  controllers: [DomainsController],
  exports: [DomainsService],
})
export class DomainsModule {}
