import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Inject,
  HttpStatus,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from "@nestjs/swagger";
import { DomainsService } from "./domains.service";
import { Domain } from "./entities/domain.entity";
import { GetUser } from "../auth/decorators/get-user.decorator";
import { User } from "../users/entities/user.entity";
import { DomainCreateDto } from "./dto/domain-create.dto";
import { DomainVerifyDto } from "./dto/domain-verify.dto";
import { DomainUpdateDto } from "./dto/domain-update.dto";

@ApiTags("Domains")
@Controller("domains")
@ApiBearerAuth("JWT-auth")
export class DomainsController {
  constructor(
    @Inject(DomainsService) private readonly domainsService: DomainsService
  ) {}

  @ApiOperation({
    summary: "Get all domains",
    description: "Retrieves all domains for the authenticated user",
  })
  @ApiOkResponse({
    description: "List of domains retrieved successfully",
    type: [Domain],
    isArray: true,
  })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Get()
  findAll(@GetUser() user: User): Promise<Domain[]> {
    return this.domainsService.findAll(user.id);
  }

  @ApiOperation({
    summary: "Get a specific domain",
    description: "Retrieves a specific domain by ID for the authenticated user",
  })
  @ApiParam({
    name: "id",
    description: "Domain ID (UUID)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @ApiOkResponse({
    description: "Domain retrieved successfully",
    type: Domain,
  })
  @ApiNotFoundResponse({ description: "Domain not found" })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Get(":id")
  findOne(@Param("id") id: string, @GetUser() user: User): Promise<Domain> {
    return this.domainsService.findOne(id, user.id);
  }

  @ApiOperation({
    summary: "Create a new domain",
    description: "Creates a new domain for the authenticated user",
  })
  @ApiBody({
    type: DomainCreateDto,
    description: "Domain data",
  })
  @ApiCreatedResponse({
    description: "Domain created successfully",
    type: Domain,
  })
  @ApiBadRequestResponse({ description: "Bad request - Invalid input data" })
  @ApiConflictResponse({ description: "Domain already exists for this user" })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Post()
  create(
    @Body() domainCreateDto: DomainCreateDto,
    @GetUser() user: User
  ): Promise<Domain> {
    return this.domainsService.create(user.id, domainCreateDto);
  }

  @ApiOperation({
    summary: "Update a domain",
    description: "Updates an existing domain by ID for the authenticated user",
  })
  @ApiParam({
    name: "id",
    description: "Domain ID (UUID)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @ApiBody({
    type: DomainUpdateDto,
    description: "Domain update data",
  })
  @ApiOkResponse({
    description: "Domain updated successfully",
    type: Domain,
  })
  @ApiNotFoundResponse({ description: "Domain not found" })
  @ApiBadRequestResponse({ description: "Bad request - Invalid input data" })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateDomainDto: DomainUpdateDto,
    @GetUser() user: User
  ): Promise<Domain> {
    return this.domainsService.update(id, user.id, updateDomainDto);
  }

  @ApiOperation({
    summary: "Delete a domain",
    description: "Deletes a domain by ID for the authenticated user",
  })
  @ApiParam({
    name: "id",
    description: "Domain ID (UUID)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: "Domain deleted successfully",
  })
  @ApiNotFoundResponse({ description: "Domain not found" })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Delete(":id")
  remove(@Param("id") id: string, @GetUser() user: User): Promise<void> {
    return this.domainsService.remove(id, user.id);
  }

  @ApiOperation({
    summary: "Verify domain ownership",
    description:
      "Verifies ownership of a domain using the specified verification method",
  })
  @ApiBody({
    type: DomainVerifyDto,
    description: "Domain verification data",
  })
  @ApiOkResponse({
    description: "Domain verified successfully",
    type: Domain,
  })
  @ApiNotFoundResponse({ description: "Domain not found" })
  @ApiBadRequestResponse({
    description:
      "Verification failed - DNS record, meta tag, or verification file not found or incorrect",
  })
  @ApiUnauthorizedResponse({
    description: "Unauthorized - JWT token is missing or invalid",
  })
  @Post("verify")
  async verifyDomain(
    @Body() verifyDto: DomainVerifyDto,
    @GetUser() user: User
  ): Promise<Domain> {
    try {
      return await this.domainsService.verifyDomain(user.id, verifyDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || "Domain verification failed"
      );
    }
  }
}
