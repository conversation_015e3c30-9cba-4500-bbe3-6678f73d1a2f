import {
  Injectable,
  NotFoundException,
  ConflictEx<PERSON>,
  BadRequestException,
  Logger,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { ConfigService } from "@nestjs/config";
import type { Repository } from "typeorm";
import { randomBytes } from "crypto";
import * as dns from "dns";
import axios from "axios";
import * as cheerio from "cheerio";
import { Domain } from "./entities/domain.entity";
import type { DomainCreateDto, DomainVerifyDto } from "shared-types";

@Injectable()
export class DomainsService {
  private readonly logger = new Logger(DomainsService.name);

  constructor(
    @InjectRepository(Domain)
    private readonly domainsRepository: Repository<Domain>,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {}

  async findAll(userId: string): Promise<Domain[]> {
    return this.domainsRepository.find({ where: { userId } });
  }

  async findOne(id: string, userId: string): Promise<Domain> {
    const domain = await this.domainsRepository.findOne({
      where: { id, userId },
    });
    if (!domain) {
      throw new NotFoundException(`Domain with ID ${id} not found`);
    }
    return domain;
  }

  async findByDomainName(
    domainName: string,
    userId: string
  ): Promise<Domain | null> {
    return this.domainsRepository.findOne({
      where: { domain: domainName, userId },
    });
  }

  async create(
    userId: string,
    domainCreateDto: DomainCreateDto
  ): Promise<Domain> {
    // Check if domain already exists for this user
    const existingDomain = await this.findByDomainName(
      domainCreateDto.domain,
      userId
    );
    if (existingDomain) {
      throw new ConflictException("Domain already exists for this user");
    }

    // Generate verification code
    const verificationCode = randomBytes(16).toString("hex");

    const domain = this.domainsRepository.create({
      ...domainCreateDto,
      userId,
      verificationCode,
    });

    return this.domainsRepository.save(domain);
  }

  async update(
    id: string,
    userId: string,
    updateDomainDto: Partial<Domain>
  ): Promise<Domain> {
    const domain = await this.findOne(id, userId);
    Object.assign(domain, updateDomainDto);
    return this.domainsRepository.save(domain);
  }

  async remove(id: string, userId: string): Promise<void> {
    const domain = await this.findOne(id, userId);
    await this.domainsRepository.remove(domain);
  }

  // async verifyDomain(
  //   userId: string,
  //   verifyDto: DomainVerifyDto
  // ): Promise<Domain> {
  //   const domain = await this.findByDomainName(verifyDto.domain, userId);
  //   if (!domain) {
  //     throw new NotFoundException(`Domain ${verifyDto.domain} not found`);
  //   }

  //   // In a real application, we would actually verify the domain ownership here
  //   // For this demo, we'll just mark it as verified
  //   domain.verified = true;
  //   domain.verificationMethod = verifyDto.verificationMethod;
  //   domain.verifiedAt = new Date();

  //   return this.domainsRepository.save(domain);
  // }

  /**
   * Verifies domain ownership using the specified method
   */
  async verifyDomain(
    userId: string,
    verifyDto: DomainVerifyDto
  ): Promise<Domain> {
    const domain = await this.findByDomainName(verifyDto.domain, userId);
    if (!domain) {
      throw new NotFoundException(`Domain ${verifyDto.domain} not found`);
    }

    try {
      let isVerified = false;

      // Check if verification should be bypassed for testing/development
      const bypassVerification =
        this.configService.get<string>("BYPASS_DOMAIN_VERIFICATION") === "true";

      // Also check if the verification details contain a special bypass code
      const bypassCode = this.configService.get<string>(
        "DOMAIN_VERIFICATION_BYPASS_CODE"
      );
      const userProvidedBypassCode =
        verifyDto.dnsValue || verifyDto.metaContent || verifyDto.fileContent;

      const shouldBypass =
        bypassVerification ||
        (bypassCode && userProvidedBypassCode === bypassCode);

      if (shouldBypass) {
        this.logger.log(
          `Domain verification bypassed for ${domain.domain} due to bypass configuration or valid bypass code`
        );
        isVerified = true;
      } else {
        // Check if verification code exists
        if (!domain.verificationCode) {
          throw new BadRequestException(
            "Verification failed. No verification code found for this domain."
          );
        }

        // Implement actual verification based on method
        if (verifyDto.verificationMethod === "dns") {
          // Check if user provided DNS value
          if (!verifyDto.dnsValue) {
            throw new BadRequestException(
              "DNS verification failed. Please provide the TXT record value."
            );
          }

          // Verify that the provided DNS value matches the verification code
          if (verifyDto.dnsValue !== domain.verificationCode) {
            throw new BadRequestException(
              "DNS verification failed. The provided TXT record value does not match the verification code."
            );
          }

          try {
            // Check for DNS TXT record
            isVerified = await this.checkDnsTxtRecord(
              domain.domain,
              domain.verificationCode
            );

            if (!isVerified) {
              throw new BadRequestException(
                "DNS verification failed. TXT record not found or incorrect. Please ensure you've added the correct TXT record to '_backlink-verify' host and wait for DNS propagation (can take up to 24 hours)."
              );
            }
          } catch (error) {
            this.logger.error(
              `DNS verification error for ${domain.domain}:`,
              error
            );
            throw new BadRequestException(
              "DNS verification failed. There was an error checking your DNS records. Please ensure you've added the correct TXT record and try again."
            );
          }
        } else if (verifyDto.verificationMethod === "meta") {
          // Check if user provided meta content
          if (!verifyDto.metaContent) {
            throw new BadRequestException(
              "Meta tag verification failed. Please provide the meta tag content value."
            );
          }

          // Verify that the provided meta content matches the verification code
          if (verifyDto.metaContent !== domain.verificationCode) {
            throw new BadRequestException(
              "Meta tag verification failed. The provided meta tag content does not match the verification code."
            );
          }

          try {
            // Check for meta tag
            isVerified = await this.checkMetaTag(
              domain.domain,
              domain.verificationCode
            );

            if (!isVerified) {
              throw new BadRequestException(
                "Meta tag verification failed. Meta tag not found or incorrect. Please ensure you've added the meta tag to your website's homepage with the correct content value."
              );
            }
          } catch (error) {
            this.logger.error(
              `Meta tag verification error for ${domain.domain}:`,
              error
            );
            throw new BadRequestException(
              "Meta tag verification failed. There was an error checking your website. Please ensure your website is accessible and the meta tag is correctly added to the homepage."
            );
          }
        } else if (verifyDto.verificationMethod === "file") {
          // Check if user provided file content
          if (!verifyDto.fileContent) {
            throw new BadRequestException(
              "File verification failed. Please provide the verification code from the file."
            );
          }

          // Verify that the provided file content matches the verification code
          if (verifyDto.fileContent !== domain.verificationCode) {
            throw new BadRequestException(
              "File verification failed. The provided verification code does not match the expected code."
            );
          }

          try {
            // Check for verification file
            isVerified = await this.checkVerificationFile(
              domain.domain,
              domain.verificationCode
            );

            if (!isVerified) {
              throw new BadRequestException(
                "File verification failed. Verification file not found or incorrect. Please ensure you've uploaded the 'backlink-verify.html' file to your website's root directory with the correct verification code."
              );
            }
          } catch (error) {
            this.logger.error(
              `File verification error for ${domain.domain}:`,
              error
            );
            throw new BadRequestException(
              "File verification failed. There was an error checking your website. Please ensure your website is accessible and the verification file is correctly uploaded to the root directory."
            );
          }
        }
      }

      domain.verified = true;
      domain.verificationMethod = verifyDto.verificationMethod;
      domain.verifiedAt = new Date();

      return this.domainsRepository.save(domain);
    } catch (error) {
      this.logger.error(
        `Domain verification failed: ${error.message}`,
        error.stack
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      // Always use BadRequestException instead of UnauthorizedException for verification failures
      throw new BadRequestException(
        `Domain verification failed: ${error.message}`
      );
    }
  }

  /**
   * Checks if a DNS TXT record exists with the verification code
   */
  private async checkDnsTxtRecord(
    domain: string,
    verificationCode: string
  ): Promise<boolean> {
    try {
      this.logger.log(`Checking DNS TXT record for domain: ${domain}`);

      // Try with _backlink-verify prefix first
      const prefixResult = await this.resolveDnsTxt(
        `_backlink-verify.${domain}`,
        verificationCode
      );
      if (prefixResult) {
        return true;
      }

      // If that fails, try with @ (root domain)
      this.logger.log(
        `No TXT record found at _backlink-verify.${domain}, trying root domain...`
      );
      return await this.resolveDnsTxt(domain, verificationCode);
    } catch (error) {
      this.logger.error(
        `Error checking DNS TXT record: ${error.message}`,
        error.stack
      );
      return false;
    }
  }

  /**
   * Helper method to resolve DNS TXT records
   */
  private async resolveDnsTxt(
    domainToCheck: string,
    verificationCode: string
  ): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      dns.resolveTxt(domainToCheck, (err, records) => {
        if (err) {
          this.logger.warn(
            `DNS resolution error for ${domainToCheck}: ${err.message}`
          );
          resolve(false);
          return;
        }

        // Check if any of the TXT records match our verification code
        const found = records.some((record) =>
          record.some((txt) => txt.includes(verificationCode))
        );

        this.logger.log(
          `DNS TXT record ${found ? "found" : "not found"} for ${domainToCheck}`
        );
        resolve(found);
      });
    });
  }

  /**
   * Checks if a meta tag exists with the verification code
   */
  private async checkMetaTag(
    domain: string,
    verificationCode: string
  ): Promise<boolean> {
    try {
      this.logger.log(`Checking meta tag for domain: ${domain}`);

      // Try HTTPS first
      try {
        const httpsResult = await this.checkMetaTagWithProtocol(
          domain,
          verificationCode,
          "https"
        );
        if (httpsResult) {
          return true;
        }
      } catch (httpsError) {
        this.logger.warn(
          `HTTPS check failed for ${domain}: ${httpsError.message}`
        );
      }

      // If HTTPS fails, try HTTP
      this.logger.log(`HTTPS check failed for ${domain}, trying HTTP...`);
      return await this.checkMetaTagWithProtocol(
        domain,
        verificationCode,
        "http"
      );
    } catch (error) {
      this.logger.error(
        `Error checking meta tag: ${error.message}`,
        error.stack
      );
      return false;
    }
  }

  /**
   * Helper method to check meta tag with specific protocol
   */
  private async checkMetaTagWithProtocol(
    domain: string,
    verificationCode: string,
    protocol: "http" | "https"
  ): Promise<boolean> {
    const url = `${protocol}://${domain}`;
    this.logger.log(`Checking meta tag at ${url}`);

    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        "User-Agent": "DomainVerifier/1.0",
      },
    });

    const $ = cheerio.load(response.data);

    // Check for meta tag with exact content match
    let metaTag = $(
      `meta[name="backlink-verification"][content="${verificationCode}"]`
    );

    // If not found, try a more lenient approach (case insensitive, partial match)
    if (metaTag.length === 0) {
      // Check all meta tags manually
      const allMetaTags = $("meta");

      allMetaTags.each(function () {
        const name = $(this).attr("name")?.toLowerCase();
        const content = $(this).attr("content");
        if (
          name === "backlink-verification" &&
          content &&
          content.includes(verificationCode)
        ) {
          // If we find a matching tag, update our metaTag variable
          metaTag = $(this);
          // Break the loop by returning false
          return false;
        }
      });
    }

    const found = metaTag.length > 0;
    this.logger.log(`Meta tag ${found ? "found" : "not found"} at ${url}`);

    return found;
  }

  /**
   * Checks if a verification file exists with the verification code
   */
  private async checkVerificationFile(
    domain: string,
    verificationCode: string
  ): Promise<boolean> {
    try {
      this.logger.log(`Checking verification file for domain: ${domain}`);

      // Try HTTPS first
      try {
        const httpsResult = await this.checkVerificationFileWithProtocol(
          domain,
          verificationCode,
          "https"
        );
        if (httpsResult) {
          return true;
        }
      } catch (httpsError) {
        this.logger.warn(
          `HTTPS file check failed for ${domain}: ${httpsError.message}`
        );
      }

      // If HTTPS fails, try HTTP
      this.logger.log(`HTTPS file check failed for ${domain}, trying HTTP...`);
      return await this.checkVerificationFileWithProtocol(
        domain,
        verificationCode,
        "http"
      );
    } catch (error) {
      this.logger.error(
        `Error checking verification file: ${error.message}`,
        error.stack
      );
      return false;
    }
  }

  /**
   * Helper method to check verification file with specific protocol
   */
  private async checkVerificationFileWithProtocol(
    domain: string,
    verificationCode: string,
    protocol: "http" | "https"
  ): Promise<boolean> {
    const url = `${protocol}://${domain}/backlink-verify.html`;
    this.logger.log(`Checking verification file at ${url}`);

    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        "User-Agent": "DomainVerifier/1.0",
      },
    });

    // Check if the file contains the verification code
    const found = response.data.includes(verificationCode);
    this.logger.log(
      `Verification file ${found ? "found" : "not found"} at ${url}`
    );

    return found;
  }
}
