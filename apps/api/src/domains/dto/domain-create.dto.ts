import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsArray, IsNotEmpty, IsOptional, IsString, MaxLength, Matches } from "class-validator"
import type { DomainCreateDto as IDomainCreateDto } from "shared-types"

export class DomainCreateDto implements IDomainCreateDto {
  @ApiProperty({
    description: "Domain name (without http/https)",
    example: "example.com",
    required: true,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty({ message: "Domain name is required" })
  @MaxLength(255)
  @Matches(/^(?!:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z0-9]{2,}$/, {
    message: "Please provide a valid domain name without protocol (e.g., example.com)",
  })
  domain: string

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is my company website focused on digital marketing services",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string

  @ApiPropertyOptional({
    description: "Keywords relevant to the domain",
    example: ["marketing", "digital", "seo", "content"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[]
}