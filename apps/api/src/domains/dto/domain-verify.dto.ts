import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsEnum,
  IsNotEmpty,
  IsString,
  MaxLength,
  Matches,
  IsOptional,
} from "class-validator";
import type { DomainVerifyDto as IDomainVerifyDto } from "shared-types";

export class DomainVerifyDto implements IDomainVerifyDto {
  @ApiProperty({
    description: "Domain name to verify (without http/https)",
    example: "example.com",
    required: true,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty({ message: "Domain name is required" })
  @MaxLength(255)
  @Matches(/^(?!:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z0-9]{2,}$/, {
    message:
      "Please provide a valid domain name without protocol (e.g., example.com)",
  })
  domain: string;

  @ApiProperty({
    description: "Method used to verify domain ownership",
    example: "dns",
    enum: ["dns", "meta", "file"],
    required: true,
  })
  @IsEnum(["dns", "meta", "file"], {
    message: "Verification method must be one of: dns, meta, file",
  })
  verificationMethod: "dns" | "meta" | "file";

  @ApiPropertyOptional({
    description: "DNS TXT record value for DNS verification",
    example: "abc123def456",
    required: false,
  })
  @IsOptional()
  @IsString()
  dnsValue?: string;

  @ApiPropertyOptional({
    description: "Meta tag content value for meta tag verification",
    example: "abc123def456",
    required: false,
  })
  @IsOptional()
  @IsString()
  metaContent?: string;

  @ApiPropertyOptional({
    description: "File content verification code for file verification",
    example: "abc123def456",
    required: false,
  })
  @IsOptional()
  @IsString()
  fileContent?: string;
}
