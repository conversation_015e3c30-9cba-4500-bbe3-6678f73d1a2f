import { ApiPropertyOptional } from "@nestjs/swagger"
import { <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString, <PERSON><PERSON>ength, Matches } from "class-validator"

export class DomainUpdateDto {
  @ApiPropertyOptional({
    description: "Domain name (without http/https)",
    example: "example.com",
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  @Matches(/^(?!:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z0-9]{2,}$/, {
    message: "Please provide a valid domain name without protocol (e.g., example.com)",
  })
  domain?: string

  @ApiPropertyOptional({
    description: "Domain title",
    example: "My Company Website",
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is my company website focused on digital marketing services",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string

  @ApiPropertyOptional({
    description: "Keywords relevant to the domain",
    example: ["marketing", "digital", "seo", "content"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[]
}