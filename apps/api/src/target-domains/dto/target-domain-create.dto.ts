import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsArray, IsNotEmpty, IsOptional, IsString, MaxLength } from "class-validator"
import type { TargetDomainCreateDto as ITargetDomainCreateDto } from "shared-types"

export class TargetDomainCreateDto implements ITargetDomainCreateDto {
  @ApiProperty({
    description: "Domain name (without http/https)",
    example: "example.com",
    required: true,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  domain: string

  @ApiPropertyOptional({
    description: "Domain title or name",
    example: "Example Website",
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is an example website for technology news and reviews",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string

  @ApiPropertyOptional({
    description: "Categories that the domain belongs to",
    example: ["Technology", "Business", "News"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[]

  @ApiPropertyOptional({
    description: "Additional notes about the domain",
    example: "Potential collaboration opportunity for guest posting",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string
}
