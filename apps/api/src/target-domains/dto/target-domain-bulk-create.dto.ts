import { ApiProperty } from "@nestjs/swagger"
import { Type } from "class-transformer"
import { IsArray, ValidateNested, ArrayMinSize, ArrayMaxSize } from "class-validator"
import { TargetDomainCreateDto } from "./target-domain-create.dto"

export class TargetDomainBulkCreateDto {
  @ApiProperty({
    description: "Array of target domains to create (maximum 100 domains per request)",
    type: [TargetDomainCreateDto],
    isArray: true,
    example: [
      {
        domain: "example1.com",
        title: "Example Website 1",
        description: "First example website",
        categories: ["Technology"]
      },
      {
        domain: "example2.com",
        title: "Example Website 2",
        description: "Second example website",
        categories: ["Business"]
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TargetDomainCreateDto)
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  domains: TargetDomainCreateDto[]
}
