import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsArray, IsNumber, IsOptional, IsString, <PERSON>Length, <PERSON>, <PERSON>, IsEmail, IsUrl } from "class-validator"

export class TargetDomainUpdateDto {
  @ApiPropertyOptional({
    description: "Domain name (without http/https)",
    example: "example.com",
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  domain?: string

  @ApiPropertyOptional({
    description: "Domain title or name",
    example: "Example Website",
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is an example website for technology news and reviews",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string

  @ApiPropertyOptional({
    description: "Relevance score (0-100)",
    example: 85,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  relevanceScore?: number

  @ApiPropertyOptional({
    description: "Domain authority score (0-100)",
    example: 50,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  domainAuthority?: number

  @ApiPropertyOptional({
    description: "Number of backlinks",
    example: 1000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  backlinks?: number

  @ApiPropertyOptional({
    description: "Monthly traffic estimate",
    example: 5000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  traffic?: number

  @ApiPropertyOptional({
    description: "Contact email address",
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsEmail()
  contactEmail?: string

  @ApiPropertyOptional({
    description: "Contact page URL",
    example: "https://example.com/contact",
  })
  @IsOptional()
  @IsUrl()
  contactPage?: string

  @ApiPropertyOptional({
    description: "Categories that the domain belongs to",
    example: ["Technology", "Business", "News"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[]

  @ApiPropertyOptional({
    description: "Keywords relevant to the domain",
    example: ["technology", "news", "reviews", "gadgets"],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[]

  @ApiPropertyOptional({
    description: "Additional notes about the domain",
    example: "Potential collaboration opportunity for guest posting",
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string
}
