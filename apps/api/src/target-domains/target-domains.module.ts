import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { TargetDomainsController } from "./target-domains.controller"
import { TargetDomainsService } from "./target-domains.service"
import { TargetDomain } from "./entities/target-domain.entity"
import { AnalysisModule } from "../analysis/analysis.module"

@Module({
  imports: [
    TypeOrmModule.forFeature([TargetDomain]),
    AnalysisModule,
  ],
  controllers: [TargetDomainsController],
  providers: [TargetDomainsService],
  exports: [TargetDomainsService],
})
export class TargetDomainsModule {}
