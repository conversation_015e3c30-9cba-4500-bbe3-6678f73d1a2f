import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from "typeorm";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from "../../users/entities/user.entity";
import { DomainAnalysis } from "../../analysis/entities/domain-analysis.entity";
import { OutreachEmail } from "../../emails/entities/outreach-email.entity";

@Entity("target_domains")
export class TargetDomain {
  @ApiProperty({
    description: "Unique identifier (UUID)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiProperty({
    description: "User ID who owns this target domain",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @Column()
  userId: string;

  @ApiProperty({
    description: "Domain name (without http/https)",
    example: "example.com",
  })
  @Column()
  domain: string;

  @ApiPropertyOptional({
    description: "Domain title or name",
    example: "Example Website",
    nullable: true,
  })
  @Column({ nullable: true })
  title: string;

  @ApiPropertyOptional({
    description: "Detailed description of the domain",
    example: "This is an example website for technology news and reviews",
    nullable: true,
  })
  @Column({ type: "text", nullable: true })
  description: string;

  @ApiPropertyOptional({
    description: "Relevance score (0-100)",
    example: 85,
    nullable: true,
  })
  @Column({ type: "float", nullable: true })
  relevanceScore: number;

  @ApiPropertyOptional({
    description: "Domain authority score (0-100)",
    example: 50,
    nullable: true,
  })
  @Column({ type: "float", nullable: true })
  domainAuthority: number;

  @ApiPropertyOptional({
    description: "Number of backlinks",
    example: 1000,
    nullable: true,
  })
  @Column({ type: "int", nullable: true })
  backlinks: number;

  @ApiPropertyOptional({
    description: "Monthly traffic estimate",
    example: 5000,
    nullable: true,
  })
  @Column({ type: "int", nullable: true })
  traffic: number;

  @ApiPropertyOptional({
    description: "Contact email address",
    example: "<EMAIL>",
    nullable: true,
  })
  @Column({ nullable: true })
  contactEmail: string;

  @ApiPropertyOptional({
    description: "Contact page URL",
    example: "https://example.com/contact",
    nullable: true,
  })
  @Column({ nullable: true })
  contactPage: string;

  @ApiPropertyOptional({
    description: "Categories associated with the domain",
    example: ["Technology", "News"],
    nullable: true,
  })
  @Column({ type: "simple-array", nullable: true })
  categories: string[];

  @ApiPropertyOptional({
    description: "Keywords associated with the domain",
    example: ["tech news", "reviews"],
    nullable: true,
  })
  @Column({ type: "simple-array", nullable: true })
  keywords: string[];

  @ApiPropertyOptional({
    description: "Last updated date",
    example: "2023-10-01T12:00:00Z",
    nullable: true,
  })
  @Column({ nullable: true })
  lastUpdated: Date;

  @ApiPropertyOptional({
    description: "Additional notes about the domain",
    example: "This domain is a great source for tech news",
    nullable: true,
  })
  @Column({ type: "text", nullable: true })
  notes: string;

  @ApiPropertyOptional({
    description: "Date when the domain was last analyzed",
    example: "2023-10-01T12:00:00Z",
    nullable: true,
  })
  @Column({ nullable: true })
  analysisDate: Date;

  @ApiPropertyOptional({
    description: "Date when the domain was last contacted",
    example: "2023-10-01T12:00:00Z",
    nullable: true,
  })
  @Column({ nullable: true })
  lastContactedAt: Date;

  @ManyToOne(() => User, (user) => user.targetDomains)
  @JoinColumn({ name: "userId" })
  user: User;

  @OneToMany(() => DomainAnalysis, (analysis) => analysis.targetDomain)
  analyses: DomainAnalysis[];

  @OneToMany(() => OutreachEmail, (email) => email.targetDomain)
  outreachEmails: OutreachEmail[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
