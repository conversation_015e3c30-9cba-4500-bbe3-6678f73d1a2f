import {
  Inject,
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { TargetDomain } from "./entities/target-domain.entity";
import type {
  TargetDomainCreateDto,
  TargetDomainBulkCreateDto,
} from "shared-types";
import { AnalysisService } from "../analysis/analysis.service";
import * as csv from "csv-parser";
import { Readable } from "stream";

@Injectable()
export class TargetDomainsService {
  private readonly logger = new Logger(TargetDomainsService.name);

  constructor(
    @InjectRepository(TargetDomain)
    private readonly targetDomainsRepository: Repository<TargetDomain>,
    @Inject(AnalysisService)
    private readonly analysisService: AnalysisService
  ) {}

  async findAll(userId: string): Promise<TargetDomain[]> {
    return this.targetDomainsRepository.find({ where: { userId } });
  }

  async findOne(id: string, userId: string): Promise<TargetDomain> {
    const targetDomain = await this.targetDomainsRepository.findOne({
      where: { id, userId },
    });
    if (!targetDomain) {
      throw new NotFoundException(`Target domain with ID ${id} not found`);
    }
    return targetDomain;
  }

  async create(
    userId: string,
    targetDomainCreateDto: TargetDomainCreateDto
  ): Promise<TargetDomain> {
    const targetDomain = this.targetDomainsRepository.create({
      ...targetDomainCreateDto,
      userId,
    });

    const savedDomain = await this.targetDomainsRepository.save(targetDomain);

    // Trigger analysis in the background
    this.analysisService.analyzeTargetDomain(savedDomain.id, userId);

    return savedDomain;
  }

  async bulkCreate(
    userId: string,
    bulkCreateDto: TargetDomainBulkCreateDto
  ): Promise<TargetDomain[]> {
    const targetDomains = bulkCreateDto.domains.map((domainDto) =>
      this.targetDomainsRepository.create({
        ...domainDto,
        userId,
      })
    );

    const savedDomains = await this.targetDomainsRepository.save(targetDomains);

    // Trigger analysis for each domain in the background
    for (const domain of savedDomains) {
      this.analysisService.analyzeTargetDomain(domain.id, userId);
    }

    return savedDomains;
  }

  async update(
    id: string,
    userId: string,
    updateTargetDomainDto: Partial<TargetDomain>
  ): Promise<TargetDomain> {
    const targetDomain = await this.findOne(id, userId);
    Object.assign(targetDomain, updateTargetDomainDto);
    return this.targetDomainsRepository.save(targetDomain);
  }

  async remove(id: string, userId: string): Promise<void> {
    const targetDomain = await this.findOne(id, userId);
    await this.targetDomainsRepository.remove(targetDomain);
  }

  /**
   * Parse a CSV file and extract domain data
   * @param buffer The CSV file buffer
   * @returns Array of target domain DTOs
   */
  async parseCSV(buffer: Buffer): Promise<TargetDomainCreateDto[]> {
    this.logger.log("Parsing CSV file");

    try {
      const domains: TargetDomainCreateDto[] = [];
      const results = await this.readCSV(buffer);

      // Validate CSV structure
      if (results.length === 0) {
        throw new BadRequestException("CSV file is empty");
      }

      // Check if the first row has a domain column
      const firstRow = results[0];
      if (
        !firstRow.domain &&
        !firstRow.Domain &&
        !firstRow.DOMAIN &&
        !firstRow["domain name"] &&
        !firstRow["Domain Name"]
      ) {
        throw new BadRequestException("CSV file must have a domain column");
      }

      // Transform CSV data to domain DTOs
      for (const row of results) {
        // Try to find the domain field with different possible names
        const domainValue =
          row.domain ||
          row.Domain ||
          row.DOMAIN ||
          row["domain name"] ||
          row["Domain Name"] ||
          "";

        if (!domainValue) {
          this.logger.warn("Skipping row with no domain value");
          continue;
        }

        // Normalize domain (remove http://, https://, www. and trailing slashes)
        let normalizedDomain = domainValue.trim().toLowerCase();
        normalizedDomain = normalizedDomain.replace(
          /^(https?:\/\/)?(www\.)?/,
          ""
        );
        normalizedDomain = normalizedDomain.replace(/\/+$/, "");

        // Skip if domain is still empty after normalization
        if (!normalizedDomain) {
          this.logger.warn(`Skipping invalid domain: ${domainValue}`);
          continue;
        }

        const domain: TargetDomainCreateDto = {
          domain: normalizedDomain,
          title: row.title || row.Title || row.name || row.Name || "",
          description: row.description || row.Description || "",
          categories: this.parseCategories(row),
          notes: row.notes || row.Notes || row.comment || row.Comment || "",
        };

        // Check for duplicates in the current batch
        if (!domains.some((d) => d.domain === domain.domain)) {
          domains.push(domain);
        } else {
          this.logger.warn(
            `Skipping duplicate domain in CSV: ${domain.domain}`
          );
        }
      }

      this.logger.log(`Parsed ${domains.length} domains from CSV`);
      return domains;
    } catch (error) {
      this.logger.error(`Error parsing CSV: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to parse CSV file: ${error.message}`
      );
    }
  }

  /**
   * Read a CSV file and return the parsed rows
   * @param buffer The CSV file buffer
   * @returns Parsed CSV rows
   */
  private async readCSV(buffer: Buffer): Promise<any[]> {
    return new Promise<any[]>((resolve, reject) => {
      const results: any[] = [];
      const stream = Readable.from(buffer);

      stream
        .pipe(csv())
        .on("data", (data) => results.push(data))
        .on("end", () => resolve(results))
        .on("error", (error) => reject(error));
    });
  }

  /**
   * Log file information for debugging CSV upload issues
   * @param file The uploaded file
   */
  logFileInfo(file: Express.Multer.File): void {
    this.logger.log(`File upload information:
      - Original name: ${file.originalname}
      - MIME type: ${file.mimetype}
      - Size: ${file.size} bytes
      - Field name: ${file.fieldname}
    `);
  }

  /**
   * Parse categories from a CSV row
   * @param row The CSV row
   * @returns Array of categories
   */
  private parseCategories(row: any): string[] {
    // Try different possible column names for categories
    const categoriesValue =
      row.categories ||
      row.Categories ||
      row.category ||
      row.Category ||
      row.tags ||
      row.Tags ||
      "";

    if (!categoriesValue) {
      return [];
    }

    // If it's already an array, return it
    if (Array.isArray(categoriesValue)) {
      return categoriesValue;
    }

    // Split by comma, semicolon, or pipe and trim each value
    return categoriesValue
      .split(/[,;|]/)
      .map((c: string) => c.trim())
      .filter((c: string) => c.length > 0);
  }
}
