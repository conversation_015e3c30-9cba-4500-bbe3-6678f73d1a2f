import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseFilePipe,
  FileTypeValidator,
  MaxFileSizeValidator,
  UploadedFile,
  UseInterceptors,
  Inject,
  BadRequestException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from "@nestjs/swagger";
import { TargetDomainsService } from "./target-domains.service";
import type { TargetDomain } from "./entities/target-domain.entity";
import { GetUser } from "../auth/decorators/get-user.decorator";
import type { User } from "../users/entities/user.entity";
// Add missing imports
import { TargetDomainBulkCreateDto } from "./dto/target-domain-bulk-create.dto";
import { TargetDomainUpdateDto } from "./dto/target-domain-update.dto";
import { TargetDomainCreateDto } from "./dto/target-domain-create.dto";

@ApiTags("target-domains")
@Controller("target-domains")
@ApiBearerAuth("JWT-auth")
export class TargetDomainsController {
  constructor(
    @Inject(TargetDomainsService)
    private readonly targetDomainsService: TargetDomainsService
  ) {}

  @Get()
  @ApiOperation({ summary: "Get all target domains for the current user" })
  @ApiResponse({ status: 200, description: "Returns all target domains" })
  findAll(@GetUser() user: User): Promise<TargetDomain[]> {
    return this.targetDomainsService.findAll(user.id);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a target domain by ID" })
  @ApiResponse({ status: 200, description: "Returns the target domain" })
  @ApiResponse({ status: 404, description: "Target domain not found" })
  findOne(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<TargetDomain> {
    return this.targetDomainsService.findOne(id, user.id);
  }

  @Post()
  @ApiBody({ type: TargetDomainCreateDto })
  @ApiOperation({ summary: "Create a new target domain" })
  @ApiResponse({
    status: 201,
    description: "Target domain created successfully",
  })
  create(
    @Body() targetDomainCreateDto: TargetDomainCreateDto,
    @GetUser() user: User
  ): Promise<TargetDomain> {
    return this.targetDomainsService.create(user.id, targetDomainCreateDto);
  }

  @Post("bulk")
  @ApiOperation({ summary: "Create multiple target domains" })
  @ApiBody({ type: TargetDomainBulkCreateDto })
  @ApiResponse({
    status: 201,
    description: "Target domains created successfully",
  })
  bulkCreate(
    @Body() bulkCreateDto: TargetDomainBulkCreateDto,
    @GetUser() user: User
  ): Promise<TargetDomain[]> {
    return this.targetDomainsService.bulkCreate(user.id, bulkCreateDto);
  }

  @Post("upload")
  @UseInterceptors(FileInterceptor("file"))
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  @ApiOperation({ summary: "Upload CSV file with target domains" })
  @ApiResponse({
    status: 201,
    description: "Target domains created successfully",
  })
  async uploadCsv(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          // Accept various MIME types that browsers might use for CSV files
          // We're using a very permissive validator here and will check the file extension as a fallback
          new FileTypeValidator({
            fileType: /.*/,
          }),
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 5 }), // 5MB
        ],
        fileIsRequired: true,
        exceptionFactory: (error) => {
          console.error("File upload error:", error);
          // Provide a more user-friendly error message
          if (error.includes("file type")) {
            return new BadRequestException(
              "Invalid file format. Please upload a CSV file with .csv extension."
            );
          }
          return new BadRequestException(error);
        },
      })
    )
    file: Express.Multer.File,
    @GetUser() user: User
  ): Promise<TargetDomain[]> {
    // Check file extension as a fallback (some browsers don't set the correct MIME type)
    if (!file.originalname.toLowerCase().endsWith(".csv")) {
      throw new BadRequestException(
        "Invalid file format. Please upload a file with .csv extension."
      );
    }

    // Log file information for debugging
    this.targetDomainsService.logFileInfo(file);

    try {
      // Parse CSV file using our enhanced parser
      const domains = await this.targetDomainsService.parseCSV(file.buffer);

      // Create the domains in the database
      return this.targetDomainsService.bulkCreate(user.id, { domains });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to process CSV file: ${error.message}`
      );
    }
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update a target domain" })
  @ApiBody({ type: TargetDomainUpdateDto })
  @ApiResponse({
    status: 200,
    description: "Target domain updated successfully",
  })
  @ApiResponse({ status: 404, description: "Target domain not found" })
  update(
    @Param("id") id: string,
    @Body() updateTargetDomainDto: TargetDomainUpdateDto,
    @GetUser() user: User
  ): Promise<TargetDomain> {
    return this.targetDomainsService.update(id, user.id, updateTargetDomainDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a target domain" })
  @ApiResponse({
    status: 200,
    description: "Target domain deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Target domain not found" })
  remove(@Param("id") id: string, @GetUser() user: User): Promise<void> {
    return this.targetDomainsService.remove(id, user.id);
  }
}
