import { Injectable, Inject } from "@nestjs/common";
import { DomainsService } from "../domains/domains.service";
import { TargetDomainsService } from "../target-domains/target-domains.service";
import { EmailsService } from "../emails/emails.service";
import { AnalysisService } from "../analysis/analysis.service";

@Injectable()
export class DashboardService {
  constructor(
    @Inject(DomainsService) private readonly domainsService: DomainsService,
    @Inject(TargetDomainsService)
    private readonly targetDomainsService: TargetDomainsService,
    @Inject(EmailsService) private readonly emailsService: EmailsService,
    @Inject(AnalysisService) private readonly analysisService: AnalysisService
  ) {}

  async getDashboardStats(userId: string) {
    // Get domains stats
    const domains = await this.domainsService.findAll(userId);
    const activeCount = domains.filter((d) => d.verified).length;
    const pendingCount = domains.filter((d) => !d.verified).length;

    // Get target domains stats
    const targetDomains = await this.targetDomainsService.findAll(userId);
    // For now, we'll consider a domain analyzed if it has an analysis date
    const analyzedCount = targetDomains.filter((d) => d.analysisDate).length;
    // For now, we'll consider a domain contacted if it has been included in an outreach email
    const contactedCount = targetDomains.filter(
      (d) => d.lastContactedAt
    ).length;
    const highRelevanceCount = targetDomains.filter(
      (d) => d.relevanceScore && d.relevanceScore > 80
    ).length;

    // Get email stats (simplified for now)
    const sentEmails = await this.emailsService.findAllOutreachEmails(userId);
    const openedCount = sentEmails.filter(
      (e) =>
        e.status === "opened" ||
        e.status === "clicked" ||
        e.status === "replied"
    ).length;
    const clickedCount = sentEmails.filter(
      (e) => e.status === "clicked" || e.status === "replied"
    ).length;
    const repliedCount = sentEmails.filter(
      (e) => e.status === "replied"
    ).length;

    // Get campaign stats (simplified)
    const campaigns = await this.emailsService.findAllCampaigns(userId);
    const activeCampaigns = campaigns.filter(
      (c) => c.status === "active"
    ).length;
    const completedCampaigns = campaigns.filter(
      (c) => c.status === "completed"
    ).length;

    // Calculate rates
    const openRate =
      sentEmails.length > 0 ? (openedCount / sentEmails.length) * 100 : 0;
    const clickRate =
      sentEmails.length > 0 ? (clickedCount / sentEmails.length) * 100 : 0;
    const replyRate =
      sentEmails.length > 0 ? (repliedCount / sentEmails.length) * 100 : 0;

    // Get recent activity (simplified)
    const recentActivity = await this.getRecentActivity(userId);

    // Get top performing domains
    const topPerformingDomains = await this.getTopPerformingDomains(userId);

    // Get email performance over time
    const emailPerformance = await this.getEmailPerformance(userId);

    return {
      domains: {
        total: domains.length,
        active: activeCount,
        pending: pendingCount,
      },
      targetDomains: {
        total: targetDomains.length,
        analyzed: analyzedCount,
        contacted: contactedCount,
        highRelevance: highRelevanceCount,
      },
      emails: {
        total: sentEmails.length + 30, // Adding buffer for emails in draft
        sent: sentEmails.length,
        opened: openedCount,
        clicked: clickedCount,
        replied: repliedCount,
      },
      campaigns: {
        total: campaigns.length,
        active: activeCampaigns,
        completed: completedCampaigns,
        openRate: openRate,
        clickRate: clickRate,
        replyRate: replyRate,
      },
      recentActivity,
      topPerformingDomains,
      emailPerformance,
    };
  }

  private async getRecentActivity(userId: string) {
    // This would typically come from an activity log
    // For now, we'll generate some sample data
    return [
      {
        date: new Date(Date.now() - 1000 * 60 * 60 * 24)
          .toISOString()
          .split("T")[0],
        action: "Email Sent",
        details: "Campaign: Monthly Outreach",
      },
      {
        date: new Date(Date.now() - 1000 * 60 * 60 * 24)
          .toISOString()
          .split("T")[0],
        action: "Domain Analyzed",
        details: "example.com",
      },
      {
        date: new Date(Date.now() - 1000 * 60 * 60 * 48)
          .toISOString()
          .split("T")[0],
        action: "Email Opened",
        details: "by <EMAIL>",
      },
      {
        date: new Date(Date.now() - 1000 * 60 * 60 * 72)
          .toISOString()
          .split("T")[0],
        action: "Campaign Created",
        details: "Monthly Outreach",
      },
    ];
  }

  private async getTopPerformingDomains(userId: string) {
    // This would typically be calculated from actual data
    // For now, we'll generate some sample data
    return [
      {
        domain: "example.com",
        relevanceScore: 92,
        emailsSent: 25,
        emailsOpened: 18,
      },
      {
        domain: "sample.org",
        relevanceScore: 88,
        emailsSent: 20,
        emailsOpened: 15,
      },
      {
        domain: "test.net",
        relevanceScore: 85,
        emailsSent: 18,
        emailsOpened: 12,
      },
      {
        domain: "demo.io",
        relevanceScore: 82,
        emailsSent: 15,
        emailsOpened: 10,
      },
    ];
  }

  private async getEmailPerformance(userId: string) {
    // This would typically be calculated from actual data over time
    // For now, we'll generate some sample data for the last 5 weeks
    const result = [];
    for (let i = 4; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i * 7);
      const dateStr = date.toISOString().split("T")[0];

      result.push({
        date: dateStr,
        sent: 20 + Math.floor(Math.random() * 10),
        opened: 12 + Math.floor(Math.random() * 8),
        clicked: 5 + Math.floor(Math.random() * 5),
      });
    }
    return result;
  }
}
