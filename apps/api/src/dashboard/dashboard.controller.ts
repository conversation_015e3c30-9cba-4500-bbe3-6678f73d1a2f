import { Controller, Get, UseGuards, Inject } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { DashboardService } from "./dashboard.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { GetUser } from "../auth/decorators/get-user.decorator";
import { User } from "../users/entities/user.entity";

@ApiTags("dashboard")
@Controller("dashboard")
@ApiBearerAuth("JWT-auth")
export class DashboardController {
  constructor(
    @Inject(DashboardService)
    private readonly dashboardService: DashboardService
  ) {}

  @Get("stats")
  @ApiOperation({ summary: "Get dashboard statistics" })
  @ApiResponse({ status: 200, description: "Returns dashboard statistics" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getDashboardStats(@GetUser() user: User) {
    return this.dashboardService.getDashboardStats(user.id);
  }
}
