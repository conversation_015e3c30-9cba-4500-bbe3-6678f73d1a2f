import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { DashboardController } from "./dashboard.controller";
import { DashboardService } from "./dashboard.service";
import { DomainsModule } from "../domains/domains.module";
import { TargetDomainsModule } from "../target-domains/target-domains.module";
import { EmailsModule } from "../emails/emails.module";
import { AnalysisModule } from "../analysis/analysis.module";

@Module({
  imports: [
    DomainsModule,
    TargetDomainsModule,
    EmailsModule,
    AnalysisModule,
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}
