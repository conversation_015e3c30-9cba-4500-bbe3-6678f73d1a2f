/**
 * Base interface for all domain information extractors
 * This enables a pluggable architecture where different providers can be easily swapped
 */
export interface DomainInfoExtractor {
  /**
   * Unique identifier for this extractor
   */
  readonly name: string;

  /**
   * Priority level for this extractor (higher = more priority)
   */
  readonly priority: number;

  /**
   * Whether this extractor is currently enabled
   */
  readonly enabled: boolean;

  /**
   * Data types this extractor can provide
   */
  readonly supportedDataTypes: DomainDataType[];

  /**
   * Extract domain information
   */
  extractDomainInfo(
    domain: string,
    options?: DomainExtractionOptions
  ): Promise<DomainInfo>;

  /**
   * Check if this extractor can handle the given domain and data type
   */
  canHandle(domain: string, dataType: DomainDataType): boolean;

  /**
   * Get the cost/rate limit impact of using this extractor
   */
  getCost(dataType: DomainDataType): ExtractorCost;

  /**
   * Validate configuration and connectivity
   */
  validateConfiguration(): Promise<ValidationResult>;
}

/**
 * Types of domain data that can be extracted
 */
export enum DomainDataType {
  BASIC_INFO = "basic_info",
  DOMAIN_RATING = "domain_rating",
  TRAFFIC_DATA = "traffic_data",
  KEYWORDS = "keywords",
  BACKLINKS = "backlinks",
  CONTACT_INFO = "contact_info",
  WHOIS_DATA = "whois_data",
  SOCIAL_PROFILES = "social_profiles",
  TECHNOLOGY_STACK = "technology_stack",
  CONTENT_ANALYSIS = "content_analysis",
}

/**
 * Options for domain extraction
 */
export interface DomainExtractionOptions {
  includeContactInfo?: boolean;
  includeKeywords?: boolean;
  includeTrafficData?: boolean;
  includeBacklinks?: boolean;
  includeTechnologyStack?: boolean;
  maxKeywords?: number;
  maxBacklinks?: number;
  useCache?: boolean;
  cacheTtl?: number;
  timeout?: number;
}

/**
 * Comprehensive domain information structure
 */
export interface DomainInfo {
  domain: string;
  extractedAt: Date;
  extractorName: string;

  // Basic information
  title?: string;
  description?: string;
  language?: string;
  country?: string;
  industry?: string;
  category?: string;
  websiteAge?: number;

  // SEO metrics
  domainRating?: number;
  domainAuthority?: number;
  trustFlow?: number;
  citationFlow?: number;

  // Traffic data
  trafficData?: {
    visits: number;
    uniqueVisitors?: number;
    pageViews?: number;
    bounceRate?: number;
    avgSessionDuration?: number;
    trend: "upward" | "downward" | "stable";
    topCountries?: Array<{ country: string; percentage: number }>;
  };

  // Keywords
  keywords?: Array<{
    keyword: string;
    position: number;
    searchVolume: number;
    difficulty?: number;
    traffic?: number;
    url?: string;
  }>;

  // Backlinks
  backlinks?: {
    totalBacklinks: number;
    referringDomains: number;
    topBacklinks?: Array<{
      sourceUrl: string;
      sourceDomain: string;
      anchor: string;
      domainRating?: number;
      firstSeen?: Date;
      lastSeen?: Date;
    }>;
  };

  // Contact information
  contactInfo?: {
    emails: string[];
    phoneNumbers: string[];
    socialProfiles: string[];
    contactPage?: string;
    contactForm?: string;
    address?: string;
    contactName?: string;
    contactPosition?: string;
  };

  // WHOIS data
  whoisData?: {
    registrar?: string;
    registrationDate?: Date;
    expirationDate?: Date;
    lastUpdated?: Date;
    nameServers?: string[];
    registrantCountry?: string;
    ageInYears?: number;
  };

  // Technology stack
  technologyStack?: {
    cms?: string;
    framework?: string;
    analytics?: string[];
    advertising?: string[];
    ecommerce?: string[];
    hosting?: string;
    cdn?: string;
    ssl?: boolean;
  };

  // Content analysis
  contentAnalysis?: {
    wordCount?: number;
    readabilityScore?: number;
    sentiment?: "positive" | "negative" | "neutral";
    topics?: string[];
    entities?: Array<{ name: string; type: string; confidence: number }>;
  };

  // Metadata
  metadata?: {
    extractionTimeMs: number;
    dataFreshness: "fresh" | "cached" | "stale";
    confidence: number;
    sources: string[];
    errors?: string[];
    warnings?: string[];
  };
}

/**
 * Cost information for using an extractor
 */
export interface ExtractorCost {
  apiCalls: number;
  estimatedDelay: number; // in milliseconds
  rateLimitImpact: "low" | "medium" | "high";
  monetaryCost?: number; // in cents
}

/**
 * Validation result for extractor configuration
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  capabilities: DomainDataType[];
  rateLimits?: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
}

/**
 * Registry for managing domain info extractors
 */
export interface DomainInfoExtractorRegistry {
  /**
   * Register a new extractor
   */
  register(extractor: DomainInfoExtractor): void;

  /**
   * Unregister an extractor
   */
  unregister(extractorName: string): void;

  /**
   * Get all registered extractors
   */
  getAll(): DomainInfoExtractor[];

  /**
   * Get extractors that can handle a specific data type
   */
  getByDataType(dataType: DomainDataType): DomainInfoExtractor[];

  /**
   * Get the best extractor for a specific domain and data type
   */
  getBest(domain: string, dataType: DomainDataType): DomainInfoExtractor | null;

  /**
   * Get extractor by name
   */
  getByName(name: string): DomainInfoExtractor | null;

  /**
   * Validate all registered extractors
   */
  validateAll(): Promise<Record<string, ValidationResult>>;
}

/**
 * Strategy for selecting extractors
 */
export enum ExtractorSelectionStrategy {
  PRIORITY = "priority", // Use highest priority extractor
  COST = "cost", // Use lowest cost extractor
  SPEED = "speed", // Use fastest extractor
  RELIABILITY = "reliability", // Use most reliable extractor
  ROUND_ROBIN = "round_robin", // Rotate between extractors
}

/**
 * Configuration for domain extraction orchestration
 */
export interface DomainExtractionConfig {
  strategy: ExtractorSelectionStrategy;
  fallbackEnabled: boolean;
  maxRetries: number;
  timeout: number;
  cacheEnabled: boolean;
  cacheTtl: number;
  parallelExtraction: boolean;
  maxConcurrency: number;
}
