import { Test, TestingModule } from '@nestjs/testing';
import { AnalysisController } from './analysis.controller';
import { AnalysisService } from './analysis.service';
import { CreateAnalysisDto } from './dto/create-analysis.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ExecutionContext } from '@nestjs/common';

describe('AnalysisController', () => {
  let controller: AnalysisController;
  let service: jest.Mocked<AnalysisService>;

  const mockAnalysisService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    analyzeDomain: jest.fn(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn((context: ExecutionContext) => {
      const request = context.switchToHttp().getRequest();
      request.user = { id: '1', email: '<EMAIL>' };
      return true;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AnalysisController],
      providers: [
        {
          provide: AnalysisService,
          useValue: mockAnalysisService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    controller = module.get<AnalysisController>(AnalysisController);
    service = module.get(AnalysisService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new analysis', async () => {
      const createAnalysisDto: CreateAnalysisDto = {
        targetDomainId: '1',
        userDomainId: '2',
      };

      const expectedResult = {
        id: '1',
        targetDomainId: '1',
        userDomainId: '2',
        score: 85,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAnalysisService.create.mockResolvedValue(expectedResult as any);

      const result = await controller.create(createAnalysisDto);

      expect(result).toEqual(expectedResult);
      expect(service.create).toHaveBeenCalledWith(createAnalysisDto);
    });

    it('should handle service errors', async () => {
      const createAnalysisDto: CreateAnalysisDto = {
        targetDomainId: '1',
        userDomainId: '2',
      };

      mockAnalysisService.create.mockRejectedValue(new Error('Service error'));

      await expect(controller.create(createAnalysisDto)).rejects.toThrow('Service error');
    });
  });

  describe('findAll', () => {
    it('should return all analyses', async () => {
      const expectedResult = [
        {
          id: '1',
          targetDomainId: '1',
          userDomainId: '2',
          score: 85,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          targetDomainId: '3',
          userDomainId: '4',
          score: 72,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockAnalysisService.findAll.mockResolvedValue(expectedResult as any);

      const result = await controller.findAll();

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single analysis', async () => {
      const analysisId = '1';
      const expectedResult = {
        id: '1',
        targetDomainId: '1',
        userDomainId: '2',
        score: 85,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAnalysisService.findOne.mockResolvedValue(expectedResult as any);

      const result = await controller.findOne(analysisId);

      expect(result).toEqual(expectedResult);
      expect(service.findOne).toHaveBeenCalledWith(analysisId);
    });

    it('should handle not found', async () => {
      const analysisId = 'non-existent';

      mockAnalysisService.findOne.mockResolvedValue(null);

      const result = await controller.findOne(analysisId);

      expect(result).toBeNull();
      expect(service.findOne).toHaveBeenCalledWith(analysisId);
    });
  });

  describe('analyzeDomain', () => {
    it('should analyze a domain and return results', async () => {
      const targetDomain = 'example.com';
      const userDomain = 'user.com';

      const expectedResult = {
        title: 'Example Domain',
        description: 'Example description',
        keywords: ['example', 'domain'],
        relevanceScore: 85,
        industry: 'Technology',
        category: 'SaaS',
        language: 'en',
        domainAuthority: 75,
        backlinks: 50000,
        traffic: 100000,
        trafficTrend: 'upward' as const,
        topKeywords: ['example', 'domain', 'technology'],
        topPages: ['/', '/about', '/services'],
        websiteAge: 60,
        topCountry: 'United States',
        contactEmail: '<EMAIL>',
        contactPage: '/contact',
        contactName: 'John Doe',
        contactPosition: 'CEO',
        contactForm: '/contact-form',
        address: '123 Main St',
        socialProfiles: ['https://twitter.com/example'],
        phoneNumbers: ['******-0123'],
        chatSupport: true,
        favicon: '/favicon.ico',
        author: 'Example Team',
        publishDate: '2023-01-01',
        lastModified: '2023-12-01',
        headings: {
          h1: ['Welcome to Example'],
          h2: ['About Us', 'Services'],
          h3: ['Our Mission', 'Our Vision'],
        },
        sentiment: 0.8,
        topTopics: ['technology', 'innovation', 'solutions'],
      };

      mockAnalysisService.analyzeDomain.mockResolvedValue(expectedResult);

      const result = await controller.analyzeDomain(targetDomain, userDomain);

      expect(result).toEqual(expectedResult);
      expect(service.analyzeDomain).toHaveBeenCalledWith(targetDomain, userDomain);
    });

    it('should handle analysis errors', async () => {
      const targetDomain = 'invalid-domain';
      const userDomain = 'user.com';

      mockAnalysisService.analyzeDomain.mockRejectedValue(new Error('Analysis failed'));

      await expect(controller.analyzeDomain(targetDomain, userDomain)).rejects.toThrow(
        'Analysis failed',
      );
    });
  });

  describe('update', () => {
    it('should update an analysis', async () => {
      const analysisId = '1';
      const updateData = { score: 90 };
      const expectedResult = {
        id: '1',
        targetDomainId: '1',
        userDomainId: '2',
        score: 90,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAnalysisService.update.mockResolvedValue(expectedResult as any);

      const result = await controller.update(analysisId, updateData);

      expect(result).toEqual(expectedResult);
      expect(service.update).toHaveBeenCalledWith(analysisId, updateData);
    });
  });

  describe('remove', () => {
    it('should remove an analysis', async () => {
      const analysisId = '1';

      mockAnalysisService.remove.mockResolvedValue(undefined);

      await controller.remove(analysisId);

      expect(service.remove).toHaveBeenCalledWith(analysisId);
    });
  });
});
