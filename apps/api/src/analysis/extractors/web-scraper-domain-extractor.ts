import { Injectable, Logger } from "@nestjs/common";
import {
  DomainInfoExtractor,
  DomainDataType,
  DomainInfo,
  DomainExtractionOptions,
  ExtractorCost,
  ValidationResult,
} from "../interfaces/domain-info-extractor.interface";
import axios from "axios";
import * as cheerio from "cheerio";

@Injectable()
export class WebScraperDomainExtractor implements DomainInfoExtractor {
  private readonly logger = new Logger(WebScraperDomainExtractor.name);

  readonly name = "web-scraper";
  readonly priority = 50; // Lower priority as fallback
  readonly enabled = true; // Always enabled as it doesn't require API keys
  readonly supportedDataTypes = [
    DomainDataType.BASIC_INFO,
    DomainDataType.CONTACT_INFO,
    DomainDataType.CONTENT_ANALYSIS,
    DomainDataType.TECHNOLOGY_STACK,
  ];

  async extractDomainInfo(
    domain: string,
    options?: DomainExtractionOptions
  ): Promise<DomainInfo> {
    const startTime = Date.now();
    this.logger.debug(`Extracting domain info for ${domain} using web scraping`);

    try {
      const domainInfo: DomainInfo = {
        domain,
        extractedAt: new Date(),
        extractorName: this.name,
        metadata: {
          extractionTimeMs: 0,
          dataFreshness: "fresh",
          confidence: 0.7, // Lower confidence for scraped data
          sources: [this.name],
          errors: [],
          warnings: [],
        },
      };

      const errors: string[] = [];
      const warnings: string[] = [];

      // Scrape basic info
      try {
        const basicInfo = await this.scrapeBasicInfo(domain);
        if (basicInfo) {
          domainInfo.title = basicInfo.title;
          domainInfo.description = basicInfo.description;
          domainInfo.language = basicInfo.language;
          domainInfo.industry = basicInfo.industry;
          domainInfo.category = basicInfo.category;
        }
      } catch (error) {
        errors.push(`Failed to scrape basic info: ${error.message}`);
      }

      // Scrape contact info if requested
      if (options?.includeContactInfo !== false) {
        try {
          const contactInfo = await this.scrapeContactInfo(domain);
          if (contactInfo) {
            domainInfo.contactInfo = contactInfo;
          }
        } catch (error) {
          warnings.push(`Failed to scrape contact info: ${error.message}`);
        }
      }

      // Analyze content if requested
      try {
        const contentAnalysis = await this.analyzeContent(domain);
        if (contentAnalysis) {
          domainInfo.contentAnalysis = contentAnalysis;
        }
      } catch (error) {
        warnings.push(`Failed to analyze content: ${error.message}`);
      }

      // Detect technology stack
      try {
        const technologyStack = await this.detectTechnologyStack(domain);
        if (technologyStack) {
          domainInfo.technologyStack = technologyStack;
        }
      } catch (error) {
        warnings.push(`Failed to detect technology stack: ${error.message}`);
      }

      // Update metadata
      domainInfo.metadata!.extractionTimeMs = Date.now() - startTime;
      domainInfo.metadata!.errors = errors;
      domainInfo.metadata!.warnings = warnings;

      // Adjust confidence based on errors
      if (errors.length > 0) {
        domainInfo.metadata!.confidence = Math.max(0.3, 0.7 - (errors.length * 0.15));
      }

      this.logger.debug(
        `Web scraping extraction completed for ${domain} in ${domainInfo.metadata!.extractionTimeMs}ms ` +
        `(errors: ${errors.length}, warnings: ${warnings.length})`
      );

      return domainInfo;
    } catch (error) {
      this.logger.error(`Web scraping extraction failed for ${domain}: ${error.message}`);
      throw error;
    }
  }

  canHandle(domain: string, dataType: DomainDataType): boolean {
    // Check if we support this data type
    if (!this.supportedDataTypes.includes(dataType)) {
      return false;
    }

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  getCost(dataType: DomainDataType): ExtractorCost {
    // Web scraping costs (no API costs, just time)
    return {
      apiCalls: 0,
      estimatedDelay: 2000, // 2 seconds for scraping
      rateLimitImpact: "low",
      monetaryCost: 0,
    };
  }

  async validateConfiguration(): Promise<ValidationResult> {
    try {
      // Test basic HTTP connectivity
      const response = await axios.get("https://httpbin.org/status/200", {
        timeout: 5000,
        headers: {
          "User-Agent": "RankMesh-Bot/1.0",
        },
      });

      return {
        valid: response.status === 200,
        errors: [],
        warnings: [],
        capabilities: this.supportedDataTypes,
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`HTTP connectivity test failed: ${error.message}`],
        warnings: [],
        capabilities: [],
      };
    }
  }

  /**
   * Scrape basic information from domain homepage
   */
  private async scrapeBasicInfo(domain: string): Promise<{
    title?: string;
    description?: string;
    language?: string;
    industry?: string;
    category?: string;
  } | null> {
    try {
      const url = `https://${domain}`;
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          "User-Agent": "Mozilla/5.0 (compatible; RankMesh-Bot/1.0)",
        },
        maxRedirects: 3,
      });

      const $ = cheerio.load(response.data);

      const title = $("title").text().trim() || $('meta[property="og:title"]').attr("content");
      const description = $('meta[name="description"]').attr("content") || 
                         $('meta[property="og:description"]').attr("content");
      const language = $("html").attr("lang") || "en";

      // Try to determine industry/category from content
      const bodyText = $("body").text().toLowerCase();
      const industry = this.detectIndustry(bodyText);
      const category = this.detectCategory(bodyText);

      return {
        title,
        description,
        language,
        industry,
        category,
      };
    } catch (error) {
      this.logger.warn(`Failed to scrape basic info for ${domain}: ${error.message}`);
      return null;
    }
  }

  /**
   * Scrape contact information
   */
  private async scrapeContactInfo(domain: string): Promise<{
    emails: string[];
    phoneNumbers: string[];
    socialProfiles: string[];
    contactPage?: string;
    contactForm?: string;
    address?: string;
  } | null> {
    try {
      const contactInfo = {
        emails: [] as string[],
        phoneNumbers: [] as string[],
        socialProfiles: [] as string[],
        contactPage: undefined as string | undefined,
        contactForm: undefined as string | undefined,
        address: undefined as string | undefined,
      };

      // Check homepage first
      await this.extractContactFromPage(`https://${domain}`, contactInfo);

      // Try common contact pages
      const contactPages = ["/contact", "/contact-us", "/about", "/about-us"];
      for (const page of contactPages) {
        try {
          await this.extractContactFromPage(`https://${domain}${page}`, contactInfo);
          if (!contactInfo.contactPage && (contactInfo.emails.length > 0 || contactInfo.phoneNumbers.length > 0)) {
            contactInfo.contactPage = `https://${domain}${page}`;
          }
        } catch (error) {
          // Ignore errors for individual pages
        }
      }

      return contactInfo.emails.length > 0 || contactInfo.phoneNumbers.length > 0 ? contactInfo : null;
    } catch (error) {
      this.logger.warn(`Failed to scrape contact info for ${domain}: ${error.message}`);
      return null;
    }
  }

  /**
   * Extract contact information from a specific page
   */
  private async extractContactFromPage(url: string, contactInfo: any): Promise<void> {
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        "User-Agent": "Mozilla/5.0 (compatible; RankMesh-Bot/1.0)",
      },
      maxRedirects: 3,
    });

    const $ = cheerio.load(response.data);
    const text = $("body").text();

    // Extract emails
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emails = text.match(emailRegex) || [];
    contactInfo.emails.push(...emails.filter(email => !contactInfo.emails.includes(email)));

    // Extract phone numbers
    const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const phones = text.match(phoneRegex) || [];
    contactInfo.phoneNumbers.push(...phones.filter(phone => !contactInfo.phoneNumbers.includes(phone)));

    // Extract social profiles
    const socialRegex = /(https?:\/\/)?(www\.)?(facebook|twitter|linkedin|instagram|youtube)\.com\/[a-zA-Z0-9._-]+/g;
    const socials = text.match(socialRegex) || [];
    contactInfo.socialProfiles.push(...socials.filter(social => !contactInfo.socialProfiles.includes(social)));

    // Check for contact form
    if ($('form[action*="contact"]').length > 0 || $('form').find('input[type="email"]').length > 0) {
      contactInfo.contactForm = url;
    }
  }

  /**
   * Analyze content
   */
  private async analyzeContent(domain: string): Promise<any> {
    // Basic content analysis implementation
    // In a real implementation, this could use NLP libraries
    return {
      wordCount: 0,
      readabilityScore: 0,
      sentiment: "neutral" as const,
      topics: [],
      entities: [],
    };
  }

  /**
   * Detect technology stack
   */
  private async detectTechnologyStack(domain: string): Promise<any> {
    // Basic technology detection
    // In a real implementation, this could use Wappalyzer or similar
    return {
      cms: undefined,
      framework: undefined,
      analytics: [],
      advertising: [],
      ecommerce: [],
      hosting: undefined,
      cdn: undefined,
      ssl: true,
    };
  }

  /**
   * Detect industry from content
   */
  private detectIndustry(text: string): string | undefined {
    const industryKeywords = {
      "Technology": ["software", "tech", "development", "programming", "app"],
      "Healthcare": ["health", "medical", "doctor", "hospital", "clinic"],
      "Finance": ["finance", "bank", "investment", "insurance", "loan"],
      "Education": ["education", "school", "university", "learning", "course"],
      "Retail": ["shop", "store", "buy", "sell", "product", "ecommerce"],
    };

    for (const [industry, keywords] of Object.entries(industryKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return industry;
      }
    }

    return undefined;
  }

  /**
   * Detect category from content
   */
  private detectCategory(text: string): string | undefined {
    const categoryKeywords = {
      "Blog": ["blog", "article", "post", "news"],
      "SaaS": ["saas", "software as a service", "subscription", "cloud"],
      "Services": ["service", "consulting", "agency", "professional"],
      "E-commerce": ["shop", "cart", "checkout", "product", "buy"],
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return category;
      }
    }

    return undefined;
  }
}
