import { Injectable, Logger, Inject } from "@nestjs/common";
import {
  DomainInfoExtractor,
  DomainDataType,
  DomainInfo,
  DomainExtractionOptions,
  ExtractorCost,
  ValidationResult,
} from "../interfaces/domain-info-extractor.interface";
import { SimilarWebService } from "../../third-party/similarweb/similarweb.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class SimilarWebDomainExtractor implements DomainInfoExtractor {
  private readonly logger = new Logger(SimilarWebDomainExtractor.name);

  readonly name = "similarweb";
  readonly priority = 85; // High priority for traffic data
  readonly enabled: boolean;
  readonly supportedDataTypes = [
    DomainDataType.TRAFFIC_DATA,
    DomainDataType.KEYWORDS,
  ];

  constructor(
    @Inject(SimilarWebService)
    private readonly similarWebService: SimilarWebService,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    this.enabled = !!this.configService.get("SIMILARWEB_API_KEY");
  }

  async extractDomainInfo(
    domain: string,
    options?: DomainExtractionOptions
  ): Promise<DomainInfo> {
    const startTime = Date.now();
    this.logger.debug(`Extracting domain info for ${domain} using SimilarWeb`);

    try {
      const domainInfo: DomainInfo = {
        domain,
        extractedAt: new Date(),
        extractorName: this.name,
        metadata: {
          extractionTimeMs: 0,
          dataFreshness: "fresh",
          confidence: 0.85,
          sources: [this.name],
          errors: [],
          warnings: [],
        },
      };

      const errors: string[] = [];
      const warnings: string[] = [];

      // Extract traffic data if requested
      if (options?.includeTrafficData !== false) {
        try {
          const trafficData = await this.similarWebService.getTrafficData(
            domain
          );
          if (trafficData) {
            domainInfo.trafficData = {
              visits: trafficData.visits,
              uniqueVisitors: trafficData.unique_visitors,
              pageViews: trafficData.page_views,
              bounceRate: trafficData.bounce_rate,
              avgSessionDuration: trafficData.avg_visit_duration,
              trend: "stable", // We'll determine this from trends data
              topCountries: [], // Will be populated from geo data
            };
          }
        } catch (error) {
          errors.push(`Failed to get traffic data: ${error.message}`);
        }
      }

      // Extract keywords if requested
      if (options?.includeKeywords !== false) {
        try {
          // SimilarWeb doesn't provide keyword data in the same way as Ahrefs
          // This would need to be implemented if SimilarWeb adds keyword endpoints
          this.logger.debug(
            `Keyword extraction not available for SimilarWeb service`
          );
        } catch (error) {
          warnings.push(`Failed to get keywords: ${error.message}`);
        }
      }

      // Update metadata
      domainInfo.metadata!.extractionTimeMs = Date.now() - startTime;
      domainInfo.metadata!.errors = errors;
      domainInfo.metadata!.warnings = warnings;

      // Adjust confidence based on errors
      if (errors.length > 0) {
        domainInfo.metadata!.confidence = Math.max(
          0.3,
          0.85 - errors.length * 0.2
        );
      }

      this.logger.debug(
        `SimilarWeb extraction completed for ${domain} in ${
          domainInfo.metadata!.extractionTimeMs
        }ms ` + `(errors: ${errors.length}, warnings: ${warnings.length})`
      );

      return domainInfo;
    } catch (error) {
      this.logger.error(
        `SimilarWeb extraction failed for ${domain}: ${error.message}`
      );
      throw error;
    }
  }

  canHandle(domain: string, dataType: DomainDataType): boolean {
    if (!this.enabled) {
      return false;
    }

    // Check if we support this data type
    if (!this.supportedDataTypes.includes(dataType)) {
      return false;
    }

    // Basic domain validation
    const domainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  getCost(dataType: DomainDataType): ExtractorCost {
    // SimilarWeb API costs and rate limits
    const costMap: Record<DomainDataType, ExtractorCost> = {
      [DomainDataType.TRAFFIC_DATA]: {
        apiCalls: 1,
        estimatedDelay: 60000, // 1 minute between requests (conservative)
        rateLimitImpact: "high",
        monetaryCost: 50, // 50 cents per request (expensive)
      },
      [DomainDataType.KEYWORDS]: {
        apiCalls: 1,
        estimatedDelay: 60000, // 1 minute between requests
        rateLimitImpact: "high",
        monetaryCost: 30, // 30 cents per request
      },
      [DomainDataType.BASIC_INFO]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.DOMAIN_RATING]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.BACKLINKS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTACT_INFO]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.WHOIS_DATA]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.SOCIAL_PROFILES]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.TECHNOLOGY_STACK]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTENT_ANALYSIS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
    };

    return (
      costMap[dataType] || {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      }
    );
  }

  async validateConfiguration(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const capabilities: DomainDataType[] = [];

    try {
      // Check if API key is configured
      const apiKey = this.configService.get("SIMILARWEB_API_KEY");
      if (!apiKey) {
        errors.push("SIMILARWEB_API_KEY not configured");
      } else {
        // Test API connectivity
        try {
          await this.similarWebService.getTrafficData("example.com");
          capabilities.push(...this.supportedDataTypes);
        } catch (error) {
          if (
            error.message.includes("unauthorized") ||
            error.message.includes("invalid")
          ) {
            errors.push("Invalid SimilarWeb API key");
          } else if (error.message.includes("rate limit")) {
            warnings.push(
              "SimilarWeb API rate limit reached during validation"
            );
            capabilities.push(...this.supportedDataTypes);
          } else {
            warnings.push(`SimilarWeb API test failed: ${error.message}`);
            capabilities.push(...this.supportedDataTypes);
          }
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        capabilities,
        rateLimits: {
          requestsPerMinute: 1,
          requestsPerHour: 60,
          requestsPerDay: 1000,
        },
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Validation error: ${error.message}`],
        warnings,
        capabilities: [],
      };
    }
  }

  /**
   * Determine traffic trend from monthly visits data
   */
  private determineTrend(
    monthlyVisits?: Array<{ month: string; visits: number }>
  ): "upward" | "downward" | "stable" {
    if (!monthlyVisits || monthlyVisits.length < 2) {
      return "stable";
    }

    // Compare last 3 months if available
    const recentMonths = monthlyVisits.slice(-3);
    if (recentMonths.length < 2) {
      return "stable";
    }

    const firstMonth = recentMonths[0].visits;
    const lastMonth = recentMonths[recentMonths.length - 1].visits;

    const changePercentage = ((lastMonth - firstMonth) / firstMonth) * 100;

    if (changePercentage > 10) {
      return "upward";
    } else if (changePercentage < -10) {
      return "downward";
    } else {
      return "stable";
    }
  }
}
