import { Injectable, Logger, Inject } from "@nestjs/common";
import {
  DomainInfoExtractor,
  DomainDataType,
  DomainInfo,
  DomainExtractionOptions,
  ExtractorCost,
  ValidationResult,
} from "../interfaces/domain-info-extractor.interface";
import { WhoisXmlService } from "../../third-party/whoisxml/whoisxml.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class WhoisXmlDomainExtractor implements DomainInfoExtractor {
  private readonly logger = new Logger(WhoisXmlDomainExtractor.name);

  readonly name = "whoisxml";
  readonly priority = 80; // Medium-high priority for WHOIS data
  readonly enabled: boolean;
  readonly supportedDataTypes = [
    DomainDataType.WHOIS_DATA,
    DomainDataType.BASIC_INFO,
  ];

  constructor(
    @Inject(WhoisXmlService)
    private readonly whoisXmlService: WhoisXmlService,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    this.enabled = !!this.configService.get("WHOISXML_API_KEY");
  }

  async extractDomainInfo(
    domain: string,
    options?: DomainExtractionOptions
  ): Promise<DomainInfo> {
    const startTime = Date.now();
    this.logger.debug(`Extracting domain info for ${domain} using WhoisXML`);

    try {
      const domainInfo: DomainInfo = {
        domain,
        extractedAt: new Date(),
        extractorName: this.name,
        metadata: {
          extractionTimeMs: 0,
          dataFreshness: "fresh",
          confidence: 0.95, // WHOIS data is very reliable
          sources: [this.name],
          errors: [],
          warnings: [],
        },
      };

      const errors: string[] = [];
      const warnings: string[] = [];

      // Extract WHOIS data
      try {
        const whoisData = await this.whoisXmlService.getWhoisData(domain);
        if (whoisData) {
          domainInfo.whoisData = {
            registrar: whoisData.registrar,
            registrationDate: whoisData.creation_date
              ? new Date(whoisData.creation_date)
              : undefined,
            expirationDate: whoisData.expiration_date
              ? new Date(whoisData.expiration_date)
              : undefined,
            lastUpdated: whoisData.updated_date
              ? new Date(whoisData.updated_date)
              : undefined,
            nameServers: whoisData.name_servers,
            registrantCountry: whoisData.registrant_country,
            ageInYears: whoisData.domain_age_years,
          };

          // Set basic info from WHOIS
          domainInfo.country = whoisData.registrant_country;
          domainInfo.websiteAge = whoisData.domain_age_years;
        }
      } catch (error) {
        errors.push(`Failed to get WHOIS data: ${error.message}`);
      }

      // Extract additional domain info if available
      try {
        // WhoisXML service doesn't provide additional domain info like title, description, etc.
        // This would need to be implemented if WhoisXML adds such endpoints
        this.logger.debug(
          `Additional domain info extraction not available for WhoisXML service`
        );
      } catch (error) {
        warnings.push(`Failed to get additional domain info: ${error.message}`);
      }

      // Update metadata
      domainInfo.metadata!.extractionTimeMs = Date.now() - startTime;
      domainInfo.metadata!.errors = errors;
      domainInfo.metadata!.warnings = warnings;

      // Adjust confidence based on errors
      if (errors.length > 0) {
        domainInfo.metadata!.confidence = Math.max(
          0.5,
          0.95 - errors.length * 0.2
        );
      }

      this.logger.debug(
        `WhoisXML extraction completed for ${domain} in ${
          domainInfo.metadata!.extractionTimeMs
        }ms ` + `(errors: ${errors.length}, warnings: ${warnings.length})`
      );

      return domainInfo;
    } catch (error) {
      this.logger.error(
        `WhoisXML extraction failed for ${domain}: ${error.message}`
      );
      throw error;
    }
  }

  canHandle(domain: string, dataType: DomainDataType): boolean {
    if (!this.enabled) {
      return false;
    }

    // Check if we support this data type
    if (!this.supportedDataTypes.includes(dataType)) {
      return false;
    }

    // Basic domain validation
    const domainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  getCost(dataType: DomainDataType): ExtractorCost {
    // WhoisXML API costs and rate limits
    const costMap: Record<DomainDataType, ExtractorCost> = {
      [DomainDataType.WHOIS_DATA]: {
        apiCalls: 1,
        estimatedDelay: 60000, // 1 minute between requests (conservative)
        rateLimitImpact: "medium",
        monetaryCost: 2, // 2 cents per request
      },
      [DomainDataType.BASIC_INFO]: {
        apiCalls: 1,
        estimatedDelay: 30000, // 30 seconds between requests
        rateLimitImpact: "medium",
        monetaryCost: 1, // 1 cent per request
      },
      [DomainDataType.DOMAIN_RATING]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.TRAFFIC_DATA]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.KEYWORDS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.BACKLINKS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTACT_INFO]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.SOCIAL_PROFILES]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.TECHNOLOGY_STACK]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTENT_ANALYSIS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
    };

    return (
      costMap[dataType] || {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      }
    );
  }

  async validateConfiguration(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const capabilities: DomainDataType[] = [];

    try {
      // Check if API key is configured
      const apiKey = this.configService.get("WHOISXML_API_KEY");
      if (!apiKey) {
        errors.push("WHOISXML_API_KEY not configured");
      } else {
        // Test API connectivity
        try {
          await this.whoisXmlService.getDomainAge("example.com");
          capabilities.push(...this.supportedDataTypes);
        } catch (error) {
          if (
            error.message.includes("unauthorized") ||
            error.message.includes("invalid")
          ) {
            errors.push("Invalid WhoisXML API key");
          } else if (error.message.includes("rate limit")) {
            warnings.push("WhoisXML API rate limit reached during validation");
            capabilities.push(...this.supportedDataTypes);
          } else {
            warnings.push(`WhoisXML API test failed: ${error.message}`);
            capabilities.push(...this.supportedDataTypes);
          }
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        capabilities,
        rateLimits: {
          requestsPerMinute: 1,
          requestsPerHour: 60,
          requestsPerDay: 1000,
        },
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Validation error: ${error.message}`],
        warnings,
        capabilities: [],
      };
    }
  }
}
