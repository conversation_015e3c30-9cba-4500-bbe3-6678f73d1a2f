import { Injectable, Logger, Inject } from "@nestjs/common";
import {
  DomainInfoExtractor,
  DomainDataType,
  DomainInfo,
  DomainExtractionOptions,
  ExtractorCost,
  ValidationResult,
} from "../interfaces/domain-info-extractor.interface";
import { AhrefsService } from "../../third-party/ahrefs/ahrefs.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class AhrefsDomainExtractor implements DomainInfoExtractor {
  private readonly logger = new Logger(AhrefsDomainExtractor.name);

  readonly name = "ahrefs";
  readonly priority = 90; // High priority for Ahrefs
  readonly enabled: boolean;
  readonly supportedDataTypes = [
    DomainDataType.DOMAIN_RATING,
    DomainDataType.BACKLINKS,
    DomainDataType.KEYWORDS,
    DomainDataType.TRAFFIC_DATA,
  ];

  constructor(
    @Inject(AhrefsService)
    private readonly ahrefsService: AhrefsService,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    this.enabled = !!this.configService.get("AHREFS_API_KEY");
  }

  async extractDomainInfo(
    domain: string,
    options?: DomainExtractionOptions
  ): Promise<DomainInfo> {
    const startTime = Date.now();
    this.logger.debug(`Extracting domain info for ${domain} using Ahrefs`);

    try {
      const domainInfo: DomainInfo = {
        domain,
        extractedAt: new Date(),
        extractorName: this.name,
        metadata: {
          extractionTimeMs: 0,
          dataFreshness: "fresh",
          confidence: 0.9,
          sources: [this.name],
          errors: [],
          warnings: [],
        },
      };

      const errors: string[] = [];
      const warnings: string[] = [];

      // Extract domain rating
      try {
        const drData = await this.ahrefsService.getDomainRating(domain);
        if (drData) {
          domainInfo.domainRating = drData.domain_rating;
          domainInfo.domainAuthority = drData.domain_rating; // Use DR as DA proxy
        }
      } catch (error) {
        errors.push(`Failed to get domain rating: ${error.message}`);
      }

      // Extract backlinks if requested
      if (options?.includeBacklinks !== false) {
        try {
          const backlinksData = await this.ahrefsService.getBacklinks(
            domain,
            options?.maxBacklinks || 100
          );
          if (backlinksData && Array.isArray(backlinksData)) {
            domainInfo.backlinks = {
              totalBacklinks: backlinksData.length,
              referringDomains: new Set(backlinksData.map((bl) => bl.url_from))
                .size,
              topBacklinks: backlinksData.map((bl: any) => ({
                sourceUrl: bl.url_from,
                sourceDomain: new URL(bl.url_from).hostname,
                anchor: bl.anchor,
                domainRating: bl.domain_rating,
                firstSeen: bl.first_seen ? new Date(bl.first_seen) : undefined,
                lastSeen: bl.last_seen ? new Date(bl.last_seen) : undefined,
              })),
            };
          }
        } catch (error) {
          warnings.push(`Failed to get backlinks: ${error.message}`);
        }
      }

      // Extract keywords if requested
      if (options?.includeKeywords !== false) {
        try {
          const keywordsData = await this.ahrefsService.getOrganicKeywords(
            domain,
            options?.maxKeywords || 100
          );
          if (keywordsData && Array.isArray(keywordsData)) {
            domainInfo.keywords = keywordsData.map((kw: any) => ({
              keyword: kw.keyword,
              position: kw.position,
              searchVolume: kw.search_volume,
              difficulty: kw.keyword_difficulty,
              traffic: kw.traffic,
              url: kw.url,
            }));
          }
        } catch (error) {
          warnings.push(`Failed to get keywords: ${error.message}`);
        }
      }

      // Note: Ahrefs doesn't provide traffic data directly
      // This would be handled by SimilarWeb extractor

      // Update metadata
      domainInfo.metadata!.extractionTimeMs = Date.now() - startTime;
      domainInfo.metadata!.errors = errors;
      domainInfo.metadata!.warnings = warnings;

      // Adjust confidence based on errors
      if (errors.length > 0) {
        domainInfo.metadata!.confidence = Math.max(
          0.3,
          0.9 - errors.length * 0.2
        );
      }

      this.logger.debug(
        `Ahrefs extraction completed for ${domain} in ${
          domainInfo.metadata!.extractionTimeMs
        }ms ` + `(errors: ${errors.length}, warnings: ${warnings.length})`
      );

      return domainInfo;
    } catch (error) {
      this.logger.error(
        `Ahrefs extraction failed for ${domain}: ${error.message}`
      );
      throw error;
    }
  }

  canHandle(domain: string, dataType: DomainDataType): boolean {
    if (!this.enabled) {
      return false;
    }

    // Check if we support this data type
    if (!this.supportedDataTypes.includes(dataType)) {
      return false;
    }

    // Basic domain validation
    const domainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  getCost(dataType: DomainDataType): ExtractorCost {
    // Ahrefs API costs and rate limits
    const costMap: Record<DomainDataType, ExtractorCost> = {
      [DomainDataType.DOMAIN_RATING]: {
        apiCalls: 1,
        estimatedDelay: 600, // 600ms between requests
        rateLimitImpact: "medium",
        monetaryCost: 5, // 5 cents per request
      },
      [DomainDataType.BACKLINKS]: {
        apiCalls: 1,
        estimatedDelay: 1000, // 1s between requests
        rateLimitImpact: "high",
        monetaryCost: 10, // 10 cents per request
      },
      [DomainDataType.KEYWORDS]: {
        apiCalls: 1,
        estimatedDelay: 1000, // 1s between requests
        rateLimitImpact: "high",
        monetaryCost: 10, // 10 cents per request
      },
      [DomainDataType.TRAFFIC_DATA]: {
        apiCalls: 1,
        estimatedDelay: 800, // 800ms between requests
        rateLimitImpact: "medium",
        monetaryCost: 8, // 8 cents per request
      },
      [DomainDataType.BASIC_INFO]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTACT_INFO]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.WHOIS_DATA]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.SOCIAL_PROFILES]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.TECHNOLOGY_STACK]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
      [DomainDataType.CONTENT_ANALYSIS]: {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      },
    };

    return (
      costMap[dataType] || {
        apiCalls: 0,
        estimatedDelay: 0,
        rateLimitImpact: "low",
        monetaryCost: 0,
      }
    );
  }

  async validateConfiguration(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const capabilities: DomainDataType[] = [];

    try {
      // Check if API key is configured
      const apiKey = this.configService.get("AHREFS_API_KEY");
      if (!apiKey) {
        errors.push("AHREFS_API_KEY not configured");
      } else {
        // Test API connectivity
        try {
          await this.ahrefsService.getDomainRating("example.com");
          capabilities.push(...this.supportedDataTypes);
        } catch (error) {
          if (
            error.message.includes("unauthorized") ||
            error.message.includes("invalid")
          ) {
            errors.push("Invalid Ahrefs API key");
          } else if (error.message.includes("rate limit")) {
            warnings.push("Ahrefs API rate limit reached during validation");
            capabilities.push(...this.supportedDataTypes);
          } else {
            warnings.push(`Ahrefs API test failed: ${error.message}`);
            capabilities.push(...this.supportedDataTypes);
          }
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        capabilities,
        rateLimits: {
          requestsPerMinute: 100,
          requestsPerHour: 6000,
          requestsPerDay: 144000,
        },
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Validation error: ${error.message}`],
        warnings,
        capabilities: [],
      };
    }
  }
}
