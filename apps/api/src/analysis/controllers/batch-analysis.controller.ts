import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { GetUser } from "../../auth/decorators/get-user.decorator";
import { User } from "../../users/entities/user.entity";
import {
  QueueService,
  BatchAnalysisJobData as BatchJobData,
} from "../../queue/queue.service";
import {
  BatchAnalysisRequestDto,
  BatchAnalysisResultDto,
  BatchAnalysisStatusDto,
} from "../dto/batch-analysis.dto";
import * as csv from "csv-parser";
import { Readable } from "stream";

// Interface for batch job result
interface BatchJobResult {
  processedDomains: number;
  failedDomains: number;
  averageScore?: number;
  topDomains?: any[];
  results?: Array<{
    targetDomainId: string;
    status: string;
    score?: number;
    error?: string;
    processingTimeMs?: number;
    completedAt?: string;
  }>;
}

@ApiTags("Batch Analysis")
@Controller("analysis/batch")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth("JWT-auth")
export class BatchAnalysisController {
  constructor(private readonly queueService: QueueService) {}

  @Post()
  @ApiOperation({ summary: "Start batch domain analysis" })
  @ApiResponse({
    status: 201,
    description: "Batch analysis started successfully",
    type: BatchAnalysisResultDto,
  })
  @ApiResponse({ status: 400, description: "Invalid request data" })
  @ApiResponse({ status: 429, description: "Rate limit exceeded" })
  async startBatchAnalysis(
    @Body() batchRequest: BatchAnalysisRequestDto,
    @GetUser() user: User
  ): Promise<{
    success: boolean;
    batchId: string;
    message: string;
    totalDomains: number;
    estimatedCompletionTime: string;
  }> {
    try {
      // Generate batch ID if not provided
      const batchId =
        batchRequest.batchId ||
        `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Prepare batch job data
      const jobData: BatchJobData = {
        targetDomainIds: batchRequest.domains,
        userId: user.id,
        batchId,
        userDomainId: batchRequest.userDomain,
        options: {
          includeContactInfo: batchRequest.includeContactInfo,
          includeKeywords: batchRequest.includeKeywords,
          includeTrafficData: batchRequest.includeTrafficData,
          maxConcurrency: batchRequest.maxConcurrency || 5,
        },
      };

      // Add batch job to queue
      const job = await this.queueService.addBatchAnalysisJob(jobData, {
        priority: 1, // Normal priority for batch jobs
      });

      // Estimate completion time (rough calculation)
      const estimatedTimePerDomain = 30; // seconds
      const totalEstimatedTime = Math.ceil(
        (batchRequest.domains.length * estimatedTimePerDomain) /
          (batchRequest.maxConcurrency || 5)
      );
      const completionTime = new Date(Date.now() + totalEstimatedTime * 1000);

      return {
        success: true,
        batchId,
        message: "Batch analysis started successfully",
        totalDomains: batchRequest.domains.length,
        estimatedCompletionTime: completionTime.toISOString(),
      };
    } catch (error) {
      throw new HttpException(
        `Failed to start batch analysis: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post("upload")
  @ApiOperation({ summary: "Upload CSV file for batch analysis" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 201,
    description: "CSV uploaded and batch analysis started",
    type: BatchAnalysisResultDto,
  })
  @ApiResponse({ status: 400, description: "Invalid CSV file" })
  @UseInterceptors(FileInterceptor("file"))
  async uploadCsvForBatchAnalysis(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: "text/csv" }),
        ],
      })
    )
    file: Express.Multer.File,
    @Query("userDomain") userDomain?: string,
    @Query("includeContactInfo") includeContactInfo?: boolean,
    @Query("includeKeywords") includeKeywords?: boolean,
    @Query("includeTrafficData") includeTrafficData?: boolean,
    @Query("maxConcurrency") maxConcurrency?: number,
    @GetUser() user?: User
  ): Promise<{
    success: boolean;
    batchId: string;
    message: string;
    totalDomains: number;
    domains: string[];
  }> {
    try {
      // Parse CSV file
      const domains = await this.parseCsvFile(file.buffer);

      if (domains.length === 0) {
        throw new HttpException(
          "No valid domains found in CSV file",
          HttpStatus.BAD_REQUEST
        );
      }

      if (domains.length > 100) {
        throw new HttpException(
          "CSV file contains too many domains (maximum 100 allowed)",
          HttpStatus.BAD_REQUEST
        );
      }

      // Create batch request
      const batchRequest: BatchAnalysisRequestDto = {
        domains,
        userDomain,
        includeContactInfo: includeContactInfo !== false,
        includeKeywords: includeKeywords !== false,
        includeTrafficData: includeTrafficData !== false,
        maxConcurrency: maxConcurrency ? Number(maxConcurrency) : 5,
      };

      // Start batch analysis
      const result = await this.startBatchAnalysis(batchRequest, user!);

      return {
        success: true,
        batchId: result.batchId,
        message: `CSV processed successfully. ${domains.length} domains queued for analysis.`,
        totalDomains: domains.length,
        domains,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to process CSV file: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(":batchId")
  @ApiOperation({ summary: "Get batch analysis results" })
  @ApiParam({ name: "batchId", description: "Batch identifier" })
  @ApiResponse({
    status: 200,
    description: "Batch analysis results retrieved successfully",
    type: BatchAnalysisResultDto,
  })
  @ApiResponse({ status: 404, description: "Batch not found" })
  async getBatchResults(
    @Param("batchId") batchId: string,
    @GetUser() user: User
  ): Promise<BatchAnalysisResultDto> {
    try {
      // Get job status from queue
      const jobStatus = await this.queueService.getJobStatus(
        "batch-analysis",
        batchId
      );

      if (!jobStatus) {
        throw new HttpException(
          `Batch ${batchId} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      // Transform job status to batch result format
      const result: BatchAnalysisResultDto = {
        batchId,
        totalDomains:
          (jobStatus.data as BatchJobData)?.targetDomainIds?.length || 0,
        completedDomains: 0,
        failedDomains: 0,
        pendingDomains: 0,
        status: this.mapJobStatusToBatchStatus(jobStatus.status),
        results: [],
        createdAt: jobStatus.createdAt,
        completedAt: jobStatus.finishedAt,
        totalProcessingTimeMs: jobStatus.finishedAt
          ? jobStatus.finishedAt.getTime() - jobStatus.createdAt.getTime()
          : undefined,
        progress: jobStatus.progress,
      };

      // If job is completed, get detailed results
      if (jobStatus.status === "completed" && jobStatus.result) {
        const batchResult = jobStatus.result as BatchJobResult;
        result.completedDomains = batchResult.processedDomains || 0;
        result.failedDomains = batchResult.failedDomains || 0;
        result.pendingDomains =
          result.totalDomains - result.completedDomains - result.failedDomains;
        result.averageScore = batchResult.averageScore;
        result.topDomains = batchResult.topDomains;
        result.results =
          batchResult.results?.map((item) => ({
            domain: item.targetDomainId,
            status: item.status as
              | "completed"
              | "failed"
              | "pending"
              | "processing",
            score: item.score,
            error: item.error,
            processingTimeMs: item.processingTimeMs,
            completedAt: item.completedAt
              ? new Date(item.completedAt)
              : undefined,
          })) || [];
      }

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to get batch results: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(":batchId/status")
  @ApiOperation({ summary: "Get batch analysis status" })
  @ApiParam({ name: "batchId", description: "Batch identifier" })
  @ApiResponse({
    status: 200,
    description: "Batch status retrieved successfully",
    type: BatchAnalysisStatusDto,
  })
  @ApiResponse({ status: 404, description: "Batch not found" })
  async getBatchStatus(
    @Param("batchId") batchId: string,
    @GetUser() user: User
  ): Promise<BatchAnalysisStatusDto> {
    try {
      const jobStatus = await this.queueService.getJobStatus(
        "batch-analysis",
        batchId
      );

      if (!jobStatus) {
        throw new HttpException(
          `Batch ${batchId} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      const totalCount =
        (jobStatus.data as BatchJobData)?.targetDomainIds?.length || 0;
      const completedCount = Math.floor(
        (jobStatus.progress / 100) * totalCount
      );
      const estimatedTimeRemaining = this.calculateEstimatedTimeRemaining(
        jobStatus.progress,
        jobStatus.createdAt
      );

      return {
        batchId,
        status: this.mapJobStatusToBatchStatus(jobStatus.status),
        progress: jobStatus.progress,
        estimatedTimeRemaining,
        completedCount,
        totalCount,
        currentDomain: undefined, // This property doesn't exist in BatchJobData
        error: jobStatus.error,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to get batch status: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(":batchId/export")
  @ApiOperation({ summary: "Export batch results as CSV" })
  @ApiParam({ name: "batchId", description: "Batch identifier" })
  @ApiQuery({ name: "format", enum: ["csv", "json"], required: false })
  @ApiResponse({
    status: 200,
    description: "Batch results exported successfully",
  })
  @ApiResponse({ status: 404, description: "Batch not found" })
  async exportBatchResults(
    @Param("batchId") batchId: string,
    @Query("format") format: "csv" | "json" = "csv",
    @GetUser() user: User
  ): Promise<any> {
    try {
      const batchResults = await this.getBatchResults(batchId, user);

      if (format === "json") {
        return batchResults;
      }

      // Generate CSV
      const csvData = this.generateCsvFromResults(batchResults);
      return {
        success: true,
        format: "csv",
        data: csvData,
        filename: `batch_${batchId}_results.csv`,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to export batch results: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Parse CSV file to extract domain list
   */
  private async parseCsvFile(buffer: Buffer): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const domains: string[] = [];
      const stream = Readable.from(buffer.toString());

      stream
        .pipe(csv({ headers: false }))
        .on("data", (row) => {
          // Try to find domain in any column
          const values = Object.values(row) as string[];
          for (const value of values) {
            const domain = this.extractDomainFromValue(value);
            if (domain && !domains.includes(domain)) {
              domains.push(domain);
            }
          }
        })
        .on("end", () => resolve(domains))
        .on("error", (error) => reject(error));
    });
  }

  /**
   * Extract domain from a CSV value
   */
  private extractDomainFromValue(value: string): string | null {
    if (!value || typeof value !== "string") return null;

    // Remove common prefixes and clean up
    const cleaned = value
      .trim()
      .toLowerCase()
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .replace(/\/.*$/, "");

    // Validate domain format
    const domainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(cleaned) ? cleaned : null;
  }

  /**
   * Map job status to batch status
   */
  private mapJobStatusToBatchStatus(
    jobStatus: string
  ): "pending" | "processing" | "completed" | "failed" | "partial" {
    switch (jobStatus) {
      case "waiting":
      case "delayed":
        return "pending";
      case "active":
        return "processing";
      case "completed":
        return "completed";
      case "failed":
        return "failed";
      default:
        return "pending";
    }
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateEstimatedTimeRemaining(
    progress: number,
    startTime: Date
  ): number {
    if (progress <= 0) return 0;
    if (progress >= 100) return 0;

    const elapsedTime = Date.now() - startTime.getTime();
    const estimatedTotalTime = (elapsedTime / progress) * 100;
    const remainingTime = estimatedTotalTime - elapsedTime;

    return Math.max(0, Math.ceil(remainingTime / 1000)); // Return in seconds
  }

  /**
   * Generate CSV from batch results
   */
  private generateCsvFromResults(batchResults: BatchAnalysisResultDto): string {
    const headers = [
      "Domain",
      "Status",
      "Score",
      "Processing Time (ms)",
      "Completed At",
      "Error",
    ];

    const rows = batchResults.results.map((result) => [
      result.domain,
      result.status,
      result.score?.toString() || "",
      result.processingTimeMs?.toString() || "",
      result.completedAt?.toISOString() || "",
      result.error || "",
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((cell) => `"${cell}"`).join(","))
      .join("\n");

    return csvContent;
  }
}
