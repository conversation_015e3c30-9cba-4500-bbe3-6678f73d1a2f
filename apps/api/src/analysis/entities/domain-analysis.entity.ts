import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm"
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { TargetDomain } from "../../target-domains/entities/target-domain.entity"
import { Domain } from "../../domains/entities/domain.entity"

@Entity("domain_analyses")
export class DomainAnalysis {
  @PrimaryGeneratedColumn("uuid")
  id: string

  @Column("uuid")
  targetDomainId: string

  @Column("uuid")
  userDomainId: string

  @Column({ type: "float" })
  relevanceScore: number

  @Column({ nullable: true, type: "int" })
  domainAuthority?: number

  @Column({ nullable: true, type: "int" })
  backlinks?: number

  @Column({ nullable: true, type: "int" })
  traffic?: number

  @Column({ nullable: true, type: "varchar", length: 20 })
  trafficTrend?: "upward" | "downward" | "stable"

  @Column({ nullable: true, type: "simple-array" })
  topKeywords?: string[]

  @Column({ nullable: true, type: "simple-array" })
  topPages?: string[]

  @Column({ nullable: true })
  industry?: string

  @Column({ nullable: true })
  category?: string

  @Column({ nullable: true })
  topCountry?: string

  @Column({ nullable: true, type: "int" })
  websiteAge?: number

  @Column({ nullable: true })
  language?: string

  @Column({ nullable: true })
  contactEmail?: string

  @Column({ nullable: true })
  contactPage?: string

  @Column({ type: "simple-array", nullable: true })
  categories?: string[]

  @Column({ type: "simple-array", nullable: true })
  keywords?: string[]

  @Column({ nullable: true })
  lastUpdated?: Date

  @ManyToOne(
    () => TargetDomain,
    (targetDomain) => targetDomain.analyses,
  )
  @JoinColumn({ name: "targetDomainId" })
  targetDomain: TargetDomain

  // @ManyToOne(
  //   () => Domain,
  //   (domain) => domain.analyses,
  // )
  // @JoinColumn({ name: "userDomainId" })
  // userDomain: Domain//TODO: check this one out

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
