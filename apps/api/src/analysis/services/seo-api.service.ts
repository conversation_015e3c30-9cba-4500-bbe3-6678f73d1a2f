import { Injectable, Logger, HttpException, HttpStatus } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { firstValueFrom } from "rxjs";
import { catchError } from "rxjs/operators";

export interface SEOMetrics {
  domainAuthority: number;
  pageAuthority: number;
  backlinks: number;
  organicTraffic: number;
  trafficValue: number;
  keywordRankings: {
    keyword: string;
    position: number;
    volume: number;
    difficulty: number;
  }[];
  topPages: {
    url: string;
    traffic: number;
    backlinks: number;
  }[];
  competitors: {
    domain: string;
    commonKeywords: number;
    domainAuthority: number;
  }[];
}

@Injectable()
export class SeoApiService {
  private readonly logger = new Logger(SeoApiService.name);
  private readonly apiKeys: Record<string, string> = {};
  private readonly apiRateLimits: Record<
    string,
    { limit: number; remaining: number; resetAt: Date }
  > = {};
  private readonly apiBaseUrls: Record<string, string> = {};
  private readonly fallbackOrder: string[] = ["moz", "semrush", "ahrefs"];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    // Initialize API keys from environment variables
    this.apiKeys.moz = this.configService.get<string>("MOZ_API_KEY", "");
    this.apiKeys.semrush = this.configService.get<string>(
      "SEMRUSH_API_KEY",
      ""
    );
    this.apiKeys.ahrefs = this.configService.get<string>("AHREFS_API_KEY", "");

    // Initialize API base URLs
    this.apiBaseUrls.moz = "https://moz.com/api/v1";
    this.apiBaseUrls.semrush = "https://api.semrush.com";
    this.apiBaseUrls.ahrefs = "https://api.ahrefs.com/v1";

    // Initialize rate limits
    this.fallbackOrder.forEach((api) => {
      this.apiRateLimits[api] = {
        limit: 10000,
        remaining: 10000,
        resetAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      };
    });
  }

  /**
   * Get SEO metrics for a domain
   */
  async getDomainMetrics(domain: string): Promise<SEOMetrics> {
    // Try each API in fallback order
    for (const api of this.fallbackOrder) {
      if (this.canUseApi(api)) {
        try {
          const metrics = await this.fetchMetricsFromApi(api, domain);
          return metrics;
        } catch (error) {
          this.logger.warn(
            `Failed to fetch metrics from ${api} API: ${error.message}`
          );
          continue; // Try next API
        }
      }
    }

    // If all APIs fail, return mock data
    this.logger.warn(
      "All SEO APIs failed or rate limited, returning mock data"
    );
    return this.getMockSeoMetrics(domain);
  }

  /**
   * Check if an API can be used (has key and not rate limited)
   */
  private canUseApi(api: string): boolean {
    // Check if API key exists
    if (!this.apiKeys[api]) {
      return false;
    }

    // Check rate limits
    const rateLimitInfo = this.apiRateLimits[api];
    if (rateLimitInfo.remaining <= 0) {
      // Check if reset time has passed
      if (new Date() > rateLimitInfo.resetAt) {
        // Reset rate limit
        rateLimitInfo.remaining = rateLimitInfo.limit;
        rateLimitInfo.resetAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
      } else {
        return false; // Still rate limited
      }
    }

    return true;
  }

  /**
   * Fetch metrics from a specific API
   */
  private async fetchMetricsFromApi(
    api: string,
    domain: string
  ): Promise<SEOMetrics> {
    switch (api) {
      case "moz":
        return this.fetchFromMoz(domain);
      case "semrush":
        return this.fetchFromSemrush(domain);
      case "ahrefs":
        return this.fetchFromAhrefs(domain);
      default:
        throw new Error(`Unknown API: ${api}`);
    }
  }

  /**
   * Fetch metrics from Moz API
   */
  private async fetchFromMoz(domain: string): Promise<SEOMetrics> {
    try {
      const url = `${this.apiBaseUrls.moz}/url-metrics/${encodeURIComponent(
        domain
      )}`;
      const response = await firstValueFrom(
        this.httpService
          .get(url, {
            headers: {
              Authorization: `Bearer ${this.apiKeys.moz}`,
            },
            params: {
              cols: "103079215104", // All metrics
            },
          })
          .pipe(
            catchError((error) => {
              this.updateRateLimits("moz", error.response?.headers);
              throw new HttpException(
                `Moz API error: ${
                  error.response?.data?.message || error.message
                }`,
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
              );
            })
          )
      );

      this.updateRateLimits("moz", response.headers);

      // Parse and transform Moz data to our standard format
      return this.transformMozData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching from Moz API: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetch metrics from SEMrush API
   */
  private async fetchFromSemrush(domain: string): Promise<SEOMetrics> {
    try {
      const url = `${this.apiBaseUrls.semrush}/analytics/api/v1`;
      const response = await firstValueFrom(
        this.httpService
          .get(url, {
            params: {
              key: this.apiKeys.semrush,
              type: "domain_ranks",
              domain: domain,
              export_columns: "Dn,Rk,Or,Ot,Oc,Ad,At,Ac",
              database: "us",
            },
          })
          .pipe(
            catchError((error) => {
              this.updateRateLimits("semrush", error.response?.headers);
              throw new HttpException(
                `SEMrush API error: ${
                  error.response?.data?.message || error.message
                }`,
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
              );
            })
          )
      );

      this.updateRateLimits("semrush", response.headers);

      // Parse and transform SEMrush data to our standard format
      return this.transformSemrushData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching from SEMrush API: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetch metrics from Ahrefs API
   */
  private async fetchFromAhrefs(domain: string): Promise<SEOMetrics> {
    try {
      const url = `${this.apiBaseUrls.ahrefs}/site-explorer/overview`;
      const response = await firstValueFrom(
        this.httpService
          .get(url, {
            params: {
              token: this.apiKeys.ahrefs,
              target: domain,
              output: "json",
            },
          })
          .pipe(
            catchError((error) => {
              this.updateRateLimits("ahrefs", error.response?.headers);
              throw new HttpException(
                `Ahrefs API error: ${
                  error.response?.data?.message || error.message
                }`,
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
              );
            })
          )
      );

      this.updateRateLimits("ahrefs", response.headers);

      // Parse and transform Ahrefs data to our standard format
      return this.transformAhrefsData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching from Ahrefs API: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update rate limits based on API response headers
   */
  private updateRateLimits(api: string, headers: any): void {
    if (!headers) return;

    const rateLimitInfo = this.apiRateLimits[api];

    // Helper function to safely parse header values
    const parseHeaderValue = (
      value: string | string[] | undefined | null
    ): string => {
      if (!value) return "0";
      if (Array.isArray(value)) return value[0] || "0";
      return value;
    };

    switch (api) {
      case "moz":
        if (headers["x-ratelimit-limit"]) {
          rateLimitInfo.limit = parseInt(
            parseHeaderValue(headers["x-ratelimit-limit"]),
            10
          );
        }
        if (headers["x-ratelimit-remaining"]) {
          rateLimitInfo.remaining = parseInt(
            parseHeaderValue(headers["x-ratelimit-remaining"]),
            10
          );
        }
        if (headers["x-ratelimit-reset"]) {
          rateLimitInfo.resetAt = new Date(
            parseInt(parseHeaderValue(headers["x-ratelimit-reset"]), 10) * 1000
          );
        }
        break;
      case "semrush":
        // SEMrush uses different headers
        if (headers["api-limits-limit"]) {
          rateLimitInfo.limit = parseInt(
            parseHeaderValue(headers["api-limits-limit"]),
            10
          );
        }
        if (headers["api-limits-remaining"]) {
          rateLimitInfo.remaining = parseInt(
            parseHeaderValue(headers["api-limits-remaining"]),
            10
          );
        }
        break;
      case "ahrefs":
        // Ahrefs uses different headers
        if (headers["x-limit-daily"]) {
          rateLimitInfo.limit = parseInt(
            parseHeaderValue(headers["x-limit-daily"]),
            10
          );
        }
        if (headers["x-limit-remaining"]) {
          rateLimitInfo.remaining = parseInt(
            parseHeaderValue(headers["x-limit-remaining"]),
            10
          );
        }
        break;
    }
  }

  /**
   * Transform Moz API data to our standard format
   */
  private transformMozData(data: Record<string, unknown>): SEOMetrics {
    return {
      domainAuthority: this.getNumberValue(data.domain_authority),
      pageAuthority: this.getNumberValue(data.page_authority),
      backlinks: this.getNumberValue(data.links),
      organicTraffic: 0, // Moz doesn't provide this directly
      trafficValue: 0, // Moz doesn't provide this directly
      keywordRankings: [],
      topPages: [],
      competitors: [],
    };
  }

  /**
   * Helper method to safely convert unknown values to numbers
   */
  private getNumberValue(value: unknown): number {
    if (typeof value === "number") return value;
    if (typeof value === "string") return parseFloat(value) || 0;
    return 0;
  }

  /**
   * Transform SEMrush API data to our standard format
   */
  private transformSemrushData(data: string): SEOMetrics {
    // Parse CSV data from SEMrush
    const lines = data.split("\n");
    if (lines.length < 2) {
      throw new Error("Invalid SEMrush data format");
    }

    // Skip header line and parse data line
    const dataLine = lines[1].split(";");

    return {
      domainAuthority: parseFloat(dataLine[1] || "0"), // SEMrush Rank as DA
      pageAuthority: 0, // SEMrush doesn't provide this directly
      backlinks: parseInt(dataLine[6] || "0", 10), // Ad backlinks as a proxy
      organicTraffic: parseInt(dataLine[2] || "0", 10),
      trafficValue: parseFloat(dataLine[3] || "0"),
      keywordRankings: [],
      topPages: [],
      competitors: [],
    };
  }

  /**
   * Transform Ahrefs API data to our standard format
   */
  private transformAhrefsData(data: Record<string, unknown>): SEOMetrics {
    return {
      domainAuthority: this.getNumberValue(data.domain_rating),
      pageAuthority: this.getNumberValue(data.url_rating),
      backlinks: this.getNumberValue(data.backlinks),
      organicTraffic: this.getNumberValue(data.organic_traffic),
      trafficValue: this.getNumberValue(data.traffic_value),
      keywordRankings: [],
      topPages: [],
      competitors: [],
    };
  }

  /**
   * Get mock SEO metrics when APIs are unavailable
   */
  private getMockSeoMetrics(domain: string): SEOMetrics {
    // Generate deterministic but random-looking values based on domain name
    const seed = this.hashString(domain);
    const random = (min: number, max: number) => {
      const x = Math.sin(seed) * 10000;
      const rand = x - Math.floor(x);
      return Math.floor(rand * (max - min + 1)) + min;
    };

    return {
      domainAuthority: random(1, 100),
      pageAuthority: random(1, 100),
      backlinks: random(10, 10000),
      organicTraffic: random(100, 100000),
      trafficValue: random(100, 10000) / 100,
      keywordRankings: Array.from({ length: 5 }, (_, i) => ({
        keyword: `keyword-${i + 1}`,
        position: random(1, 100),
        volume: random(10, 10000),
        difficulty: random(1, 100),
      })),
      topPages: Array.from({ length: 3 }, (_, i) => ({
        url: `https://${domain}/page-${i + 1}`,
        traffic: random(10, 1000),
        backlinks: random(0, 100),
      })),
      competitors: Array.from({ length: 3 }, (_, i) => ({
        domain: `competitor-${i + 1}.com`,
        commonKeywords: random(10, 1000),
        domainAuthority: random(1, 100),
      })),
    };
  }

  /**
   * Simple string hash function
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }
}
