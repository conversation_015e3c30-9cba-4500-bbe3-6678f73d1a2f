import { Injectable, Logger, Inject } from "@nestjs/common";
import * as cheerio from "cheerio";
import axios from "axios";
import * as natural from "natural";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import robotsParser from "robots-parser";

@Injectable()
export class WebScraperService {
  private readonly logger = new Logger(WebScraperService.name);
  private readonly tokenizer = new natural.WordTokenizer();
  private readonly tfidf = new natural.TfIdf();
  private readonly stemmer = natural.PorterStemmer;
  private readonly userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
  ];

  // Cache for robots.txt data to avoid repeated fetches
  private robotsCache: Map<string, { robots: any; timestamp: number }> =
    new Map();

  constructor(@Inject(HttpService) private readonly httpService: HttpService) {}

  /**
   * Scrape a website and extract metadata
   */
  async scrapeWebsite(domain: string): Promise<{
    title: string;
    description: string;
    keywords: string[];
    language: string;
    links: string[];
    emails: string[];
    contactPage: string | null;
    content: string;
    author: string;
    publishDate: string | null;
    lastModified: string | null;
    favicon: string | null;
    socialProfiles: string[];
    headings: { h1: string[]; h2: string[]; h3: string[] };
    images: { url: string; alt: string }[];
    structuredData: any[];
    metaTags: Record<string, string>;
    canonicalUrl: string | null;
    isAllowedByRobotsTxt: boolean;
  }> {
    try {
      const url = this.normalizeUrl(domain);
      this.logger.log(`Scraping website: ${url}`);

      // Check robots.txt before scraping
      const isAllowed = await this.isAllowedByRobotsTxt(url);
      if (!isAllowed) {
        this.logger.warn(`Scraping disallowed by robots.txt for ${url}`);
        return this.getEmptyResult(domain, false);
      }

      const html = await this.fetchHtml(url);
      if (!html) {
        throw new Error(`Failed to fetch HTML from ${url}`);
      }

      const $ = cheerio.load(html);

      // Extract basic metadata
      const title = $("title").text().trim() || domain;
      const description = $('meta[name="description"]').attr("content") || "";
      const metaKeywords = $('meta[name="keywords"]').attr("content") || "";
      const language = $("html").attr("lang") || "en";
      const author = $('meta[name="author"]').attr("content") || "";
      const publishDate =
        $('meta[property="article:published_time"]').attr("content") || null;
      const lastModified =
        $('meta[property="article:modified_time"]').attr("content") || null;
      const canonicalUrl = $('link[rel="canonical"]').attr("href") || null;

      // Extract favicon
      const favicon = this.extractFavicon($, url);

      // Extract all links
      const links: string[] = [];
      $("a[href]").each((_, element) => {
        const href = $(element).attr("href");
        if (href && !href.startsWith("#") && !href.startsWith("javascript:")) {
          try {
            const absoluteUrl = new URL(href, url).href;
            links.push(absoluteUrl);
          } catch (e) {
            // Invalid URL, skip
          }
        }
      });

      // Extract emails
      const emails = this.extractEmails(html);

      // Find contact page
      const contactPage = this.findContactPage(links, url);

      // Extract content for keyword analysis
      const content = this.extractContent($);

      // Extract keywords from content
      const extractedKeywords = this.extractKeywords(content, metaKeywords);

      // Extract social profiles
      const socialProfiles = this.extractSocialProfiles(links);

      // Extract headings
      const headings = this.extractHeadings($);

      // Extract images
      const images = this.extractImages($, url);

      // Extract structured data
      const structuredData = this.extractStructuredData($);

      // Extract meta tags
      const metaTags = this.extractMetaTags($);

      return {
        title,
        description,
        keywords: extractedKeywords,
        language,
        links: [...new Set(links)], // Remove duplicates
        emails,
        contactPage,
        content,
        author,
        publishDate,
        lastModified,
        favicon,
        socialProfiles,
        headings,
        images,
        structuredData,
        metaTags,
        canonicalUrl,
        isAllowedByRobotsTxt: true,
      };
    } catch (error) {
      this.logger.error(
        `Error scraping website ${domain}: ${error.message}`,
        error.stack
      );
      return this.getEmptyResult(domain, true);
    }
  }

  /**
   * Get empty result for a domain
   */
  private getEmptyResult(domain: string, isAllowed: boolean): any {
    return {
      title: domain,
      description: "",
      keywords: [],
      language: "en",
      links: [],
      emails: [],
      contactPage: null,
      content: "",
      author: "",
      publishDate: null,
      lastModified: null,
      favicon: null,
      socialProfiles: [],
      headings: { h1: [], h2: [], h3: [] },
      images: [],
      structuredData: [],
      metaTags: {},
      canonicalUrl: null,
      isAllowedByRobotsTxt: isAllowed,
    };
  }

  /**
   * Normalize URL by adding protocol if missing
   */
  normalizeUrl(domain: string): string {
    if (!domain.startsWith("http://") && !domain.startsWith("https://")) {
      return `https://${domain}`;
    }
    return domain;
  }

  /**
   * Fetch HTML content from a URL with retry logic
   */
  private async fetchHtml(url: string, retries = 2): Promise<string | null> {
    try {
      const userAgent =
        this.userAgents[Math.floor(Math.random() * this.userAgents.length)];

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            "User-Agent": userAgent,
            Accept: "text/html,application/xhtml+xml,application/xml",
            "Accept-Language": "en-US,en;q=0.9",
          },
          timeout: 10000,
          maxRedirects: 5,
        })
      );

      return response.data;
    } catch (error) {
      if (retries > 0) {
        this.logger.warn(`Retrying fetch for ${url}, ${retries} retries left`);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return this.fetchHtml(url, retries - 1);
      }

      this.logger.error(`Failed to fetch ${url}: ${error.message}`);
      return null;
    }
  }

  /**
   * Extract main content from HTML
   */
  private extractContent($: cheerio.Root): string {
    // Remove script and style elements
    $("script, style, iframe, noscript").remove();

    // Try to find main content areas
    const contentSelectors = [
      "main",
      "article",
      "#content",
      ".content",
      "#main",
      ".main",
      ".post",
      ".article",
    ];

    let content = "";

    // Try each content selector
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        content = element.text();
        break;
      }
    }

    // If no content found, use body
    if (!content) {
      content = $("body").text();
    }

    // Clean up the content
    return content.replace(/\s+/g, " ").trim();
  }

  /**
   * Extract keywords from content using TF-IDF
   */
  private extractKeywords(content: string, metaKeywords: string): string[] {
    // Start with any meta keywords
    const keywords = new Set<string>();
    if (metaKeywords) {
      metaKeywords.split(",").forEach((keyword) => {
        const trimmed = keyword.trim();
        if (trimmed.length > 2) {
          keywords.add(trimmed.toLowerCase());
        }
      });
    }

    // If no content, return meta keywords
    if (!content || content.length < 50) {
      return Array.from(keywords);
    }

    // Tokenize and stem the content
    const tokens = this.tokenizer.tokenize(content.toLowerCase());
    const stemmedTokens = tokens.map((token) => this.stemmer.stem(token));

    // Add document to TF-IDF
    this.tfidf.addDocument(stemmedTokens);

    // Get top terms
    const terms: { term: string; score: number }[] = [];
    this.tfidf.listTerms(0).forEach((item) => {
      // Only include terms that are at least 3 characters
      if (item.term.length >= 3) {
        terms.push({ term: item.term, score: item.tfidf });
      }
    });

    // Sort by score and take top 10
    terms.sort((a, b) => b.score - a.score);
    const topTerms = terms.slice(0, 10).map((item) => item.term);

    // Add top terms to keywords
    topTerms.forEach((term) => keywords.add(term));

    return Array.from(keywords).slice(0, 20); // Limit to 20 keywords
  }

  /**
   * Extract email addresses from HTML
   */
  private extractEmails(html: string): string[] {
    const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi;
    const matches = html.match(emailRegex) || [];
    return [...new Set(matches)]; // Remove duplicates
  }

  /**
   * Find contact page URL
   */
  private findContactPage(links: string[], baseUrl: string): string | null {
    const contactKeywords = [
      "contact",
      "kontakt",
      "contacto",
      "contato",
      "about",
      "get-in-touch",
      "reach-us",
    ];

    for (const link of links) {
      const lowercaseUrl = link.toLowerCase();

      // Check if URL contains contact keywords
      if (contactKeywords.some((keyword) => lowercaseUrl.includes(keyword))) {
        // Ensure it's from the same domain
        try {
          const linkDomain = new URL(link).hostname;
          const baseDomain = new URL(baseUrl).hostname;

          if (linkDomain === baseDomain) {
            return link;
          }
        } catch (e) {
          // Invalid URL, skip
        }
      }
    }

    return null;
  }

  /**
   * Extract social media profiles from links
   */
  private extractSocialProfiles(links: string[]): string[] {
    const socialDomains = [
      "facebook.com",
      "twitter.com",
      "linkedin.com",
      "instagram.com",
      "youtube.com",
      "pinterest.com",
      "github.com",
      "medium.com",
      "tiktok.com",
      "snapchat.com",
    ];

    const socialProfiles: string[] = [];

    for (const link of links) {
      try {
        const url = new URL(link);
        if (socialDomains.some((domain) => url.hostname.includes(domain))) {
          socialProfiles.push(link);
        }
      } catch (e) {
        // Invalid URL, skip
      }
    }

    return [...new Set(socialProfiles)]; // Remove duplicates
  }

  /**
   * Extract headings from HTML
   */
  private extractHeadings($: cheerio.Root): {
    h1: string[];
    h2: string[];
    h3: string[];
  } {
    const h1: string[] = [];
    const h2: string[] = [];
    const h3: string[] = [];

    $("h1").each((_, element) => {
      const text = $(element).text().trim();
      if (text) h1.push(text);
    });

    $("h2").each((_, element) => {
      const text = $(element).text().trim();
      if (text) h2.push(text);
    });

    $("h3").each((_, element) => {
      const text = $(element).text().trim();
      if (text) h3.push(text);
    });

    return { h1, h2, h3 };
  }

  /**
   * Extract images from HTML
   */
  private extractImages(
    $: cheerio.Root,
    baseUrl: string
  ): { url: string; alt: string }[] {
    const images: { url: string; alt: string }[] = [];

    $("img").each((_, element) => {
      const src = $(element).attr("src");
      const alt = $(element).attr("alt") || "";

      if (src) {
        try {
          const absoluteUrl = new URL(src, baseUrl).href;
          images.push({ url: absoluteUrl, alt });
        } catch (e) {
          // Invalid URL, skip
        }
      }
    });

    return images;
  }

  /**
   * Extract structured data from HTML
   */
  private extractStructuredData($: cheerio.Root): any[] {
    const structuredData: any[] = [];

    $('script[type="application/ld+json"]').each((_, element) => {
      try {
        const content = $(element).html();
        if (content) {
          const data = JSON.parse(content);
          structuredData.push(data);
        }
      } catch (e) {
        // Invalid JSON, skip
      }
    });

    return structuredData;
  }

  /**
   * Extract meta tags from HTML
   */
  private extractMetaTags($: cheerio.Root): Record<string, string> {
    const metaTags: Record<string, string> = {};

    $("meta").each((_, element) => {
      const name = $(element).attr("name") || $(element).attr("property");
      const content = $(element).attr("content");

      if (name && content) {
        metaTags[name] = content;
      }
    });

    return metaTags;
  }

  /**
   * Extract favicon from HTML
   */
  private extractFavicon($: cheerio.Root, baseUrl: string): string | null {
    // Try standard favicon locations
    const faviconSelectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="apple-touch-icon-precomposed"]',
    ];

    for (const selector of faviconSelectors) {
      const element = $(selector).first();
      const href = element.attr("href");

      if (href) {
        try {
          return new URL(href, baseUrl).href;
        } catch (e) {
          // Invalid URL, skip
        }
      }
    }

    // Try default favicon location
    try {
      const url = new URL("/favicon.ico", baseUrl);
      return url.href;
    } catch (e) {
      // Invalid URL
    }

    return null;
  }

  /**
   * Check if scraping is allowed by robots.txt
   */
  private async isAllowedByRobotsTxt(url: string): Promise<boolean> {
    try {
      const parsedUrl = new URL(url);
      const robotsUrl = `${parsedUrl.protocol}//${parsedUrl.hostname}/robots.txt`;

      // Check cache first
      const cacheEntry = this.robotsCache.get(parsedUrl.hostname);
      if (cacheEntry && Date.now() - cacheEntry.timestamp < 3600000) {
        // Cache for 1 hour
        return cacheEntry.robots.isAllowed(url, "Googlebot");
      }

      // Fetch robots.txt
      try {
        const response = await firstValueFrom(
          this.httpService.get(robotsUrl, {
            timeout: 5000,
            validateStatus: (status) => status < 500, // Accept 404 as valid response
          })
        );

        const robots = robotsParser(robotsUrl, response.data);

        // Cache the result
        this.robotsCache.set(parsedUrl.hostname, {
          robots,
          timestamp: Date.now(),
        });

        return robots.isAllowed(url, "Googlebot") || true;
      } catch (error) {
        // If robots.txt can't be fetched, assume scraping is allowed
        this.logger.warn(
          `Failed to fetch robots.txt for ${parsedUrl.hostname}: ${error.message}`
        );
        return true;
      }
    } catch (error) {
      this.logger.error(
        `Error checking robots.txt for ${url}: ${error.message}`
      );
      return true; // Default to allowing scraping on error
    }
  }
}
