import { Injectable, Logger } from "@nestjs/common";
import * as natural from "natural";

@Injectable()
export class ContentAnalyzerService {
  private readonly logger = new Logger(ContentAnalyzerService.name);
  private readonly tokenizer = new natural.WordTokenizer();
  private readonly stemmer = natural.PorterStemmer;
  private readonly tfidf = new natural.TfIdf();
  private readonly sentimentAnalyzer = new natural.SentimentAnalyzer(
    "English",
    natural.PorterStemmer,
    "afinn"
  );
  private readonly stopwords = new Set([
    "a",
    "about",
    "above",
    "after",
    "again",
    "against",
    "all",
    "am",
    "an",
    "and",
    "any",
    "are",
    "as",
    "at",
    "be",
    "because",
    "been",
    "before",
    "being",
    "below",
    "between",
    "both",
    "but",
    "by",
    "can",
    "did",
    "do",
    "does",
    "doing",
    "down",
    "during",
    "each",
    "few",
    "for",
    "from",
    "further",
    "had",
    "has",
    "have",
    "having",
    "he",
    "her",
    "here",
    "hers",
    "herself",
    "him",
    "himself",
    "his",
    "how",
    "i",
    "if",
    "in",
    "into",
    "is",
    "it",
    "its",
    "itself",
    "just",
    "me",
    "more",
    "most",
    "my",
    "myself",
    "no",
    "nor",
    "not",
    "now",
    "of",
    "off",
    "on",
    "once",
    "only",
    "or",
    "other",
    "our",
    "ours",
    "ourselves",
    "out",
    "over",
    "own",
    "s",
    "same",
    "she",
    "should",
    "so",
    "some",
    "such",
    "t",
    "than",
    "that",
    "the",
    "their",
    "theirs",
    "them",
    "themselves",
    "then",
    "there",
    "these",
    "they",
    "this",
    "those",
    "through",
    "to",
    "too",
    "under",
    "until",
    "up",
    "very",
    "was",
    "we",
    "were",
    "what",
    "when",
    "where",
    "which",
    "while",
    "who",
    "whom",
    "why",
    "will",
    "with",
    "you",
    "your",
    "yours",
    "yourself",
    "yourselves",
  ]);

  // Industry categories and their related keywords
  private readonly industryKeywords = {
    Technology: [
      "software",
      "technology",
      "tech",
      "digital",
      "app",
      "development",
      "programming",
      "code",
      "data",
      "cloud",
      "ai",
      "artificial intelligence",
      "machine learning",
      "ml",
      "saas",
      "platform",
    ],
    Marketing: [
      "marketing",
      "advertising",
      "branding",
      "seo",
      "content",
      "social media",
      "campaign",
      "audience",
      "strategy",
      "promotion",
      "analytics",
    ],
    Finance: [
      "finance",
      "financial",
      "investment",
      "banking",
      "money",
      "wealth",
      "trading",
      "stocks",
      "crypto",
      "cryptocurrency",
      "insurance",
      "loan",
      "mortgage",
    ],
    Healthcare: [
      "health",
      "healthcare",
      "medical",
      "wellness",
      "fitness",
      "doctor",
      "patient",
      "hospital",
      "clinic",
      "therapy",
      "treatment",
      "medicine",
    ],
    Education: [
      "education",
      "learning",
      "teaching",
      "school",
      "university",
      "college",
      "course",
      "training",
      "student",
      "academic",
      "e-learning",
    ],
    Entertainment: [
      "entertainment",
      "media",
      "film",
      "movie",
      "music",
      "game",
      "gaming",
      "streaming",
      "video",
      "tv",
      "television",
      "podcast",
    ],
    Travel: [
      "travel",
      "tourism",
      "vacation",
      "destination",
      "hotel",
      "flight",
      "booking",
      "tour",
      "adventure",
      "experience",
    ],
    Food: [
      "food",
      "restaurant",
      "recipe",
      "cooking",
      "cuisine",
      "chef",
      "meal",
      "dining",
      "catering",
      "beverage",
    ],
    Fashion: [
      "fashion",
      "clothing",
      "style",
      "apparel",
      "design",
      "trend",
      "collection",
      "accessory",
      "beauty",
      "luxury",
    ],
    "Real Estate": [
      "real estate",
      "property",
      "home",
      "house",
      "apartment",
      "rent",
      "buy",
      "sell",
      "mortgage",
      "commercial",
      "residential",
    ],
    Business: [
      "business",
      "company",
      "corporate",
      "enterprise",
      "startup",
      "entrepreneur",
      "management",
      "consulting",
      "service",
      "solution",
    ],
    Legal: [
      "legal",
      "law",
      "attorney",
      "lawyer",
      "firm",
      "practice",
      "litigation",
      "counsel",
      "compliance",
      "regulation",
    ],
  };

  /**
   * Determine the industry of a website based on its content and keywords
   */
  determineIndustry(content: string, keywords: string[]): string {
    try {
      // Combine content and keywords for analysis
      const allText = content + " " + keywords.join(" ");

      // Tokenize and normalize the text
      const tokens = this.tokenizer.tokenize(allText.toLowerCase());

      // Count industry keyword matches
      const industryCounts: Record<string, number> = {};

      // Initialize counts for all industries
      Object.keys(this.industryKeywords).forEach((industry) => {
        industryCounts[industry] = 0;
      });

      // Count matches for each industry
      tokens.forEach((token) => {
        Object.entries(this.industryKeywords).forEach(
          ([industry, keywords]) => {
            if (
              keywords.some(
                (keyword) =>
                  keyword === token ||
                  (keyword.includes(" ") &&
                    allText.toLowerCase().includes(keyword))
              )
            ) {
              industryCounts[industry]++;
            }
          }
        );
      });

      // Find the industry with the highest count
      let maxCount = 0;
      let detectedIndustry = "Unknown";

      Object.entries(industryCounts).forEach(([industry, count]) => {
        if (count > maxCount) {
          maxCount = count;
          detectedIndustry = industry;
        }
      });

      // If no clear industry is detected, use 'Unknown'
      return maxCount > 0 ? detectedIndustry : "Unknown";
    } catch (error) {
      this.logger.error(
        `Error determining industry: ${error.message}`,
        error.stack
      );
      return "Unknown";
    }
  }

  /**
   * Calculate relevance score between two websites
   */
  calculateRelevanceScore(
    sourceContent: string,
    sourceKeywords: string[],
    sourceIndustry: string,
    targetContent: string,
    targetKeywords: string[],
    targetIndustry: string
  ): number {
    try {
      // 1. Calculate keyword similarity (30% of score)
      const keywordSimilarity = this.calculateKeywordSimilarity(
        sourceKeywords,
        targetKeywords
      );

      // 2. Calculate content similarity (30% of score)
      const contentSimilarity = this.calculateContentSimilarity(
        sourceContent,
        targetContent
      );

      // 3. Calculate industry match (20% of score)
      const industryMatch = this.calculateIndustryMatch(
        sourceIndustry,
        targetIndustry
      );

      // 4. Calculate sentiment match (10% of score)
      const sentimentMatch = this.calculateSentimentMatch(
        sourceContent,
        targetContent
      );

      // 5. Calculate topic similarity (10% of score)
      const topicSimilarity = this.calculateTopicSimilarity(
        sourceContent,
        targetContent
      );

      // Calculate weighted score
      const weightedScore =
        keywordSimilarity * 0.3 +
        contentSimilarity * 0.3 +
        industryMatch * 0.2 +
        sentimentMatch * 0.1 +
        topicSimilarity * 0.1;

      // Return score on a scale of 0-100
      return Math.round(weightedScore * 100);
    } catch (error) {
      this.logger.error(
        `Error calculating relevance score: ${error.message}`,
        error.stack
      );
      return 50; // Default to neutral score
    }
  }

  /**
   * Calculate similarity between two sets of keywords
   */
  private calculateKeywordSimilarity(
    sourceKeywords: string[],
    targetKeywords: string[]
  ): number {
    if (sourceKeywords.length === 0 || targetKeywords.length === 0) {
      return 0;
    }

    // Normalize keywords
    const normalizedSourceKeywords = sourceKeywords.map((k) => k.toLowerCase());
    const normalizedTargetKeywords = targetKeywords.map((k) => k.toLowerCase());

    // Count exact matches
    let exactMatches = 0;
    normalizedSourceKeywords.forEach((sourceKeyword) => {
      if (normalizedTargetKeywords.includes(sourceKeyword)) {
        exactMatches++;
      }
    });

    // Count partial matches (one keyword is contained within another)
    let partialMatches = 0;
    normalizedSourceKeywords.forEach((sourceKeyword) => {
      normalizedTargetKeywords.forEach((targetKeyword) => {
        if (
          sourceKeyword !== targetKeyword &&
          (sourceKeyword.includes(targetKeyword) ||
            targetKeyword.includes(sourceKeyword))
        ) {
          partialMatches++;
        }
      });
    });

    // Calculate similarity score
    const exactMatchScore =
      exactMatches /
      Math.min(
        normalizedSourceKeywords.length,
        normalizedTargetKeywords.length
      );
    const partialMatchScore =
      partialMatches /
      (normalizedSourceKeywords.length * normalizedTargetKeywords.length);

    // Weight exact matches more heavily
    return exactMatchScore * 0.7 + partialMatchScore * 0.3;
  }

  /**
   * Calculate similarity between two text contents
   */
  private calculateContentSimilarity(
    sourceContent: string,
    targetContent: string
  ): number {
    if (!sourceContent || !targetContent) {
      return 0;
    }

    // Tokenize and stem the content
    const sourceTokens = this.tokenizer.tokenize(sourceContent.toLowerCase());
    const targetTokens = this.tokenizer.tokenize(targetContent.toLowerCase());

    // If either content has too few tokens, return low similarity
    if (sourceTokens.length < 10 || targetTokens.length < 10) {
      return 0.1;
    }

    // Use TF-IDF to find important terms in source content
    this.tfidf.addDocument(sourceTokens);

    // Get top terms from source
    const sourceTerms = this.tfidf
      .listTerms(0)
      .filter((item) => item.term.length > 3) // Filter out short terms
      .slice(0, 20) // Take top 20 terms
      .map((item) => item.term);

    // Count how many source terms appear in target content
    let matchCount = 0;
    sourceTerms.forEach((term) => {
      if (targetTokens.includes(term)) {
        matchCount++;
      }
    });

    // Calculate similarity score
    return matchCount / sourceTerms.length;
  }

  /**
   * Calculate match score between two industries
   */
  private calculateIndustryMatch(
    sourceIndustry: string,
    targetIndustry: string
  ): number {
    // Define industry relationships (which industries are related)
    const relatedIndustries: Record<string, string[]> = {
      Technology: ["Business", "Education", "Entertainment"],
      Marketing: ["Business", "Technology", "Entertainment"],
      Finance: ["Business", "Real Estate", "Legal"],
      Healthcare: ["Education", "Business"],
      Education: ["Technology", "Healthcare"],
      Entertainment: ["Marketing", "Technology"],
      Travel: ["Entertainment", "Food"],
      Food: ["Travel", "Healthcare"],
      Fashion: ["Entertainment", "Business"],
      "Real Estate": ["Business", "Finance", "Legal"],
      Business: ["Technology", "Finance", "Marketing", "Legal"],
      Legal: ["Business", "Finance", "Real Estate"],
    };

    // Exact match
    if (sourceIndustry === targetIndustry) {
      return 1.0;
    }

    // Unknown industry
    if (sourceIndustry === "Unknown" || targetIndustry === "Unknown") {
      return 0.5;
    }

    // Related industry match
    if (
      relatedIndustries[sourceIndustry] &&
      relatedIndustries[sourceIndustry].includes(targetIndustry)
    ) {
      return 0.7;
    }

    // Secondary relationship (2 degrees of separation)
    for (const relatedIndustry of relatedIndustries[sourceIndustry] || []) {
      if (
        relatedIndustries[relatedIndustry] &&
        relatedIndustries[relatedIndustry].includes(targetIndustry)
      ) {
        return 0.4;
      }
    }

    // No relationship
    return 0.2;
  }

  /**
   * Calculate sentiment match between two texts
   */
  private calculateSentimentMatch(
    sourceContent: string,
    targetContent: string
  ): number {
    try {
      if (!sourceContent || !targetContent) {
        return 0.5; // Neutral match if either content is missing
      }

      // Get sentiment scores (-5 to 5 range typically)
      const sourceTokens = this.tokenizer.tokenize(sourceContent.toLowerCase());
      const targetTokens = this.tokenizer.tokenize(targetContent.toLowerCase());

      const sourceSentiment = this.sentimentAnalyzer.getSentiment(sourceTokens);
      const targetSentiment = this.sentimentAnalyzer.getSentiment(targetTokens);

      // Normalize to 0-1 range
      const normalizedSourceSentiment = (sourceSentiment + 5) / 10;
      const normalizedTargetSentiment = (targetSentiment + 5) / 10;

      // Calculate similarity (1 - absolute difference)
      const sentimentDifference = Math.abs(
        normalizedSourceSentiment - normalizedTargetSentiment
      );
      return 1 - sentimentDifference;
    } catch (error) {
      this.logger.error(
        `Error calculating sentiment match: ${error.message}`,
        error.stack
      );
      return 0.5; // Default to neutral match
    }
  }

  /**
   * Calculate topic similarity between two texts using LDA-like approach
   */
  private calculateTopicSimilarity(
    sourceContent: string,
    targetContent: string
  ): number {
    try {
      if (!sourceContent || !targetContent) {
        return 0.1;
      }

      // Extract topics (simplified LDA-like approach)
      const sourceTopics = this.extractTopics(sourceContent);
      const targetTopics = this.extractTopics(targetContent);

      // Count matching topics
      let matchCount = 0;
      for (const sourceTopic of sourceTopics) {
        if (
          targetTopics.some(
            (targetTopic) =>
              this.calculateWordSimilarity(sourceTopic, targetTopic) > 0.7
          )
        ) {
          matchCount++;
        }
      }

      // Calculate similarity score
      return matchCount / Math.max(sourceTopics.length, 1);
    } catch (error) {
      this.logger.error(
        `Error calculating topic similarity: ${error.message}`,
        error.stack
      );
      return 0.1;
    }
  }

  /**
   * Extract topics from content (simplified approach)
   */
  private extractTopics(content: string): string[] {
    // Tokenize and remove stopwords
    const tokens = this.tokenizer
      .tokenize(content.toLowerCase())
      .filter((token) => !this.stopwords.has(token) && token.length > 3);

    // Count token frequencies
    const tokenCounts: Record<string, number> = {};
    tokens.forEach((token) => {
      tokenCounts[token] = (tokenCounts[token] || 0) + 1;
    });

    // Sort by frequency
    const sortedTokens = Object.entries(tokenCounts)
      .sort((a, b) => b[1] - a[1])
      .map((entry) => entry[0]);

    // Return top tokens as topics
    return sortedTokens.slice(0, 10);
  }

  /**
   * Calculate similarity between two words
   */
  private calculateWordSimilarity(word1: string, word2: string): number {
    if (word1 === word2) return 1.0;

    // Stem the words
    const stemmed1 = this.stemmer.stem(word1);
    const stemmed2 = this.stemmer.stem(word2);

    if (stemmed1 === stemmed2) return 0.9;

    // Check if one contains the other
    if (word1.includes(word2) || word2.includes(word1)) return 0.8;
    if (stemmed1.includes(stemmed2) || stemmed2.includes(stemmed1)) return 0.7;

    // Calculate Levenshtein distance
    const distance = natural.LevenshteinDistance(word1, word2);
    const maxLength = Math.max(word1.length, word2.length);

    // Normalize distance to similarity score
    return Math.max(0, 1 - distance / maxLength);
  }
}
