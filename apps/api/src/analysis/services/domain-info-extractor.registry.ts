import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import {
  DomainInfoExtractor,
  DomainInfoExtractorRegistry,
  DomainDataType,
  ValidationResult,
  ExtractorSelectionStrategy,
} from "../interfaces/domain-info-extractor.interface";

@Injectable()
export class DomainInfoExtractorRegistryService implements DomainInfoExtractorRegistry, OnModuleInit {
  private readonly logger = new Logger(DomainInfoExtractorRegistryService.name);
  private readonly extractors = new Map<string, DomainInfoExtractor>();
  private readonly dataTypeIndex = new Map<DomainDataType, DomainInfoExtractor[]>();
  private roundRobinCounters = new Map<DomainDataType, number>();

  async onModuleInit() {
    this.logger.log("Initializing Domain Info Extractor Registry");
    await this.validateAll();
  }

  /**
   * Register a new extractor
   */
  register(extractor: DomainInfoExtractor): void {
    this.logger.log(`Registering extractor: ${extractor.name}`);
    
    // Store extractor
    this.extractors.set(extractor.name, extractor);
    
    // Update data type index
    for (const dataType of extractor.supportedDataTypes) {
      if (!this.dataTypeIndex.has(dataType)) {
        this.dataTypeIndex.set(dataType, []);
      }
      
      const extractors = this.dataTypeIndex.get(dataType)!;
      
      // Remove existing entry if present
      const existingIndex = extractors.findIndex(e => e.name === extractor.name);
      if (existingIndex >= 0) {
        extractors.splice(existingIndex, 1);
      }
      
      // Insert in priority order (highest first)
      const insertIndex = extractors.findIndex(e => e.priority < extractor.priority);
      if (insertIndex >= 0) {
        extractors.splice(insertIndex, 0, extractor);
      } else {
        extractors.push(extractor);
      }
    }

    this.logger.log(
      `Extractor ${extractor.name} registered with priority ${extractor.priority} ` +
      `for data types: ${extractor.supportedDataTypes.join(", ")}`
    );
  }

  /**
   * Unregister an extractor
   */
  unregister(extractorName: string): void {
    this.logger.log(`Unregistering extractor: ${extractorName}`);
    
    const extractor = this.extractors.get(extractorName);
    if (!extractor) {
      this.logger.warn(`Extractor ${extractorName} not found for unregistration`);
      return;
    }

    // Remove from main registry
    this.extractors.delete(extractorName);

    // Remove from data type index
    for (const dataType of extractor.supportedDataTypes) {
      const extractors = this.dataTypeIndex.get(dataType);
      if (extractors) {
        const index = extractors.findIndex(e => e.name === extractorName);
        if (index >= 0) {
          extractors.splice(index, 1);
        }
        
        // Clean up empty arrays
        if (extractors.length === 0) {
          this.dataTypeIndex.delete(dataType);
        }
      }
    }

    this.logger.log(`Extractor ${extractorName} unregistered successfully`);
  }

  /**
   * Get all registered extractors
   */
  getAll(): DomainInfoExtractor[] {
    return Array.from(this.extractors.values());
  }

  /**
   * Get extractors that can handle a specific data type
   */
  getByDataType(dataType: DomainDataType): DomainInfoExtractor[] {
    return this.dataTypeIndex.get(dataType) || [];
  }

  /**
   * Get the best extractor for a specific domain and data type
   */
  getBest(
    domain: string, 
    dataType: DomainDataType, 
    strategy: ExtractorSelectionStrategy = ExtractorSelectionStrategy.PRIORITY
  ): DomainInfoExtractor | null {
    const candidates = this.getByDataType(dataType).filter(
      extractor => extractor.enabled && extractor.canHandle(domain, dataType)
    );

    if (candidates.length === 0) {
      return null;
    }

    switch (strategy) {
      case ExtractorSelectionStrategy.PRIORITY:
        return candidates[0]; // Already sorted by priority

      case ExtractorSelectionStrategy.COST:
        return candidates.reduce((best, current) => {
          const bestCost = best.getCost(dataType);
          const currentCost = current.getCost(dataType);
          return currentCost.apiCalls < bestCost.apiCalls ? current : best;
        });

      case ExtractorSelectionStrategy.SPEED:
        return candidates.reduce((best, current) => {
          const bestCost = best.getCost(dataType);
          const currentCost = current.getCost(dataType);
          return currentCost.estimatedDelay < bestCost.estimatedDelay ? current : best;
        });

      case ExtractorSelectionStrategy.RELIABILITY:
        // For now, use priority as a proxy for reliability
        return candidates[0];

      case ExtractorSelectionStrategy.ROUND_ROBIN:
        const counter = this.roundRobinCounters.get(dataType) || 0;
        const selected = candidates[counter % candidates.length];
        this.roundRobinCounters.set(dataType, counter + 1);
        return selected;

      default:
        return candidates[0];
    }
  }

  /**
   * Get extractor by name
   */
  getByName(name: string): DomainInfoExtractor | null {
    return this.extractors.get(name) || null;
  }

  /**
   * Validate all registered extractors
   */
  async validateAll(): Promise<Record<string, ValidationResult>> {
    const results: Record<string, ValidationResult> = {};
    
    this.logger.log(`Validating ${this.extractors.size} registered extractors`);

    for (const [name, extractor] of this.extractors) {
      try {
        this.logger.debug(`Validating extractor: ${name}`);
        const result = await extractor.validateConfiguration();
        results[name] = result;
        
        if (!result.valid) {
          this.logger.warn(
            `Extractor ${name} validation failed: ${result.errors.join(", ")}`
          );
        } else {
          this.logger.debug(`Extractor ${name} validation passed`);
        }
      } catch (error) {
        this.logger.error(`Error validating extractor ${name}: ${error.message}`);
        results[name] = {
          valid: false,
          errors: [`Validation error: ${error.message}`],
          warnings: [],
          capabilities: [],
        };
      }
    }

    const validCount = Object.values(results).filter(r => r.valid).length;
    this.logger.log(`Validation complete: ${validCount}/${this.extractors.size} extractors valid`);

    return results;
  }

  /**
   * Get registry statistics
   */
  getStatistics(): {
    totalExtractors: number;
    enabledExtractors: number;
    dataTypeCoverage: Record<DomainDataType, number>;
    extractorsByPriority: Array<{ name: string; priority: number; enabled: boolean }>;
  } {
    const extractors = this.getAll();
    const enabledExtractors = extractors.filter(e => e.enabled);
    
    const dataTypeCoverage: Record<DomainDataType, number> = {} as any;
    for (const dataType of Object.values(DomainDataType)) {
      dataTypeCoverage[dataType] = this.getByDataType(dataType).filter(e => e.enabled).length;
    }

    const extractorsByPriority = extractors
      .map(e => ({ name: e.name, priority: e.priority, enabled: e.enabled }))
      .sort((a, b) => b.priority - a.priority);

    return {
      totalExtractors: extractors.length,
      enabledExtractors: enabledExtractors.length,
      dataTypeCoverage,
      extractorsByPriority,
    };
  }

  /**
   * Get extractors with fallback chain for a data type
   */
  getWithFallback(domain: string, dataType: DomainDataType): DomainInfoExtractor[] {
    return this.getByDataType(dataType).filter(
      extractor => extractor.enabled && extractor.canHandle(domain, dataType)
    );
  }

  /**
   * Check if a data type is supported
   */
  isDataTypeSupported(dataType: DomainDataType): boolean {
    return this.getByDataType(dataType).some(e => e.enabled);
  }

  /**
   * Get coverage report for all data types
   */
  getCoverageReport(): Record<DomainDataType, {
    supported: boolean;
    extractorCount: number;
    extractors: string[];
  }> {
    const report: any = {};
    
    for (const dataType of Object.values(DomainDataType)) {
      const extractors = this.getByDataType(dataType).filter(e => e.enabled);
      report[dataType] = {
        supported: extractors.length > 0,
        extractorCount: extractors.length,
        extractors: extractors.map(e => e.name),
      };
    }

    return report;
  }

  /**
   * Reset round-robin counters
   */
  resetRoundRobinCounters(): void {
    this.roundRobinCounters.clear();
    this.logger.debug("Round-robin counters reset");
  }

  /**
   * Get health status of all extractors
   */
  async getHealthStatus(): Promise<Record<string, {
    name: string;
    enabled: boolean;
    healthy: boolean;
    lastValidation?: Date;
    errors: string[];
  }>> {
    const status: any = {};
    const validationResults = await this.validateAll();

    for (const [name, extractor] of this.extractors) {
      const validation = validationResults[name];
      status[name] = {
        name: extractor.name,
        enabled: extractor.enabled,
        healthy: validation?.valid || false,
        lastValidation: new Date(),
        errors: validation?.errors || [],
      };
    }

    return status;
  }
}
