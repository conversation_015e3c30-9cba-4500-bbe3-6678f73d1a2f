import { Injectable, Logger, Inject } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { WebScraperService } from "./web-scraper.service";
import { firstValueFrom } from "rxjs";
import * as cheerio from "cheerio";

interface ContactInfo {
  emails: string[];
  contactPage: string | null;
  socialProfiles: string[];
  phoneNumbers: string[];
  contactName: string | null;
  contactPosition: string | null;
  contactForm: string | null;
  address: string | null;
  chatSupport: boolean;
  lastChecked: Date;
  confidence?: number;
  extractionMethod?: "direct" | "ml_fallback" | "pattern_matching";
}

interface MLContactPrediction {
  emails: Array<{
    email: string;
    confidence: number;
    context: string;
  }>;
  phones: Array<{
    phone: string;
    confidence: number;
    context: string;
  }>;
  names: Array<{
    name: string;
    position?: string;
    confidence: number;
    context: string;
  }>;
}

@Injectable()
export class ContactExtractorService {
  private readonly logger = new Logger(ContactExtractorService.name);
  private readonly requestDelay = 1500; // 1.5 second delay between requests
  private readonly maxPagesToVisit = 5; // Maximum number of pages to visit per domain
  private readonly contactCache = new Map<
    string,
    { data: ContactInfo; timestamp: number }
  >();
  private readonly cacheTTL = 24 * 60 * 60 * 1000; // 24 hours

  // Enhanced email patterns for better detection
  private readonly enhancedEmailPatterns = [
    // Standard email pattern
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    // Email with spaces (sometimes obfuscated)
    /\b[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}\b/g,
    // Email with [at] or (at) replacement
    /\b[A-Za-z0-9._%+-]+\s*(?:\[at\]|\(at\)|@)\s*[A-Za-z0-9.-]+\s*(?:\[dot\]|\(dot\)|\.)?\s*[A-Z|a-z]{2,}\b/gi,
    // Email in mailto links
    /mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/gi,
  ];

  // Common contact role patterns for ML-based extraction
  private readonly contactRolePatterns = [
    /(?:contact|reach|email|write)\s+(?:us|me)\s+(?:at|@)\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/gi,
    /(?:ceo|founder|director|manager|lead|head)\s*:?\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/gi,
    /([A-Za-z\s]+)\s*[-–—]\s*(?:ceo|founder|director|manager|lead|head)/gi,
  ];

  constructor(
    @Inject(HttpService)
    private readonly httpService: HttpService,
    @Inject(WebScraperService)
    private readonly webScraperService: WebScraperService
  ) {}

  /**
   * Extract contact information from a domain
   */
  async extractContactInfo(domain: string): Promise<ContactInfo> {
    try {
      this.logger.log(`Extracting contact information for domain: ${domain}`);

      // Check cache first
      const normalizedDomain = domain
        .toLowerCase()
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "");
      const cacheEntry = this.contactCache.get(normalizedDomain);

      if (cacheEntry && Date.now() - cacheEntry.timestamp < this.cacheTTL) {
        this.logger.log(`Using cached contact info for ${domain}`);
        return cacheEntry.data;
      }

      // First, scrape the main page
      const mainPageData = await this.webScraperService.scrapeWebsite(domain);

      // Initialize results with data from main page
      const result: ContactInfo = {
        emails: [...mainPageData.emails],
        contactPage: mainPageData.contactPage,
        socialProfiles: this.extractSocialProfiles(mainPageData.links),
        phoneNumbers: this.extractPhoneNumbers(mainPageData.content),
        contactName: this.extractContactName(
          mainPageData.content,
          mainPageData.structuredData
        ),
        contactPosition: this.extractContactPosition(
          mainPageData.content,
          mainPageData.structuredData
        ),
        contactForm: this.findContactForm(mainPageData.links, domain),
        address: this.extractAddress(
          mainPageData.content,
          mainPageData.structuredData
        ),
        chatSupport: this.detectChatSupport(mainPageData.content),
        lastChecked: new Date(),
      };

      // If we found a contact page, scrape it too
      if (result.contactPage) {
        await this.delay(this.requestDelay);
        const contactPageData = await this.webScraperService.scrapeWebsite(
          result.contactPage
        );

        // Add unique emails from contact page
        contactPageData.emails.forEach((email) => {
          if (!result.emails.includes(email)) {
            result.emails.push(email);
          }
        });

        // Add unique social profiles from contact page
        const contactPageSocialProfiles = this.extractSocialProfiles(
          contactPageData.links
        );
        contactPageSocialProfiles.forEach((profile) => {
          if (!result.socialProfiles.includes(profile)) {
            result.socialProfiles.push(profile);
          }
        });

        // Add unique phone numbers from contact page
        const contactPagePhoneNumbers = this.extractPhoneNumbers(
          contactPageData.content
        );
        contactPagePhoneNumbers.forEach((phone) => {
          if (!result.phoneNumbers.includes(phone)) {
            result.phoneNumbers.push(phone);
          }
        });

        // Update other contact information if not already found
        if (!result.contactName) {
          result.contactName = this.extractContactName(
            contactPageData.content,
            contactPageData.structuredData
          );
        }

        if (!result.contactPosition) {
          result.contactPosition = this.extractContactPosition(
            contactPageData.content,
            contactPageData.structuredData
          );
        }

        if (!result.contactForm) {
          result.contactForm = this.detectContactForm(contactPageData)
            ? result.contactPage
            : null;
        }

        if (!result.address) {
          result.address = this.extractAddress(
            contactPageData.content,
            contactPageData.structuredData
          );
        }

        if (!result.chatSupport) {
          result.chatSupport = this.detectChatSupport(contactPageData.content);
        }
      }

      // If we still don't have enough contact info, try to find and scrape an about page
      if (
        (result.emails.length === 0 || !result.contactName) &&
        mainPageData.links.length > 0
      ) {
        const aboutPageUrl = this.findAboutPage(mainPageData.links, domain);

        if (aboutPageUrl) {
          await this.delay(this.requestDelay);
          const aboutPageData = await this.webScraperService.scrapeWebsite(
            aboutPageUrl
          );

          // Add unique emails from about page
          aboutPageData.emails.forEach((email) => {
            if (!result.emails.includes(email)) {
              result.emails.push(email);
            }
          });

          // Add unique social profiles from about page
          const aboutPageSocialProfiles = this.extractSocialProfiles(
            aboutPageData.links
          );
          aboutPageSocialProfiles.forEach((profile) => {
            if (!result.socialProfiles.includes(profile)) {
              result.socialProfiles.push(profile);
            }
          });

          // Add unique phone numbers from about page
          const aboutPagePhoneNumbers = this.extractPhoneNumbers(
            aboutPageData.content
          );
          aboutPagePhoneNumbers.forEach((phone) => {
            if (!result.phoneNumbers.includes(phone)) {
              result.phoneNumbers.push(phone);
            }
          });

          // Update other contact information if not already found
          if (!result.contactName) {
            result.contactName = this.extractContactName(
              aboutPageData.content,
              aboutPageData.structuredData
            );
          }

          if (!result.contactPosition) {
            result.contactPosition = this.extractContactPosition(
              aboutPageData.content,
              aboutPageData.structuredData
            );
          }

          if (!result.address && !result.contactPage) {
            result.address = this.extractAddress(
              aboutPageData.content,
              aboutPageData.structuredData
            );
          }
        }
      }

      // If still no contact info found, try ML-based fallback
      if (result.emails.length === 0 && !result.contactName) {
        this.logger.debug(`Attempting ML-based fallback for ${domain}`);

        try {
          const mlPrediction = await this.mlBasedContactExtraction(domain);

          // Add high-confidence emails from ML prediction
          mlPrediction.emails
            .filter((emailPred) => emailPred.confidence > 0.7)
            .forEach((emailPred) => {
              if (!result.emails.includes(emailPred.email)) {
                result.emails.push(emailPred.email);
              }
            });

          // Add high-confidence phones from ML prediction
          mlPrediction.phones
            .filter((phonePred) => phonePred.confidence > 0.7)
            .forEach((phonePred) => {
              if (!result.phoneNumbers.includes(phonePred.phone)) {
                result.phoneNumbers.push(phonePred.phone);
              }
            });

          // Add high-confidence names from ML prediction
          if (!result.contactName) {
            const highConfidenceName = mlPrediction.names.find(
              (namePred) => namePred.confidence > 0.8
            );
            if (highConfidenceName) {
              result.contactName = highConfidenceName.name;
              result.contactPosition = highConfidenceName.position || null;
            }
          }

          // Set extraction method if ML found anything
          if (
            result.emails.length > 0 ||
            result.phoneNumbers.length > 0 ||
            result.contactName
          ) {
            result.extractionMethod = "ml_fallback";
          }
        } catch (error) {
          this.logger.warn(
            `ML fallback failed for ${domain}: ${error.message}`
          );
        }
      }

      // Set confidence and extraction method
      result.confidence = this.calculateOverallConfidence(
        result,
        result.extractionMethod || "direct"
      );

      // Cache the result
      this.contactCache.set(normalizedDomain, {
        data: result,
        timestamp: Date.now(),
      });

      this.logger.log(
        `Contact extraction completed for ${domain}: ` +
          `${result.emails.length} emails, ${result.phoneNumbers.length} phones, ` +
          `method: ${
            result.extractionMethod
          }, confidence: ${result.confidence?.toFixed(2)}`
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error extracting contact info for ${domain}: ${error.message}`,
        error.stack
      );
      return {
        emails: [],
        contactPage: null,
        socialProfiles: [],
        phoneNumbers: [],
        contactName: null,
        contactPosition: null,
        contactForm: null,
        address: null,
        chatSupport: false,
        lastChecked: new Date(),
      };
    }
  }

  /**
   * Extract social media profiles from links
   */
  private extractSocialProfiles(links: string[]): string[] {
    const socialDomains = [
      "facebook.com",
      "twitter.com",
      "linkedin.com",
      "instagram.com",
      "youtube.com",
      "pinterest.com",
      "github.com",
      "medium.com",
      "tiktok.com",
    ];

    const socialProfiles: string[] = [];

    links.forEach((link) => {
      try {
        const url = new URL(link);
        const hostname = url.hostname.replace("www.", "");

        if (socialDomains.some((domain) => hostname.includes(domain))) {
          socialProfiles.push(link);
        }
      } catch (e) {
        // Invalid URL, skip
      }
    });

    return [...new Set(socialProfiles)]; // Remove duplicates
  }

  /**
   * Extract phone numbers from content
   */
  private extractPhoneNumbers(content: string): string[] {
    // Multiple phone number patterns
    const phonePatterns = [
      // International format with country code
      /(\+\d{1,3}[\s.-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/g,

      // US/Canada format: (*************
      /\(\d{3}\)\s*\d{3}[-.\s]?\d{4}/g,

      // UK format: +44 1234 567890
      /(\+44|0044)\s?(\d{4}|\d{5})\s?\d{6}/g,

      // European format: +49 123 4567890
      /(\+\d{2})\s?(\d{3,4})\s?\d{6,7}/g,

      // Phone number with extension
      /(\+\d{1,3}[\s.-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}(\s*ext\.?\s*\d{1,5})?/gi,

      // Phone number with "Tel:" or "Phone:" prefix
      /(tel|phone|call|fax|telephone)[\s:]+(\+\d{1,3}[\s.-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/gi,
    ];

    // Collect all matches
    let allMatches: string[] = [];

    for (const pattern of phonePatterns) {
      const matches = content.match(pattern) || [];
      allMatches = [...allMatches, ...matches];
    }

    // Clean up matches
    const cleanedMatches = allMatches.map((match) => {
      // Remove any text before the actual number (like "Tel:" or "Phone:")
      match = match.replace(/^(tel|phone|call|fax|telephone)[\s:]+/i, "");

      // Standardize formatting
      match = match.replace(/\s+/g, " ").trim();

      return match;
    });

    // Remove duplicates
    return [...new Set(cleanedMatches)];
  }

  /**
   * Find about page URL
   */
  private findAboutPage(links: string[], domain: string): string | null {
    const aboutKeywords = [
      "about",
      "about-us",
      "about_us",
      "aboutus",
      "company",
      "team",
      "who-we-are",
    ];

    for (const link of links) {
      const lowercaseUrl = link.toLowerCase();

      // Check if URL contains about keywords
      if (aboutKeywords.some((keyword) => lowercaseUrl.includes(keyword))) {
        // Ensure it's from the same domain
        try {
          const linkDomain = new URL(link).hostname;
          const baseDomain = new URL(
            this.webScraperService.normalizeUrl(domain)
          ).hostname;

          if (linkDomain === baseDomain) {
            return link;
          }
        } catch (e) {
          // Invalid URL, skip
        }
      }
    }

    return null;
  }

  /**
   * Delay execution for a specified time
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Extract contact name from content and structured data
   */
  private extractContactName(
    content: string,
    structuredData: any[]
  ): string | null {
    // First try to extract from structured data
    for (const data of structuredData) {
      // Check for Person schema
      if (data["@type"] === "Person" && data.name) {
        return data.name;
      }

      // Check for Organization schema with founder or employee
      if (data["@type"] === "Organization") {
        if (data.founder && data.founder.name) {
          return data.founder.name;
        }

        if (
          data.employee &&
          data.employee.length > 0 &&
          data.employee[0].name
        ) {
          return data.employee[0].name;
        }
      }

      // Check for ContactPage schema
      if (
        data["@type"] === "ContactPage" &&
        data.contactPoint &&
        data.contactPoint.name
      ) {
        return data.contactPoint.name;
      }
    }

    // Try to find common contact name patterns in content
    const namePatterns = [
      /contact:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /contact person:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /contact name:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /director:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /manager:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /founder:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
      /ceo:?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)/i,
    ];

    for (const pattern of namePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Extract contact position from content and structured data
   */
  private extractContactPosition(
    content: string,
    structuredData: any[]
  ): string | null {
    // First try to extract from structured data
    for (const data of structuredData) {
      // Check for Person schema with jobTitle
      if (data["@type"] === "Person" && data.jobTitle) {
        return data.jobTitle;
      }

      // Check for ContactPoint with contactType
      if (data["@type"] === "ContactPoint" && data.contactType) {
        return data.contactType;
      }
    }

    // Try to find common position patterns in content
    const positionPatterns = [
      /position:?\s*([A-Za-z\s]+?)(\.|\n|<)/i,
      /job title:?\s*([A-Za-z\s]+?)(\.|\n|<)/i,
      /title:?\s*([A-Za-z\s]+?)(\.|\n|<)/i,
      /role:?\s*([A-Za-z\s]+?)(\.|\n|<)/i,
    ];

    for (const pattern of positionPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Extract address from content and structured data
   */
  private extractAddress(
    content: string,
    structuredData: any[]
  ): string | null {
    // First try to extract from structured data
    for (const data of structuredData) {
      // Check for PostalAddress
      if (data.address && data.address["@type"] === "PostalAddress") {
        const address = data.address;
        const parts = [];

        if (address.streetAddress) parts.push(address.streetAddress);
        if (address.addressLocality) parts.push(address.addressLocality);
        if (address.addressRegion) parts.push(address.addressRegion);
        if (address.postalCode) parts.push(address.postalCode);
        if (address.addressCountry) parts.push(address.addressCountry);

        if (parts.length > 0) {
          return parts.join(", ");
        }
      }

      // Check for Organization or LocalBusiness with address
      if (
        (data["@type"] === "Organization" ||
          data["@type"] === "LocalBusiness") &&
        data.address
      ) {
        if (typeof data.address === "string") {
          return data.address;
        } else if (data.address.streetAddress) {
          const address = data.address;
          const parts = [];

          if (address.streetAddress) parts.push(address.streetAddress);
          if (address.addressLocality) parts.push(address.addressLocality);
          if (address.addressRegion) parts.push(address.addressRegion);
          if (address.postalCode) parts.push(address.postalCode);
          if (address.addressCountry) parts.push(address.addressCountry);

          if (parts.length > 0) {
            return parts.join(", ");
          }
        }
      }
    }

    // Try to find address patterns in content
    const addressPatterns = [
      /address:?\s*([^<>\n]{10,100}?)(\n|<)/i,
      /location:?\s*([^<>\n]{10,100}?)(\n|<)/i,
      /office:?\s*([^<>\n]{10,100}?)(\n|<)/i,
    ];

    for (const pattern of addressPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Find contact form URL
   */
  private findContactForm(links: string[], domain: string): string | null {
    const formKeywords = [
      "contact",
      "form",
      "get-in-touch",
      "reach-out",
      "message-us",
    ];

    for (const link of links) {
      const lowercaseUrl = link.toLowerCase();

      // Check if URL contains form keywords
      if (formKeywords.some((keyword) => lowercaseUrl.includes(keyword))) {
        // Ensure it's from the same domain
        try {
          const linkDomain = new URL(link).hostname;
          const baseDomain = new URL(
            this.webScraperService.normalizeUrl(domain)
          ).hostname;

          if (linkDomain === baseDomain) {
            return link;
          }
        } catch (e) {
          // Invalid URL, skip
        }
      }
    }

    return null;
  }

  /**
   * Detect if a page has a contact form
   */
  private detectContactForm(pageData: any): boolean {
    // Check if the page has form elements
    const formElements = ["form", "input", "textarea", "button", "submit"];
    const content = pageData.content.toLowerCase();

    // Check for form-related keywords in the content
    const formKeywords = [
      "contact us",
      "send message",
      "get in touch",
      "submit",
      "send email",
    ];

    // Check if any form elements are present in the content
    const hasFormElements = formElements.some((element) =>
      content.includes(`<${element}`)
    );

    // Check if any form keywords are present in the content
    const hasFormKeywords = formKeywords.some((keyword) =>
      content.includes(keyword)
    );

    return hasFormElements && hasFormKeywords;
  }

  /**
   * Detect if a page has chat support
   */
  private detectChatSupport(content: string): boolean {
    const chatKeywords = [
      "live chat",
      "chat with us",
      "chat support",
      "chat now",
      "start chat",
      "chat online",
      "chat bot",
      "chatbot",
      "support chat",
      "messenger",
      "intercom",
      "drift",
      "zendesk",
      "livechat",
      "tawk",
      "olark",
      "freshchat",
      "crisp",
    ];

    const lowercaseContent = content.toLowerCase();

    return chatKeywords.some((keyword) => lowercaseContent.includes(keyword));
  }

  /**
   * Enhanced email extraction using multiple patterns
   */
  private extractEmailsEnhanced(content: string): string[] {
    const emails = new Set<string>();

    // Apply all enhanced email patterns
    for (const pattern of this.enhancedEmailPatterns) {
      const matches = content.match(pattern) || [];
      matches.forEach((match) => {
        // Clean up the match
        let email = match.replace(/^mailto:/, "");
        email = email.replace(/\[at\]/gi, "@");
        email = email.replace(/\(at\)/gi, "@");
        email = email.replace(/\[dot\]/gi, ".");
        email = email.replace(/\(dot\)/gi, ".");
        email = email.replace(/\s+/g, "");

        // Validate email format
        if (this.isValidEmail(email)) {
          emails.add(email.toLowerCase());
        }
      });
    }

    return Array.from(emails);
  }

  /**
   * ML-based contact extraction fallback
   */
  private async mlBasedContactExtraction(
    domain: string
  ): Promise<MLContactPrediction> {
    this.logger.debug(`Attempting ML-based contact extraction for ${domain}`);

    try {
      // Get page content for analysis
      const mainPageData = await this.webScraperService.scrapeWebsite(domain);
      const content = mainPageData.content;

      const prediction: MLContactPrediction = {
        emails: [],
        phones: [],
        names: [],
      };

      // Enhanced email detection with context
      const emailMatches = this.extractEmailsWithContext(content);
      prediction.emails = emailMatches.map((match) => ({
        email: match.email,
        confidence: this.calculateEmailConfidence(match.email, match.context),
        context: match.context,
      }));

      // Enhanced phone detection with context
      const phoneMatches = this.extractPhonesWithContext(content);
      prediction.phones = phoneMatches.map((match) => ({
        phone: match.phone,
        confidence: this.calculatePhoneConfidence(match.phone, match.context),
        context: match.context,
      }));

      // Enhanced name detection with context
      const nameMatches = this.extractNamesWithContext(content);
      prediction.names = nameMatches.map((match) => ({
        name: match.name,
        position: match.position,
        confidence: this.calculateNameConfidence(match.name, match.context),
        context: match.context,
      }));

      return prediction;
    } catch (error) {
      this.logger.error(
        `ML-based extraction failed for ${domain}: ${error.message}`
      );
      return { emails: [], phones: [], names: [] };
    }
  }

  /**
   * Extract emails with surrounding context
   */
  private extractEmailsWithContext(content: string): Array<{
    email: string;
    context: string;
  }> {
    const results: Array<{ email: string; context: string }> = [];
    const emails = this.extractEmailsEnhanced(content);

    emails.forEach((email) => {
      // Find context around the email
      const emailIndex = content.toLowerCase().indexOf(email.toLowerCase());
      if (emailIndex !== -1) {
        const start = Math.max(0, emailIndex - 100);
        const end = Math.min(content.length, emailIndex + email.length + 100);
        const context = content.substring(start, end).trim();

        results.push({ email, context });
      }
    });

    return results;
  }

  /**
   * Extract phone numbers with surrounding context
   */
  private extractPhonesWithContext(content: string): Array<{
    phone: string;
    context: string;
  }> {
    const results: Array<{ phone: string; context: string }> = [];
    const phones = this.extractPhoneNumbers(content);

    phones.forEach((phone) => {
      // Find context around the phone number
      const phoneIndex = content.indexOf(phone);
      if (phoneIndex !== -1) {
        const start = Math.max(0, phoneIndex - 100);
        const end = Math.min(content.length, phoneIndex + phone.length + 100);
        const context = content.substring(start, end).trim();

        results.push({ phone, context });
      }
    });

    return results;
  }

  /**
   * Extract names with surrounding context and positions
   */
  private extractNamesWithContext(content: string): Array<{
    name: string;
    position?: string;
    context: string;
  }> {
    const results: Array<{ name: string; position?: string; context: string }> =
      [];

    // Look for name patterns with roles
    for (const pattern of this.contactRolePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1]) {
          const start = Math.max(0, match.index! - 50);
          const end = Math.min(
            content.length,
            match.index! + match[0].length + 50
          );
          const context = content.substring(start, end).trim();

          results.push({
            name: match[1].trim(),
            context,
          });
        }
      }
    }

    return results;
  }

  /**
   * Calculate confidence score for email based on context
   */
  private calculateEmailConfidence(email: string, context: string): number {
    let confidence = 0.5; // Base confidence

    // Higher confidence for emails in contact context
    if (/contact|reach|email|write/i.test(context)) {
      confidence += 0.3;
    }

    // Higher confidence for role-based emails
    if (/ceo|founder|director|manager|lead|head/i.test(context)) {
      confidence += 0.2;
    }

    // Lower confidence for generic emails
    if (/noreply|no-reply|donotreply|support@|info@/i.test(email)) {
      confidence -= 0.2;
    }

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  /**
   * Calculate confidence score for phone based on context
   */
  private calculatePhoneConfidence(phone: string, context: string): number {
    let confidence = 0.6; // Base confidence

    // Higher confidence for phones in contact context
    if (/contact|call|phone|tel|reach/i.test(context)) {
      confidence += 0.3;
    }

    // Lower confidence for fax numbers
    if (/fax/i.test(context)) {
      confidence -= 0.2;
    }

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  /**
   * Calculate confidence score for name based on context
   */
  private calculateNameConfidence(name: string, context: string): number {
    let confidence = 0.4; // Base confidence

    // Higher confidence for names with clear roles
    if (/ceo|founder|director|manager|lead|head/i.test(context)) {
      confidence += 0.4;
    }

    // Higher confidence for names in contact context
    if (/contact|reach|email/i.test(context)) {
      confidence += 0.2;
    }

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/;
    return (
      emailRegex.test(email) && !email.includes("..") && email.length < 254
    );
  }

  /**
   * Calculate overall confidence for contact info
   */
  private calculateOverallConfidence(
    contactInfo: ContactInfo,
    method: string
  ): number {
    let confidence = 0;

    // Base confidence by method
    switch (method) {
      case "direct":
        confidence = 0.9;
        break;
      case "pattern_matching":
        confidence = 0.7;
        break;
      case "ml_fallback":
        confidence = 0.5;
        break;
      default:
        confidence = 0.3;
    }

    // Adjust based on amount of data found
    if (contactInfo.emails.length > 0) confidence += 0.1;
    if (contactInfo.phoneNumbers.length > 0) confidence += 0.1;
    if (contactInfo.contactName) confidence += 0.1;
    if (contactInfo.contactPage) confidence += 0.1;

    return Math.min(1.0, confidence);
  }
}
