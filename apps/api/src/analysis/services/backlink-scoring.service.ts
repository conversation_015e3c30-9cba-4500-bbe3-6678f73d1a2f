import { Injectable, Logger } from "@nestjs/common";
import { AhrefsService } from "../../third-party/ahrefs/ahrefs.service";
import { SimilarWebService } from "../../third-party/similarweb/similarweb.service";
import { WhoisXmlService } from "../../third-party/whoisxml/whoisxml.service";
import { CacheService } from "../../cache/cache.service";

export interface ScoringWeights {
  domainRating: number; // 30%
  trafficVolume: number; // 20%
  keywordRelevance: number; // 20%
  websiteAge: number; // 10%
  industryMatch: number; // 10%
  countryLanguageMatch: number; // 5%
  contactInfoPresent: number; // 5%
}

export interface ScoringFactors {
  domainRating: number;
  trafficVolume: number;
  keywordRelevance: number;
  websiteAge: number;
  industryMatch: number;
  countryLanguageMatch: number;
  contactInfoPresent: number;
}

export interface DomainData {
  domain: string;
  keywords: string[];
  industry: string;
  language: string;
  country: string;
  hasContactInfo: boolean;
}

@Injectable()
export class BacklinkScoringService {
  private readonly logger = new Logger(BacklinkScoringService.name);

  // Exact weights as specified in requirements
  private readonly requiredWeights: ScoringWeights = {
    domainRating: 0.3, // DR (30%)
    trafficVolume: 0.2, // Traffic Volume (20%)
    keywordRelevance: 0.2, // Keyword Relevance to Target (20%)
    websiteAge: 0.1, // Website Age (10%)
    industryMatch: 0.1, // Industry Match (10%)
    countryLanguageMatch: 0.05, // Country Match / Language Match (5%)
    contactInfoPresent: 0.05, // Contact Info Present (5%)
  };

  constructor(
    private readonly ahrefsService: AhrefsService,
    private readonly similarWebService: SimilarWebService,
    private readonly whoisXmlService: WhoisXmlService,
    private readonly cacheService: CacheService
  ) {}

  /**
   * Calculate backlink score for a domain using the exact required algorithm
   */
  async calculateBacklinkScore(
    targetDomain: DomainData,
    candidateDomain: DomainData,
    weights: Partial<ScoringWeights> = {}
  ): Promise<{
    score: number;
    factors: ScoringFactors;
    breakdown: Record<
      string,
      { value: number; weight: number; contribution: number }
    >;
  }> {
    const finalWeights = { ...this.requiredWeights, ...weights };

    const factors = await this.calculateScoringFactors(
      targetDomain,
      candidateDomain
    );

    // Calculate weighted score
    const score =
      factors.domainRating * finalWeights.domainRating +
      factors.trafficVolume * finalWeights.trafficVolume +
      factors.keywordRelevance * finalWeights.keywordRelevance +
      factors.websiteAge * finalWeights.websiteAge +
      factors.industryMatch * finalWeights.industryMatch +
      factors.countryLanguageMatch * finalWeights.countryLanguageMatch +
      factors.contactInfoPresent * finalWeights.contactInfoPresent;

    // Ensure score is between 0 and 100
    const finalScore = Math.max(0, Math.min(100, score * 100));

    // Create breakdown for transparency
    const breakdown = {
      domainRating: {
        value: factors.domainRating,
        weight: finalWeights.domainRating,
        contribution: factors.domainRating * finalWeights.domainRating * 100,
      },
      trafficVolume: {
        value: factors.trafficVolume,
        weight: finalWeights.trafficVolume,
        contribution: factors.trafficVolume * finalWeights.trafficVolume * 100,
      },
      keywordRelevance: {
        value: factors.keywordRelevance,
        weight: finalWeights.keywordRelevance,
        contribution:
          factors.keywordRelevance * finalWeights.keywordRelevance * 100,
      },
      websiteAge: {
        value: factors.websiteAge,
        weight: finalWeights.websiteAge,
        contribution: factors.websiteAge * finalWeights.websiteAge * 100,
      },
      industryMatch: {
        value: factors.industryMatch,
        weight: finalWeights.industryMatch,
        contribution: factors.industryMatch * finalWeights.industryMatch * 100,
      },
      countryLanguageMatch: {
        value: factors.countryLanguageMatch,
        weight: finalWeights.countryLanguageMatch,
        contribution:
          factors.countryLanguageMatch *
          finalWeights.countryLanguageMatch *
          100,
      },
      contactInfoPresent: {
        value: factors.contactInfoPresent,
        weight: finalWeights.contactInfoPresent,
        contribution:
          factors.contactInfoPresent * finalWeights.contactInfoPresent * 100,
      },
    };

    this.logger.debug(
      `Calculated backlink score for ${candidateDomain.domain}: ${finalScore}`,
      {
        factors,
        weights: finalWeights,
        breakdown,
      }
    );

    return {
      score: finalScore,
      factors,
      breakdown,
    };
  }

  /**
   * Calculate individual scoring factors
   */
  private async calculateScoringFactors(
    targetDomain: DomainData,
    candidateDomain: DomainData
  ): Promise<ScoringFactors> {
    const [
      domainRating,
      trafficVolume,
      keywordRelevance,
      websiteAge,
      industryMatch,
      countryLanguageMatch,
      contactInfoPresent,
    ] = await Promise.all([
      this.calculateDomainRatingFactor(candidateDomain.domain),
      this.calculateTrafficVolumeFactor(candidateDomain.domain),
      this.calculateKeywordRelevanceFactor(targetDomain, candidateDomain),
      this.calculateWebsiteAgeFactor(candidateDomain.domain),
      this.calculateIndustryMatchFactor(targetDomain, candidateDomain),
      this.calculateCountryLanguageMatchFactor(targetDomain, candidateDomain),
      this.calculateContactInfoFactor(candidateDomain),
    ]);

    return {
      domainRating,
      trafficVolume,
      keywordRelevance,
      websiteAge,
      industryMatch,
      countryLanguageMatch,
      contactInfoPresent,
    };
  }

  /**
   * Calculate Domain Rating factor (0-1)
   */
  private async calculateDomainRatingFactor(domain: string): Promise<number> {
    try {
      const ahrefsData = await this.ahrefsService.getDomainRating(domain);
      if (ahrefsData?.domain_rating) {
        // Normalize DR (0-100) to 0-1 scale
        return Math.min(1, ahrefsData.domain_rating / 100);
      }
      // Return a default value if no data is available
      return 0.5; // Assume medium domain rating when data is unavailable
    } catch (error) {
      this.logger.warn(
        `Failed to get domain rating for ${domain}: ${error.message}`
      );
      return 0.5; // Fallback to medium rating
    }
  }

  /**
   * Calculate Traffic Volume factor (0-1)
   */
  private async calculateTrafficVolumeFactor(domain: string): Promise<number> {
    try {
      const trafficData = await this.similarWebService.getTrafficData(domain);
      if (trafficData?.visits) {
        // Normalize traffic volume using logarithmic scale
        // 1M+ visits = 1.0, 100K+ = 0.8, 10K+ = 0.6, 1K+ = 0.4, 100+ = 0.2, <100 = 0.1
        const visits = trafficData.visits;
        if (visits >= 1000000) return 1.0;
        if (visits >= 100000) return 0.8;
        if (visits >= 10000) return 0.6;
        if (visits >= 1000) return 0.4;
        if (visits >= 100) return 0.2;
        return 0.1;
      }
      // Return a default value if no data is available
      return 0.3; // Assume low-medium traffic when data is unavailable
    } catch (error) {
      this.logger.warn(
        `Failed to get traffic data for ${domain}: ${error.message}`
      );
      return 0.3; // Fallback to low-medium traffic
    }
  }

  /**
   * Calculate Keyword Relevance factor (0-1)
   */
  private calculateKeywordRelevanceFactor(
    targetDomain: DomainData,
    candidateDomain: DomainData
  ): number {
    const targetKeywords = targetDomain.keywords.map((k) => k.toLowerCase());
    const candidateKeywords = candidateDomain.keywords.map((k) =>
      k.toLowerCase()
    );

    if (targetKeywords.length === 0 || candidateKeywords.length === 0) {
      return 0;
    }

    // Calculate Jaccard similarity
    const intersection = targetKeywords.filter((k) =>
      candidateKeywords.includes(k)
    );
    const union = [...new Set([...targetKeywords, ...candidateKeywords])];

    return intersection.length / union.length;
  }

  /**
   * Calculate Website Age factor (0-1)
   */
  private async calculateWebsiteAgeFactor(domain: string): Promise<number> {
    try {
      const ageYears = await this.whoisXmlService.getDomainAge(domain);
      // Normalize age: 10+ years = 1.0, 5+ years = 0.8, 2+ years = 0.6, 1+ year = 0.4, <1 year = 0.2
      if (ageYears >= 10) return 1.0;
      if (ageYears >= 5) return 0.8;
      if (ageYears >= 2) return 0.6;
      if (ageYears >= 1) return 0.4;
      return 0.2;
    } catch (error) {
      this.logger.warn(
        `Failed to get domain age for ${domain}: ${error.message}`
      );
      return 0.5; // Fallback to medium age when data is unavailable
    }
  }

  /**
   * Calculate Industry Match factor (0-1)
   */
  private calculateIndustryMatchFactor(
    targetDomain: DomainData,
    candidateDomain: DomainData
  ): number {
    if (!targetDomain.industry || !candidateDomain.industry) {
      return 0;
    }

    // Exact match
    if (
      targetDomain.industry.toLowerCase() ===
      candidateDomain.industry.toLowerCase()
    ) {
      return 1.0;
    }

    // Partial match (contains similar words)
    const targetWords = targetDomain.industry.toLowerCase().split(/\s+/);
    const candidateWords = candidateDomain.industry.toLowerCase().split(/\s+/);
    const commonWords = targetWords.filter((word) =>
      candidateWords.includes(word)
    );

    if (commonWords.length > 0) {
      return 0.5;
    }

    return 0;
  }

  /**
   * Calculate Country/Language Match factor (0-1)
   */
  private calculateCountryLanguageMatchFactor(
    targetDomain: DomainData,
    candidateDomain: DomainData
  ): number {
    let score = 0;

    // Language match (50% of this factor)
    if (targetDomain.language && candidateDomain.language) {
      if (
        targetDomain.language.toLowerCase() ===
        candidateDomain.language.toLowerCase()
      ) {
        score += 0.5;
      }
    }

    // Country match (50% of this factor)
    if (targetDomain.country && candidateDomain.country) {
      if (
        targetDomain.country.toLowerCase() ===
        candidateDomain.country.toLowerCase()
      ) {
        score += 0.5;
      }
    }

    return score;
  }

  /**
   * Calculate Contact Info Present factor (0-1)
   */
  private calculateContactInfoFactor(candidateDomain: DomainData): number {
    return candidateDomain.hasContactInfo ? 1.0 : 0.0;
  }
}
