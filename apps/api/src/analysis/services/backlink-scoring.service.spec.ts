import { Test, TestingModule } from '@nestjs/testing';
import { BacklinkScoringService, DomainData } from './backlink-scoring.service';
import { AhrefsService } from '../../third-party/ahrefs/ahrefs.service';
import { SimilarWebService } from '../../third-party/similarweb/similarweb.service';
import { WhoisXmlService } from '../../third-party/whoisxml/whoisxml.service';
import { CacheService } from '../../cache/cache.service';

describe('BacklinkScoringService', () => {
  let service: BacklinkScoringService;
  let ahrefsService: jest.Mocked<AhrefsService>;
  let similarWebService: jest.Mocked<SimilarWebService>;
  let whoisXmlService: jest.Mocked<WhoisXmlService>;
  let cacheService: jest.Mocked<CacheService>;

  const mockAhrefsService = {
    getDomainRating: jest.fn(),
  };

  const mockSimilarWebService = {
    getTrafficData: jest.fn(),
  };

  const mockWhoisXmlService = {
    getDomainAge: jest.fn(),
  };

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    getOrSet: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BacklinkScoringService,
        {
          provide: AhrefsService,
          useValue: mockAhrefsService,
        },
        {
          provide: SimilarWebService,
          useValue: mockSimilarWebService,
        },
        {
          provide: WhoisXmlService,
          useValue: mockWhoisXmlService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<BacklinkScoringService>(BacklinkScoringService);
    ahrefsService = module.get(AhrefsService);
    similarWebService = module.get(SimilarWebService);
    whoisXmlService = module.get(WhoisXmlService);
    cacheService = module.get(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('calculateBacklinkScore', () => {
    const targetDomain: DomainData = {
      domain: 'target.com',
      keywords: ['seo', 'marketing', 'digital'],
      industry: 'Marketing',
      language: 'en',
      country: 'United States',
      hasContactInfo: true,
    };

    const candidateDomain: DomainData = {
      domain: 'candidate.com',
      keywords: ['seo', 'tools', 'analytics'],
      industry: 'Marketing',
      language: 'en',
      country: 'United States',
      hasContactInfo: true,
    };

    beforeEach(() => {
      // Mock API responses
      mockAhrefsService.getDomainRating.mockResolvedValue({
        domain: 'candidate.com',
        domain_rating: 75,
        ahrefs_rank: 1000,
        backlinks: 50000,
        referring_domains: 1000,
        organic_keywords: 5000,
        organic_traffic: 10000,
      });

      mockSimilarWebService.getTrafficData.mockResolvedValue({
        domain: 'candidate.com',
        visits: 500000,
        unique_visitors: 300000,
        page_views: 1000000,
        bounce_rate: 0.4,
        pages_per_visit: 2.5,
        avg_visit_duration: 180,
        global_rank: 5000,
        country_rank: 500,
        category_rank: 50,
      });

      mockWhoisXmlService.getDomainAge.mockResolvedValue(5);
    });

    it('should calculate backlink score with correct weights', async () => {
      const result = await service.calculateBacklinkScore(targetDomain, candidateDomain);

      expect(result.score).toBeGreaterThan(0);
      expect(result.score).toBeLessThanOrEqual(100);
      expect(result.factors).toBeDefined();
      expect(result.breakdown).toBeDefined();

      // Verify all factors are present
      expect(result.factors.domainRating).toBeDefined();
      expect(result.factors.trafficVolume).toBeDefined();
      expect(result.factors.keywordRelevance).toBeDefined();
      expect(result.factors.websiteAge).toBeDefined();
      expect(result.factors.industryMatch).toBeDefined();
      expect(result.factors.countryLanguageMatch).toBeDefined();
      expect(result.factors.contactInfoPresent).toBeDefined();

      // Verify breakdown has correct weights
      expect(result.breakdown.domainRating.weight).toBe(0.30);
      expect(result.breakdown.trafficVolume.weight).toBe(0.20);
      expect(result.breakdown.keywordRelevance.weight).toBe(0.20);
      expect(result.breakdown.websiteAge.weight).toBe(0.10);
      expect(result.breakdown.industryMatch.weight).toBe(0.10);
      expect(result.breakdown.countryLanguageMatch.weight).toBe(0.05);
      expect(result.breakdown.contactInfoPresent.weight).toBe(0.05);
    });

    it('should handle perfect match scenario', async () => {
      const perfectCandidate: DomainData = {
        ...candidateDomain,
        keywords: targetDomain.keywords, // Exact keyword match
        industry: targetDomain.industry, // Exact industry match
        language: targetDomain.language, // Exact language match
        country: targetDomain.country, // Exact country match
        hasContactInfo: true,
      };

      const result = await service.calculateBacklinkScore(targetDomain, perfectCandidate);

      expect(result.factors.keywordRelevance).toBe(1.0);
      expect(result.factors.industryMatch).toBe(1.0);
      expect(result.factors.countryLanguageMatch).toBe(1.0);
      expect(result.factors.contactInfoPresent).toBe(1.0);
    });

    it('should handle no match scenario', async () => {
      const noMatchCandidate: DomainData = {
        domain: 'nomatch.com',
        keywords: ['cooking', 'recipes'],
        industry: 'Food',
        language: 'es',
        country: 'Spain',
        hasContactInfo: false,
      };

      // Mock low-quality metrics
      mockAhrefsService.getDomainRating.mockResolvedValue({
        domain: 'nomatch.com',
        domain_rating: 10,
        ahrefs_rank: 100000,
        backlinks: 50,
        referring_domains: 10,
        organic_keywords: 100,
        organic_traffic: 50,
      });

      mockSimilarWebService.getTrafficData.mockResolvedValue({
        domain: 'nomatch.com',
        visits: 50,
        unique_visitors: 30,
        page_views: 100,
        bounce_rate: 0.8,
        pages_per_visit: 1.2,
        avg_visit_duration: 30,
        global_rank: 1000000,
        country_rank: 50000,
        category_rank: 5000,
      });

      mockWhoisXmlService.getDomainAge.mockResolvedValue(0.5);

      const result = await service.calculateBacklinkScore(targetDomain, noMatchCandidate);

      expect(result.factors.keywordRelevance).toBe(0);
      expect(result.factors.industryMatch).toBe(0);
      expect(result.factors.countryLanguageMatch).toBe(0);
      expect(result.factors.contactInfoPresent).toBe(0);
      expect(result.score).toBeLessThan(50);
    });

    it('should handle API failures gracefully', async () => {
      mockAhrefsService.getDomainRating.mockRejectedValue(new Error('API Error'));
      mockSimilarWebService.getTrafficData.mockRejectedValue(new Error('API Error'));
      mockWhoisXmlService.getDomainAge.mockRejectedValue(new Error('API Error'));

      const result = await service.calculateBacklinkScore(targetDomain, candidateDomain);

      expect(result.score).toBeDefined();
      expect(result.factors.domainRating).toBe(0);
      expect(result.factors.trafficVolume).toBe(0);
      expect(result.factors.websiteAge).toBe(0);
    });

    it('should allow custom weights', async () => {
      const customWeights = {
        domainRating: 0.50,
        trafficVolume: 0.30,
        keywordRelevance: 0.10,
        websiteAge: 0.05,
        industryMatch: 0.03,
        countryLanguageMatch: 0.01,
        contactInfoPresent: 0.01,
      };

      const result = await service.calculateBacklinkScore(
        targetDomain,
        candidateDomain,
        customWeights,
      );

      expect(result.breakdown.domainRating.weight).toBe(0.50);
      expect(result.breakdown.trafficVolume.weight).toBe(0.30);
      expect(result.breakdown.keywordRelevance.weight).toBe(0.10);
    });
  });

  describe('keyword relevance calculation', () => {
    it('should calculate Jaccard similarity correctly', async () => {
      const target: DomainData = {
        domain: 'target.com',
        keywords: ['seo', 'marketing', 'digital'],
        industry: 'Marketing',
        language: 'en',
        country: 'US',
        hasContactInfo: true,
      };

      const candidate: DomainData = {
        domain: 'candidate.com',
        keywords: ['seo', 'tools'], // 1 common keyword out of 4 total unique
        industry: 'Marketing',
        language: 'en',
        country: 'US',
        hasContactInfo: true,
      };

      // Mock other services to isolate keyword relevance test
      mockAhrefsService.getDomainRating.mockResolvedValue(null);
      mockSimilarWebService.getTrafficData.mockResolvedValue(null);
      mockWhoisXmlService.getDomainAge.mockResolvedValue(1);

      const result = await service.calculateBacklinkScore(target, candidate);

      // Jaccard similarity: intersection(1) / union(4) = 0.25
      expect(result.factors.keywordRelevance).toBe(0.25);
    });
  });
});
