import { Injectable, Logger, Inject } from "@nestjs/common";
import { WebScraperService } from "./web-scraper.service";
import { ContactExtractorService } from "./contact-extractor.service";
import { ContentAnalyzerService } from "./content-analyzer.service";
import { SeoApiService } from "./seo-api.service";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";

@Injectable()
export class DomainAnalyzerService {
  private readonly logger = new Logger(DomainAnalyzerService.name);
  private readonly requestDelay = 2000; // 2 seconds delay between domain requests

  constructor(
    @Inject(WebScraperService)
    private readonly webScraperService: WebScraperService,
    @Inject(ContactExtractorService)
    private readonly contactExtractorService: ContactExtractorService,
    @Inject(ContentAnalyzerService)
    private readonly contentAnalyzerService: ContentAnalyzerService,
    @Inject(SeoApiService)
    private readonly seoApiService: SeoApiService,
    @Inject(HttpService)
    private readonly httpService: HttpService
  ) {}

  /**
   * Analyze a target domain and calculate its relevance to a user domain
   */
  async analyzeDomain(
    targetDomain: string,
    userDomain: string
  ): Promise<{
    // Basic domain info
    title: string;
    description: string;
    keywords: string[];

    // Analysis results
    relevanceScore: number;
    industry: string;
    category: string;
    language: string;

    // SEO metrics
    domainAuthority: number;
    backlinks: number;
    traffic: number;
    trafficTrend: "upward" | "downward" | "stable";
    topKeywords: string[];
    topPages: string[];

    // Domain metrics
    websiteAge: number;
    topCountry: string;

    // Contact info
    contactEmail: string | null;
    contactPage: string | null;
    contactName: string | null;
    contactPosition: string | null;
    contactForm: string | null;
    address: string | null;
    socialProfiles: string[];
    phoneNumbers: string[];
    chatSupport: boolean;

    // Enhanced metadata
    favicon: string | null;
    author: string | null;
    publishDate: string | null;
    lastModified: string | null;
    headings: { h1: string[]; h2: string[]; h3: string[] };
    sentiment: number;
    topTopics: string[];
  }> {
    try {
      this.logger.log(
        `Analyzing domain: ${targetDomain} relative to ${userDomain}`
      );

      // 1. Scrape target domain
      const targetData = await this.webScraperService.scrapeWebsite(
        targetDomain
      );

      // 2. Extract contact information
      const contactInfo = await this.contactExtractorService.extractContactInfo(
        targetDomain
      );

      // 3. Determine industry
      const industry = this.contentAnalyzerService.determineIndustry(
        targetData.content,
        targetData.keywords
      );

      // 4. Scrape user domain for comparison
      const userData = await this.webScraperService.scrapeWebsite(userDomain);

      // 5. Determine user domain industry
      const userIndustry = this.contentAnalyzerService.determineIndustry(
        userData.content,
        userData.keywords
      );

      // 6. Calculate relevance score
      const relevanceScore =
        this.contentAnalyzerService.calculateRelevanceScore(
          userData.content,
          userData.keywords,
          userIndustry,
          targetData.content,
          targetData.keywords,
          industry
        );

      // 7. Fetch SEO metrics
      const seoMetrics = await this.fetchSEOMetrics(targetDomain);

      // 8. Fetch domain age and other metrics
      const domainMetrics = await this.fetchDomainMetrics(targetDomain);

      // 9. Determine website category based on content and links
      const category = this.determineWebsiteCategory(targetData);

      // 10. Calculate sentiment score
      const sentiment = this.calculateSentiment(targetData.content);

      // 11. Extract top topics
      const topTopics = this.extractTopTopics(
        targetData.content,
        targetData.keywords
      );

      // Combine all data
      return {
        // Basic domain info
        title: targetData.title,
        description: targetData.description,
        keywords: targetData.keywords,

        // Analysis results
        relevanceScore,
        industry,
        category,
        language: targetData.language,

        // SEO metrics
        domainAuthority: seoMetrics.domainAuthority,
        backlinks: seoMetrics.backlinks,
        traffic: seoMetrics.traffic,
        trafficTrend: seoMetrics.trafficTrend,
        topKeywords: seoMetrics.topKeywords,
        topPages: seoMetrics.topPages,

        // Domain metrics
        websiteAge: domainMetrics.ageInMonths,
        topCountry: domainMetrics.topCountry,

        // Contact info
        contactEmail:
          contactInfo.emails.length > 0 ? contactInfo.emails[0] : null,
        contactPage: contactInfo.contactPage,
        contactName: contactInfo.contactName,
        contactPosition: contactInfo.contactPosition,
        contactForm: contactInfo.contactForm,
        address: contactInfo.address,
        socialProfiles: contactInfo.socialProfiles,
        phoneNumbers: contactInfo.phoneNumbers,
        chatSupport: contactInfo.chatSupport,

        // Enhanced metadata
        favicon: targetData.favicon,
        author: targetData.author || null,
        publishDate: targetData.publishDate,
        lastModified: targetData.lastModified,
        headings: targetData.headings,
        sentiment,
        topTopics,
      };
    } catch (error) {
      this.logger.error(
        `Error analyzing domain ${targetDomain}: ${error.message}`,
        error.stack
      );

      // Return default values on error
      return {
        title: targetDomain,
        description: "",
        keywords: [],
        relevanceScore: 50,
        industry: "Unknown",
        category: "Unknown",
        language: "en",
        domainAuthority: 0,
        backlinks: 0,
        traffic: 0,
        trafficTrend: "stable",
        topKeywords: [],
        topPages: [],
        websiteAge: 0,
        topCountry: "Unknown",
        contactEmail: null,
        contactPage: null,
        contactName: null,
        contactPosition: null,
        contactForm: null,
        address: null,
        socialProfiles: [],
        phoneNumbers: [],
        chatSupport: false,
        favicon: null,
        author: null,
        publishDate: null,
        lastModified: null,
        headings: { h1: [], h2: [], h3: [] },
        sentiment: 0,
        topTopics: [],
      };
    }
  }

  /**
   * Fetch SEO metrics for a domain using the SEO API service
   */
  private async fetchSEOMetrics(domain: string): Promise<{
    domainAuthority: number;
    backlinks: number;
    traffic: number;
    trafficTrend: "upward" | "downward" | "stable";
    topKeywords: string[];
    topPages: string[];
  }> {
    try {
      // Get metrics from SEO API service
      const metrics = await this.seoApiService.getDomainMetrics(domain);

      // Extract top keywords from the API response
      const topKeywords = metrics.keywordRankings
        .sort((a, b) => a.position - b.position)
        .slice(0, 5)
        .map((k) => k.keyword);

      // Extract top pages from the API response
      const topPages = metrics.topPages
        .sort((a, b) => b.traffic - a.traffic)
        .slice(0, 5)
        .map((p) => p.url);

      // Determine traffic trend based on domain authority and backlinks
      let trafficTrend: "upward" | "downward" | "stable" = "stable";

      // This is a simplified logic - in a real app, you'd compare historical data
      if (metrics.domainAuthority > 50 && metrics.backlinks > 1000) {
        trafficTrend = "upward";
      } else if (metrics.domainAuthority < 20 && metrics.backlinks < 100) {
        trafficTrend = "downward";
      }

      return {
        domainAuthority: metrics.domainAuthority,
        backlinks: metrics.backlinks,
        traffic: metrics.organicTraffic,
        trafficTrend,
        topKeywords:
          topKeywords.length > 0
            ? topKeywords
            : this.generateDefaultKeywords(domain),
        topPages:
          topPages.length > 0 ? topPages : this.generateDefaultPages(domain),
      };
    } catch (error) {
      this.logger.error(
        `Error fetching SEO metrics for ${domain}: ${error.message}`,
        error.stack
      );
      return {
        domainAuthority: 0,
        backlinks: 0,
        traffic: 0,
        trafficTrend: "stable",
        topKeywords: [],
        topPages: [],
      };
    }
  }

  /**
   * Generate default keywords when API doesn't return any
   */
  private generateDefaultKeywords(domain: string): string[] {
    const baseDomain = domain.split(".")[0];
    return [
      baseDomain,
      `best ${baseDomain}`,
      `${baseDomain} services`,
      `${baseDomain} reviews`,
      `${baseDomain} alternatives`,
    ];
  }

  /**
   * Generate default pages when API doesn't return any
   */
  private generateDefaultPages(domain: string): string[] {
    return [
      `https://${domain}/`,
      `https://${domain}/about`,
      `https://${domain}/services`,
      `https://${domain}/blog`,
      `https://${domain}/contact`,
    ];
  }

  /**
   * Fetch domain age and other metrics
   * In a real implementation, this would connect to WHOIS or domain API
   */
  private async fetchDomainMetrics(domain: string): Promise<{
    ageInMonths: number;
    topCountry: string;
  }> {
    try {
      // Simulate API delay
      await this.delay(300);

      // Generate mock data based on domain name
      // In a real implementation, this would call an actual WHOIS API
      const domainParts = domain.split(".");
      const baseDomain = domainParts[0];
      const tld = domainParts[domainParts.length - 1];

      let baseAge = 24; // 2 years as base

      if (baseDomain.length <= 5) {
        baseAge += 60; // Add 5 years
      } else if (baseDomain.length <= 8) {
        baseAge += 36; // Add 3 years
      }

      if (["com", "org", "net"].includes(tld)) {
        baseAge += 24; // Add 2 years
      }

      const randomFactor = Math.floor(Math.random() * 24); // +/- 2 years
      const ageInMonths = Math.max(1, baseAge + randomFactor - 12);

      // Determine top country based on TLD
      let topCountry = "United States";
      if (tld === "uk") topCountry = "United Kingdom";
      else if (tld === "ca") topCountry = "Canada";
      else if (tld === "au") topCountry = "Australia";
      else if (tld === "de") topCountry = "Germany";
      else if (tld === "fr") topCountry = "France";
      else if (tld === "jp") topCountry = "Japan";
      else if (tld === "in") topCountry = "India";
      else if (tld === "br") topCountry = "Brazil";
      else if (Math.random() > 0.7) {
        const countries = [
          "United States",
          "United Kingdom",
          "Canada",
          "Germany",
          "France",
          "India",
          "Australia",
          "Japan",
          "Brazil",
          "Spain",
        ];
        topCountry = countries[Math.floor(Math.random() * countries.length)];
      }

      return {
        ageInMonths,
        topCountry,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching domain metrics for ${domain}: ${error.message}`,
        error.stack
      );
      return {
        ageInMonths: 12,
        topCountry: "Unknown",
      };
    }
  }

  /**
   * Determine website category based on content and links
   */
  private determineWebsiteCategory(data: {
    title: string;
    description: string;
    content: string;
    links: string[];
  }): string {
    const content = (
      data.title +
      " " +
      data.description +
      " " +
      data.content
    ).toLowerCase();

    // Check for e-commerce indicators
    if (
      content.includes("shop") ||
      content.includes("store") ||
      content.includes("product") ||
      content.includes("buy") ||
      content.includes("cart") ||
      content.includes("checkout") ||
      data.links.some(
        (link) => link.includes("/product") || link.includes("/shop")
      )
    ) {
      return "E-commerce";
    }

    // Check for blog indicators
    if (
      content.includes("blog") ||
      content.includes("article") ||
      content.includes("post") ||
      content.includes("author") ||
      data.links.some(
        (link) => link.includes("/blog") || link.includes("/article")
      )
    ) {
      return "Blog";
    }

    // Check for SaaS indicators
    if (
      content.includes("software") ||
      content.includes("platform") ||
      content.includes("solution") ||
      content.includes("cloud") ||
      content.includes("subscription") ||
      content.includes("login") ||
      content.includes("sign up") ||
      content.includes("pricing")
    ) {
      return "SaaS";
    }

    // Check for corporate indicators
    if (
      content.includes("company") ||
      content.includes("business") ||
      content.includes("enterprise") ||
      content.includes("corporate") ||
      content.includes("industry") ||
      content.includes("professional")
    ) {
      return "Corporate";
    }

    // Check for portfolio indicators
    if (
      content.includes("portfolio") ||
      content.includes("work") ||
      content.includes("project") ||
      content.includes("showcase") ||
      content.includes("gallery")
    ) {
      return "Portfolio";
    }

    // Check for news indicators
    if (
      content.includes("news") ||
      content.includes("latest") ||
      content.includes("update") ||
      content.includes("press") ||
      content.includes("media")
    ) {
      return "News";
    }

    // Check for forum indicators
    if (
      content.includes("forum") ||
      content.includes("community") ||
      content.includes("discussion") ||
      content.includes("member") ||
      content.includes("topic") ||
      content.includes("thread")
    ) {
      return "Forum";
    }

    // Default to Services
    return "Services";
  }

  /**
   * Simple string hash function
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Delay execution for a specified time
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Calculate sentiment score for content
   * Returns a score from -1 (very negative) to 1 (very positive)
   */
  private calculateSentiment(content: string): number {
    try {
      // Use the content analyzer's sentiment analyzer
      const tokens = content.toLowerCase().split(/\s+/).slice(0, 1000); // Limit to first 1000 words

      // Create a simple sentiment analyzer
      const positiveWords = [
        "good",
        "great",
        "excellent",
        "amazing",
        "wonderful",
        "fantastic",
        "terrific",
        "outstanding",
        "superb",
        "brilliant",
        "awesome",
        "best",
        "better",
        "positive",
        "perfect",
        "happy",
        "pleased",
        "delighted",
        "satisfied",
        "love",
        "like",
        "enjoy",
        "recommend",
        "recommended",
        "helpful",
        "useful",
        "valuable",
        "beneficial",
        "success",
        "successful",
        "innovative",
        "leading",
        "trusted",
        "reliable",
        "quality",
        "premium",
        "professional",
        "expert",
        "advanced",
        "easy",
        "simple",
        "effective",
        "efficient",
        "powerful",
        "secure",
        "safe",
        "proven",
        "popular",
      ];

      const negativeWords = [
        "bad",
        "poor",
        "terrible",
        "awful",
        "horrible",
        "disappointing",
        "worst",
        "negative",
        "difficult",
        "hard",
        "complicated",
        "complex",
        "confusing",
        "problem",
        "issue",
        "bug",
        "error",
        "failure",
        "fail",
        "failed",
        "failing",
        "broken",
        "unreliable",
        "unstable",
        "slow",
        "expensive",
        "overpriced",
        "outdated",
        "obsolete",
        "old",
        "basic",
        "limited",
        "restricted",
        "lacking",
        "missing",
        "inadequate",
        "insufficient",
        "mediocre",
        "average",
        "ordinary",
        "frustrating",
        "annoying",
        "irritating",
        "useless",
        "worthless",
        "waste",
      ];

      let positiveCount = 0;
      let negativeCount = 0;

      // Count positive and negative words
      tokens.forEach((token) => {
        if (positiveWords.includes(token)) {
          positiveCount++;
        } else if (negativeWords.includes(token)) {
          negativeCount++;
        }
      });

      // Calculate sentiment score
      const totalSentimentWords = positiveCount + negativeCount;
      if (totalSentimentWords === 0) {
        return 0; // Neutral
      }

      return (positiveCount - negativeCount) / totalSentimentWords;
    } catch (error) {
      this.logger.error(
        `Error calculating sentiment: ${error.message}`,
        error.stack
      );
      return 0; // Default to neutral
    }
  }

  /**
   * Extract top topics from content and keywords
   */
  private extractTopTopics(content: string, keywords: string[]): string[] {
    try {
      // Combine content and keywords
      const allText = content + " " + keywords.join(" ");

      // Tokenize and clean
      const tokens = allText
        .toLowerCase()
        .split(/\s+/)
        .filter((token) => token.length > 3) // Filter out short words
        .filter((token) => !this.isStopWord(token)); // Filter out stop words

      // Count token frequencies
      const tokenCounts: Record<string, number> = {};
      tokens.forEach((token) => {
        tokenCounts[token] = (tokenCounts[token] || 0) + 1;
      });

      // Sort by frequency
      const sortedTokens = Object.entries(tokenCounts)
        .sort((a, b) => b[1] - a[1])
        .map((entry) => entry[0]);

      // Return top 10 topics
      return sortedTokens.slice(0, 10);
    } catch (error) {
      this.logger.error(
        `Error extracting topics: ${error.message}`,
        error.stack
      );
      return [];
    }
  }

  /**
   * Check if a word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      "a",
      "about",
      "above",
      "after",
      "again",
      "against",
      "all",
      "am",
      "an",
      "and",
      "any",
      "are",
      "as",
      "at",
      "be",
      "because",
      "been",
      "before",
      "being",
      "below",
      "between",
      "both",
      "but",
      "by",
      "can",
      "did",
      "do",
      "does",
      "doing",
      "down",
      "during",
      "each",
      "few",
      "for",
      "from",
      "further",
      "had",
      "has",
      "have",
      "having",
      "he",
      "her",
      "here",
      "hers",
      "herself",
      "him",
      "himself",
      "his",
      "how",
      "i",
      "if",
      "in",
      "into",
      "is",
      "it",
      "its",
      "itself",
      "just",
      "me",
      "more",
      "most",
      "my",
      "myself",
      "no",
      "nor",
      "not",
      "now",
      "of",
      "off",
      "on",
      "once",
      "only",
      "or",
      "other",
      "our",
      "ours",
      "ourselves",
      "out",
      "over",
      "own",
      "s",
      "same",
      "she",
      "should",
      "so",
      "some",
      "such",
      "t",
      "than",
      "that",
      "the",
      "their",
      "theirs",
      "them",
      "themselves",
      "then",
      "there",
      "these",
      "they",
      "this",
      "those",
      "through",
      "to",
      "too",
      "under",
      "until",
      "up",
      "very",
      "was",
      "we",
      "were",
      "what",
      "when",
      "where",
      "which",
      "while",
      "who",
      "whom",
      "why",
      "will",
      "with",
      "you",
      "your",
      "yours",
      "yourself",
      "yourselves",
    ]);

    return stopWords.has(word);
  }
}
