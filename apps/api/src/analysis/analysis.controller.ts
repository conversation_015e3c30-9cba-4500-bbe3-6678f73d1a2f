import {
  Controller,
  Get,
  Inject,
  Param,
  Post,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { AnalysisService } from "./analysis.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { GetUser } from "../auth/decorators/get-user.decorator";
import type { User } from "../users/entities/user.entity";
import type { AnalysisResultDto } from "shared-types";

@ApiTags("analysis")
@Controller("analysis")
@ApiBearerAuth("JWT-auth")
export class AnalysisController {
  constructor(
    @Inject(AnalysisService) private readonly analysisService: AnalysisService
  ) {}

  @ApiOperation({ summary: "Get all analyses for the current user" })
  @ApiResponse({ status: 200, description: "Returns all analyses" })
  @Get()
  findAll(@GetUser() user: User): Promise<AnalysisResultDto[]> {
    return this.analysisService.findAll(user.id);
  }

  @ApiOperation({ summary: "Get an analysis by ID" })
  @ApiResponse({ status: 200, description: "Returns the analysis" })
  @ApiResponse({ status: 404, description: "Analysis not found" })
  findOne(
    @Param("id") id: string,
    @GetUser() user: User
  ): Promise<AnalysisResultDto> {
    return this.analysisService.findOne(id, user.id);
  }

  @Post(":targetDomainId/analyze")
  @ApiOperation({ summary: "Analyze a target domain" })
  @ApiResponse({ status: 200, description: "Domain analyzed successfully" })
  @ApiResponse({ status: 404, description: "Target domain not found" })
  async analyzeTargetDomain(
    @Param("targetDomainId") targetDomainId: string,
    @GetUser() user: User
  ): Promise<{ success: boolean }> {
    await this.analysisService.analyzeTargetDomain(targetDomainId, user.id);
    return { success: true };
  }
}
