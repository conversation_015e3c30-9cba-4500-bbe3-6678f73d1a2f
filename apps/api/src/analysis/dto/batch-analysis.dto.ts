import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { 
  IsString, 
  IsArray, 
  IsOptional, 
  IsNumber, 
  IsBoolean,
  IsUUID,
  IsDate,
  Min, 
  Max, 
  ArrayMinSize,
  ArrayMaxSize,
  ValidateNested,
  IsNotEmpty,
  Matches,
  Length
} from "class-validator";
import { Type } from "class-transformer";

export class BatchAnalysisRequestDto {
  @ApiProperty({ 
    description: "Array of domain names to analyze",
    example: ["example.com", "test.org", "sample.net"],
    minItems: 1,
    maxItems: 100
  })
  @IsArray()
  @ArrayMinSize(1, { message: "At least one domain is required" })
  @ArrayMaxSize(100, { message: "Maximum 100 domains allowed per batch" })
  @IsString({ each: true })
  @Matches(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/, {
    each: true,
    message: "Each domain must be a valid domain name"
  })
  domains: string[];

  @ApiPropertyOptional({ 
    description: "User's domain for comparison",
    example: "mydomain.com"
  })
  @IsOptional()
  @IsString()
  @Matches(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/, {
    message: "User domain must be a valid domain name"
  })
  userDomain?: string;

  @ApiPropertyOptional({ 
    description: "Include contact information extraction",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeContactInfo?: boolean;

  @ApiPropertyOptional({ 
    description: "Include keyword analysis",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeKeywords?: boolean;

  @ApiPropertyOptional({ 
    description: "Include traffic data analysis",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeTrafficData?: boolean;

  @ApiPropertyOptional({ 
    description: "Maximum number of concurrent analyses",
    minimum: 1,
    maximum: 10,
    default: 5
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxConcurrency?: number;

  @ApiPropertyOptional({ 
    description: "Custom batch identifier",
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  batchId?: string;
}

export class BatchAnalysisItemDto {
  @ApiProperty({ 
    description: "Domain name",
    example: "example.com"
  })
  @IsString()
  @IsNotEmpty()
  domain: string;

  @ApiProperty({ 
    description: "Analysis status",
    enum: ["pending", "processing", "completed", "failed"]
  })
  @IsString()
  status: "pending" | "processing" | "completed" | "failed";

  @ApiPropertyOptional({ 
    description: "Relevance score if analysis completed",
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  score?: number;

  @ApiPropertyOptional({ 
    description: "Error message if analysis failed"
  })
  @IsOptional()
  @IsString()
  error?: string;

  @ApiPropertyOptional({ 
    description: "Processing time in milliseconds"
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  processingTimeMs?: number;

  @ApiPropertyOptional({ 
    description: "Analysis completion timestamp"
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  completedAt?: Date;
}

export class BatchAnalysisResultDto {
  @ApiProperty({ 
    description: "Unique batch identifier",
    example: "batch_123e4567-e89b-12d3-a456-************"
  })
  @IsString()
  @IsUUID(4)
  batchId: string;

  @ApiProperty({ 
    description: "Total number of domains in batch",
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  totalDomains: number;

  @ApiProperty({ 
    description: "Number of completed analyses",
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  completedDomains: number;

  @ApiProperty({ 
    description: "Number of failed analyses",
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  failedDomains: number;

  @ApiProperty({ 
    description: "Number of pending analyses",
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  pendingDomains: number;

  @ApiProperty({ 
    description: "Batch processing status",
    enum: ["pending", "processing", "completed", "failed", "partial"]
  })
  @IsString()
  status: "pending" | "processing" | "completed" | "failed" | "partial";

  @ApiProperty({ 
    description: "Individual domain analysis results",
    type: [BatchAnalysisItemDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchAnalysisItemDto)
  results: BatchAnalysisItemDto[];

  @ApiProperty({ 
    description: "Batch creation timestamp"
  })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiPropertyOptional({ 
    description: "Batch completion timestamp"
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  completedAt?: Date;

  @ApiPropertyOptional({ 
    description: "Total processing time in milliseconds"
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalProcessingTimeMs?: number;

  @ApiPropertyOptional({ 
    description: "Average score of completed analyses",
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  averageScore?: number;

  @ApiPropertyOptional({ 
    description: "Top scoring domains",
    type: [Object]
  })
  @IsOptional()
  @IsArray()
  topDomains?: Array<{
    domain: string;
    score: number;
  }>;

  @ApiPropertyOptional({ 
    description: "Progress percentage (0-100)",
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(100)
  progress?: number;
}

export class BatchAnalysisStatusDto {
  @ApiProperty({ 
    description: "Batch identifier"
  })
  @IsString()
  @IsUUID(4)
  batchId: string;

  @ApiProperty({ 
    description: "Current status",
    enum: ["pending", "processing", "completed", "failed", "partial"]
  })
  @IsString()
  status: "pending" | "processing" | "completed" | "failed" | "partial";

  @ApiProperty({ 
    description: "Progress percentage",
    minimum: 0,
    maximum: 100
  })
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(100)
  progress: number;

  @ApiProperty({ 
    description: "Estimated time remaining in seconds"
  })
  @IsNumber()
  @Min(0)
  estimatedTimeRemaining: number;

  @ApiProperty({ 
    description: "Number of completed domains"
  })
  @IsNumber()
  @Min(0)
  completedCount: number;

  @ApiProperty({ 
    description: "Total number of domains"
  })
  @IsNumber()
  @Min(1)
  totalCount: number;

  @ApiPropertyOptional({ 
    description: "Current processing domain"
  })
  @IsOptional()
  @IsString()
  currentDomain?: string;

  @ApiPropertyOptional({ 
    description: "Error message if batch failed"
  })
  @IsOptional()
  @IsString()
  error?: string;
}
