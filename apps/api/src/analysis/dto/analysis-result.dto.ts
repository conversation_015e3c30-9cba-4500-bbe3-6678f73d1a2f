import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsN<PERSON>ber,
  IsOptional,
  IsUUID,
  IsDate,
  Min,
  Max,
  IsIn,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  Matches,
  Length,
  IsObject,
} from "class-validator";
import { Type } from "class-transformer";

export class ScoreBreakdownDto {
  @ApiProperty({
    description: "Keyword relevance score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  keywordRelevance: number;

  @ApiProperty({
    description: "Industry match score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  industryMatch: number;

  @ApiProperty({
    description: "Traffic quality score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  trafficQuality: number;

  @ApiProperty({
    description: "Domain authority score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  domainAuthority: number;

  @ApiProperty({
    description: "Language match score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  languageMatch: number;

  @ApiProperty({
    description: "Website age score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  websiteAge: number;

  @ApiProperty({
    description: "Backlinks score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  backlinks: number;

  @ApiProperty({
    description: "Category match score (0-100)",
    minimum: 0,
    maximum: 100,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  categoryMatch: number;
}

export class AnalysisResultDto {
  @ApiProperty({
    description: "Unique identifier for the analysis",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsString()
  @IsUUID(4)
  id: string;

  @ApiProperty({
    description: "Domain name that was analyzed",
    example: "example.com",
    pattern: "^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$",
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/, {
    message: "Domain must be a valid domain name",
  })
  domain: string;

  @ApiPropertyOptional({
    description: "Title of the domain",
    example: "Example Company - Leading Solutions Provider",
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  title?: string;

  @ApiPropertyOptional({
    description: "Description of the domain",
    example: "A comprehensive description of the company and its services",
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @Length(1, 500)
  description?: string;

  @ApiProperty({
    description: "Relevance score (0-100) compared to user's domain",
    minimum: 0,
    maximum: 100,
    example: 85.5,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  relevanceScore: number;

  @ApiPropertyOptional({
    description: "Domain authority score (0-100)",
    minimum: 0,
    maximum: 100,
    example: 65,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  domainAuthority?: number;

  @ApiPropertyOptional({
    description: "Number of backlinks",
    minimum: 0,
    example: 1250,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  backlinks?: number;

  @ApiPropertyOptional({
    description: "Monthly traffic estimate",
    minimum: 0,
    example: 50000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  traffic?: number;

  @ApiPropertyOptional({
    description: "Traffic trend (upward/downward/stable)",
    enum: ["upward", "downward", "stable"],
    example: "upward",
  })
  @IsOptional()
  @IsIn(["upward", "downward", "stable"])
  trafficTrend?: "upward" | "downward" | "stable";

  @ApiPropertyOptional({
    description: "Top ranking keywords",
    type: [String],
    example: ["business software", "enterprise solutions", "cloud services"],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Length(1, 100, { each: true })
  topKeywords?: string[];

  @ApiPropertyOptional({
    description: "Primary industry category",
    example: "Technology",
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  industry?: string;

  @ApiPropertyOptional({
    description: "Website category (Blog, E-commerce, etc.)",
    example: "SaaS",
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  category?: string;

  @ApiPropertyOptional({
    description: "Primary language of the website",
    example: "en",
    pattern: "^[a-z]{2}(-[A-Z]{2})?$",
  })
  @IsOptional()
  @IsString()
  @Matches(/^[a-z]{2}(-[A-Z]{2})?$/, {
    message:
      "Language must be a valid ISO 639-1 language code (e.g., 'en', 'en-US')",
  })
  language?: string;

  @ApiPropertyOptional({
    description: "Age of website in months",
    minimum: 0,
    example: 36,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  websiteAge?: number;

  @ApiPropertyOptional({
    description: "Top country for traffic",
    example: "US",
    pattern: "^[A-Z]{2}$",
  })
  @IsOptional()
  @IsString()
  @Matches(/^[A-Z]{2}$/, {
    message: "Country must be a valid ISO 3166-1 alpha-2 country code",
  })
  topCountry?: string;

  @ApiPropertyOptional({
    description: "Detailed scoring breakdown",
    type: ScoreBreakdownDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ScoreBreakdownDto)
  @IsObject()
  scoreBreakdown?: ScoreBreakdownDto;

  @ApiPropertyOptional({
    description: "Timestamp when analysis was performed",
    example: "2024-01-15T10:30:00Z",
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  analyzedAt?: Date;
}
