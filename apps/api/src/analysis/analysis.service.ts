import {
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
  Logger,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { DomainAnalysis } from "./entities/domain-analysis.entity";
import { DomainsService } from "../domains/domains.service";
import { TargetDomainsService } from "../target-domains/target-domains.service";
import type { AnalysisResultDto } from "shared-types";
import { DomainAnalyzerService } from "./services/domain-analyzer.service";
import { DomainAnalysisData } from "../types";

@Injectable()
export class AnalysisService {
  private readonly logger = new Logger(AnalysisService.name);

  constructor(
    @InjectRepository(DomainAnalysis)
    private analysisRepository: Repository<DomainAnalysis>,
    @Inject(DomainsService)
    private domainsService: DomainsService,
    @Inject(forwardRef(() => TargetDomainsService))
    private targetDomainsService: TargetDomainsService,
    @Inject(DomainAnalyzerService)
    private domainAnalyzerService: DomainAnalyzerService
  ) {}

  async findAll(userId: string): Promise<AnalysisResultDto[]> {
    const analyses = await this.analysisRepository
      .createQueryBuilder("analysis")
      .innerJoinAndSelect("analysis.targetDomain", "targetDomain")
      .innerJoinAndSelect("analysis.userDomain", "userDomain")
      .where("targetDomain.userId = :userId", { userId })
      .getMany();

    return analyses.map((analysis) => ({
      id: analysis.id,
      domain: analysis.targetDomain.domain,
      title: analysis.targetDomain.title || "",
      description: analysis.targetDomain.description || "",
      relevanceScore: analysis.relevanceScore,
      domainAuthority: analysis.domainAuthority || 0,
      backlinks: analysis.backlinks || 0,
      traffic: analysis.traffic || 0,
      trafficTrend: analysis.trafficTrend || "stable",
      topKeywords: analysis.topKeywords || [],
      topPages: analysis.topPages || [],
      industry: analysis.industry || "Unknown",
      category: analysis.category || "Unknown",
      topCountry: analysis.topCountry || "Unknown",
      websiteAge: analysis.websiteAge || 0,
      language: analysis.language || "en",
      contactEmail: analysis.contactEmail || undefined,
      contactPage: analysis.contactPage || undefined,
      categories: analysis.categories || [],
      keywords: analysis.keywords || [],
      lastUpdated: analysis.lastUpdated || new Date(),
    }));
  }

  async findOne(id: string, userId: string): Promise<AnalysisResultDto> {
    const analysis = await this.analysisRepository
      .createQueryBuilder("analysis")
      .innerJoinAndSelect("analysis.targetDomain", "targetDomain")
      .innerJoinAndSelect("analysis.userDomain", "userDomain")
      .where("analysis.id = :id", { id })
      .andWhere("targetDomain.userId = :userId", { userId })
      .getOne();

    if (!analysis) {
      throw new NotFoundException(`Analysis with ID ${id} not found`);
    }

    return {
      id: analysis.id,
      domain: analysis.targetDomain.domain,
      title: analysis.targetDomain.title || "",
      description: analysis.targetDomain.description || "",
      relevanceScore: analysis.relevanceScore,
      domainAuthority: analysis.domainAuthority || 0,
      backlinks: analysis.backlinks || 0,
      traffic: analysis.traffic || 0,
      trafficTrend: analysis.trafficTrend || "stable",
      topKeywords: analysis.topKeywords || [],
      topPages: analysis.topPages || [],
      industry: analysis.industry || "Unknown",
      category: analysis.category || "Unknown",
      topCountry: analysis.topCountry || "Unknown",
      websiteAge: analysis.websiteAge || 0,
      language: analysis.language || "en",
      contactEmail: analysis.contactEmail || undefined,
      contactPage: analysis.contactPage || undefined,
      categories: analysis.categories || [],
      keywords: analysis.keywords || [],
      lastUpdated: analysis.lastUpdated || new Date(),
    };
  }

  async analyzeTargetDomain(
    targetDomainId: string,
    userId: string
  ): Promise<void> {
    this.logger.log(
      `Analyzing target domain ${targetDomainId} for user ${userId}`
    );

    const targetDomain = await this.targetDomainsService.findOne(
      targetDomainId,
      userId
    );

    const userDomains = await this.domainsService.findAll(userId);
    if (!userDomains.length || !userDomains[0].verified) {
      this.logger.warn(
        `Cannot analyze without a verified user domain for user ${userId}`
      );
      throw new Error("Cannot analyze without a verified user domain");
    }

    const userDomain = userDomains[0];

    try {
      // Use the domain analyzer service to analyze the target domain
      const analysisResult = await this.domainAnalyzerService.analyzeDomain(
        targetDomain.domain,
        userDomain.domain
      );

      this.logger.log(
        `Analysis completed for domain ${targetDomain.domain} with relevance score ${analysisResult.relevanceScore}`
      );

      // Check if there's an existing analysis
      const existingAnalysis = await this.analysisRepository.findOne({
        where: {
          targetDomainId,
          userDomainId: userDomain.id,
        },
      });

      // Prepare analysis data
      const analysisData: DomainAnalysisData = {
        relevanceScore: analysisResult.relevanceScore,
        domainAuthority: analysisResult.domainAuthority || 0,
        backlinks: analysisResult.backlinks || 0,
        traffic: analysisResult.traffic || 0,
        trafficTrend: analysisResult.trafficTrend || "stable",
        topKeywords: analysisResult.topKeywords || [],
        topPages: analysisResult.topPages || [],
        industry: analysisResult.industry || "Unknown",
        category: analysisResult.category || "Unknown",
        topCountry: analysisResult.topCountry || "Unknown",
        websiteAge: analysisResult.websiteAge || 0,
        language: analysisResult.language || "en",
        contactEmail: analysisResult.contactEmail,
        contactPage: analysisResult.contactPage,
        categories: analysisResult.keywords || [], // Use keywords as categories
        keywords: analysisResult.keywords || [],
        lastUpdated: new Date(),
      };

      // Save the analysis data
      if (existingAnalysis) {
        this.logger.log(
          `Updating existing analysis for domain ${targetDomain.domain}`
        );
        Object.assign(existingAnalysis, analysisData);
        await this.analysisRepository.save(existingAnalysis);
      } else {
        this.logger.log(
          `Creating new analysis for domain ${targetDomain.domain}`
        );

        // Create a new analysis with proper type handling
        const analysis = new DomainAnalysis();
        analysis.targetDomainId = targetDomainId;
        analysis.userDomainId = userDomain.id;
        analysis.relevanceScore = analysisData.relevanceScore;
        analysis.domainAuthority = analysisData.domainAuthority;
        analysis.backlinks = analysisData.backlinks;
        analysis.traffic = analysisData.traffic;
        analysis.trafficTrend = analysisData.trafficTrend;
        analysis.topKeywords = analysisData.topKeywords;
        analysis.topPages = analysisData.topPages;
        analysis.industry = analysisData.industry;
        analysis.category = analysisData.category;
        analysis.topCountry = analysisData.topCountry;
        analysis.websiteAge = analysisData.websiteAge;
        analysis.language = analysisData.language;
        analysis.contactEmail =
          analysisData.contactEmail === null
            ? undefined
            : analysisData.contactEmail;
        analysis.contactPage =
          analysisData.contactPage === null
            ? undefined
            : analysisData.contactPage;
        analysis.categories = analysisData.categories;
        analysis.keywords = analysisData.keywords;
        analysis.lastUpdated = analysisData.lastUpdated;

        await this.analysisRepository.save(analysis);
      }

      // Update the target domain with analysis data
      await this.targetDomainsService.update(targetDomainId, userId, {
        title: analysisResult.title,
        description: analysisResult.description,
        relevanceScore: analysisData.relevanceScore,
        domainAuthority: analysisData.domainAuthority,
        backlinks: analysisData.backlinks,
        traffic: analysisData.traffic,
        contactEmail:
          analysisData.contactEmail === null
            ? undefined
            : analysisData.contactEmail,
        contactPage:
          analysisData.contactPage === null
            ? undefined
            : analysisData.contactPage,
        categories: analysisData.categories,
        keywords: analysisData.keywords,
        lastUpdated: analysisData.lastUpdated,
      });

      this.logger.log(`Analysis data saved for domain ${targetDomain.domain}`);
    } catch (error) {
      this.logger.error(
        `Error analyzing domain ${targetDomain.domain}: ${error.message}`,
        error.stack
      );
      throw new Error(`Failed to analyze domain: ${error.message}`);
    }
  }

  // Removed unused fetchDomainData method

  // Removed unused methods

  private async fetchWebsiteContent(domain: string): Promise<{
    categories: string[];
    keywords: string[];
    industry: string;
    category: string;
    language: string;
  }> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 400));

      const domainName = domain.split(".")[0].toLowerCase();

      const allIndustries = [
        "Technology",
        "Marketing",
        "Finance",
        "Healthcare",
        "Education",
        "Entertainment",
        "Travel",
        "Food",
        "Fashion",
        "Sports",
        "Business",
        "Science",
        "Art",
        "News",
        "Real Estate",
      ];

      const allCategories = [
        "SaaS",
        "Blog",
        "E-commerce",
        "Services",
        "Portfolio",
        "Corporate",
        "News",
        "Forum",
        "Social Network",
        "Educational",
      ];

      const keywordsByIndustry: Record<string, string[]> = {
        Technology: [
          "software",
          "development",
          "programming",
          "tech",
          "innovation",
          "digital",
          "code",
          "app",
        ],
        Marketing: [
          "marketing",
          "advertising",
          "branding",
          "seo",
          "content",
          "social media",
          "strategy",
        ],
        Finance: [
          "finance",
          "investment",
          "banking",
          "money",
          "wealth",
          "trading",
          "stocks",
        ],
        Healthcare: [
          "health",
          "wellness",
          "fitness",
          "medical",
          "nutrition",
          "healthcare",
          "diet",
        ],
        Education: [
          "education",
          "learning",
          "teaching",
          "school",
          "university",
          "course",
          "training",
        ],
        Business: [
          "business",
          "entrepreneur",
          "startup",
          "company",
          "management",
          "leadership",
        ],
        Travel: [
          "travel",
          "vacation",
          "tourism",
          "destination",
          "hotel",
          "flight",
          "adventure",
        ],
      };

      let industry = "Unknown";
      let selectedCategories: string[] = [];

      if (
        domainName.includes("tech") ||
        domainName.includes("code") ||
        domainName.includes("dev")
      ) {
        industry = "Technology";
        selectedCategories.push("Technology");
      } else if (
        domainName.includes("market") ||
        domainName.includes("seo") ||
        domainName.includes("content")
      ) {
        industry = "Marketing";
        selectedCategories.push("Marketing");
      } else if (
        domainName.includes("edu") ||
        domainName.includes("learn") ||
        domainName.includes("school")
      ) {
        industry = "Education";
        selectedCategories.push("Education");
      } else if (
        domainName.includes("health") ||
        domainName.includes("fit") ||
        domainName.includes("med")
      ) {
        industry = "Healthcare";
        selectedCategories.push("Health");
      } else if (
        domainName.includes("travel") ||
        domainName.includes("tour") ||
        domainName.includes("trip")
      ) {
        industry = "Travel";
        selectedCategories.push("Travel");
      } else if (
        domainName.includes("finance") ||
        domainName.includes("bank") ||
        domainName.includes("money")
      ) {
        industry = "Finance";
        selectedCategories.push("Finance");
      } else {
        industry =
          allIndustries[Math.floor(Math.random() * allIndustries.length)];
        selectedCategories.push(industry);
      }

      let category = "Unknown";
      if (domainName.includes("blog")) {
        category = "Blog";
      } else if (domainName.includes("shop") || domainName.includes("store")) {
        category = "E-commerce";
      } else if (
        domainName.includes("app") ||
        domainName.includes("software")
      ) {
        category = "SaaS";
      } else if (domainName.includes("service")) {
        category = "Services";
      } else if (domainName.includes("news")) {
        category = "News";
      } else if (
        domainName.includes("forum") ||
        domainName.includes("community")
      ) {
        category = "Forum";
      } else {
        category =
          allCategories[Math.floor(Math.random() * allCategories.length)];
      }

      if (selectedCategories.length === 0) {
        const numCategories = Math.floor(Math.random() * 2) + 1;
        for (let i = 0; i < numCategories; i++) {
          const randomCategory =
            allIndustries[Math.floor(Math.random() * allIndustries.length)];
          if (!selectedCategories.includes(randomCategory)) {
            selectedCategories.push(randomCategory);
          }
        }
      }

      let keywords: string[] = [];
      const industryKeywords =
        keywordsByIndustry[industry as keyof typeof keywordsByIndustry];
      if (industryKeywords) {
        const numKeywords = Math.floor(Math.random() * 3) + 3;
        for (let i = 0; i < numKeywords && i < industryKeywords.length; i++) {
          const randomKeyword =
            industryKeywords[
              Math.floor(Math.random() * industryKeywords.length)
            ];
          if (!keywords.includes(randomKeyword)) {
            keywords.push(randomKeyword);
          }
        }
      }

      if (domainName.length > 3 && !keywords.includes(domainName)) {
        keywords.push(domainName);
      }

      const languages = ["en", "es", "fr", "de", "it", "pt", "ja", "zh", "ru"];
      const languageWeights = [
        0.7, 0.05, 0.05, 0.05, 0.03, 0.03, 0.03, 0.03, 0.03,
      ];

      let language = "en";
      const randomValue = Math.random();
      let cumulativeWeight = 0;

      for (let i = 0; i < languages.length; i++) {
        cumulativeWeight += languageWeights[i];
        if (randomValue <= cumulativeWeight) {
          language = languages[i];
          break;
        }
      }

      const tld = domain.split(".").pop();
      if (tld === "fr") language = "fr";
      else if (tld === "de") language = "de";
      else if (tld === "es") language = "es";
      else if (tld === "it") language = "it";
      else if (tld === "jp") language = "ja";
      else if (tld === "cn") language = "zh";
      else if (tld === "ru") language = "ru";

      return {
        categories: selectedCategories,
        keywords,
        industry,
        category,
        language,
      };
    } catch (error) {
      console.error("Error fetching website content:", error);
      return {
        categories: [],
        keywords: [],
        industry: "Unknown",
        category: "Unknown",
        language: "en",
      };
    }
  }

  private async fetchSEOData(domain: string): Promise<{
    topKeywords: string[];
    topPages: string[];
  }> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 250));

      const domainName = domain.split(".")[0].toLowerCase();

      const topKeywords: string[] = [];

      topKeywords.push(domainName);

      if (domainName.length > 5) {
        topKeywords.push(`best ${domainName}`);
        topKeywords.push(`${domainName} services`);
        topKeywords.push(`${domainName} review`);
      }

      if (domainName.includes("tech")) {
        topKeywords.push("technology solutions");
        topKeywords.push("tech services");
        topKeywords.push("software development");
      } else if (domainName.includes("health")) {
        topKeywords.push("health services");
        topKeywords.push("wellness tips");
        topKeywords.push("medical advice");
      } else if (domainName.includes("finance")) {
        topKeywords.push("financial advice");
        topKeywords.push("investment tips");
        topKeywords.push("money management");
      } else {
        const genericKeywords = [
          "services",
          "solutions",
          "guide",
          "tips",
          "best practices",
          "how to",
          "tutorial",
          "review",
          "comparison",
          "alternatives",
        ];

        for (let i = 0; i < 3; i++) {
          const randomKeyword =
            genericKeywords[Math.floor(Math.random() * genericKeywords.length)];
          if (!topKeywords.includes(randomKeyword)) {
            topKeywords.push(randomKeyword);
          }
        }
      }

      const topPages = [`/`, `/about`, `/services`, `/blog`, `/contact`];

      if (domainName.includes("blog") || Math.random() > 0.5) {
        topPages.push(`/blog/top-10-${domainName}-tips`);
        topPages.push(`/blog/why-${domainName}-matters`);
      }

      if (domainName.includes("shop") || domainName.includes("store")) {
        topPages.push(`/products`);
        topPages.push(`/products/best-seller`);
      }

      return {
        topKeywords: topKeywords.slice(0, 5),
        topPages,
      };
    } catch (error) {
      console.error("Error fetching SEO data:", error);
      return {
        topKeywords: [],
        topPages: [],
      };
    }
  }

  private async fetchDomainAgeData(domain: string): Promise<{
    ageInMonths: number;
  }> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 150));

      const domainParts = domain.split(".");
      const baseDomain = domainParts[0];
      const tld = domainParts[domainParts.length - 1];

      let baseAge = 24; // 2 years as base

      if (baseDomain.length <= 5) {
        baseAge += 60; // Add 5 years
      } else if (baseDomain.length <= 8) {
        baseAge += 36; // Add 3 years
      }

      if (["com", "org", "net"].includes(tld)) {
        baseAge += 24; // Add 2 years
      }

      const randomFactor = Math.floor(Math.random() * 24); // +/- 2 years
      const ageInMonths = Math.max(1, baseAge + randomFactor - 12);

      return { ageInMonths };
    } catch (error) {
      console.error("Error fetching domain age data:", error);
      return { ageInMonths: 12 }; // Default to 1 year
    }
  }

  private async fetchContactInfo(
    domain: string
  ): Promise<{ email: string | null; contactPage: string | null }> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 150));

      const hasContactPage = Math.random() > 0.2; // 80% chance of having a contact page
      const hasEmail = Math.random() > 0.4; // 60% chance of finding an email

      const contactPage = hasContactPage ? `https://${domain}/contact` : null;
      const email = hasEmail ? `contact@${domain}` : null;

      return { email, contactPage };
    } catch (error) {
      console.error("Error fetching contact info:", error);
      return { email: null, contactPage: null };
    }
  }

  private async calculateComprehensiveScore(
    userDomain: {
      domain: string;
      keywords?: string[];
      industry?: string;
      category?: string;
      language?: string;
    },
    targetDomainData: {
      domain?: string;
      keywords?: string[];
      industry?: string;
      category?: string;
      language?: string;
      traffic?: number;
      trafficTrend?: "upward" | "downward" | "stable";
      topCountry?: string;
      domainAuthority?: number;
      websiteAge?: number;
      backlinks?: number;
    },
    targetDomainName: string
  ): Promise<number> {
    // Define weights for different factors
    const weights = {
      keywordRelevance: 0.25, // 25% - Content relevance
      industryMatch: 0.2, // 20% - Industry match
      trafficQuality: 0.15, // 15% - Traffic quality
      domainAuthority: 0.15, // 15% - Domain authority
      languageMatch: 0.1, // 10% - Language match
      websiteAge: 0.05, // 5% - Website age
      backlinks: 0.05, // 5% - Backlinks
      categoryMatch: 0.05, // 5% - Website category match
    };

    // 1. Calculate keyword relevance score
    const keywordRelevanceScore = await this.calculateKeywordRelevanceScore(
      userDomain.keywords || [],
      targetDomainData.keywords || [],
      userDomain.domain,
      targetDomainName
    );

    // 2. Calculate industry match score
    const industryMatchScore = this.calculateIndustryMatchScore(
      userDomain.industry || "Unknown",
      targetDomainData.industry || "Unknown"
    );

    // 3. Calculate traffic quality score
    const trafficQualityScore = this.calculateTrafficQualityScore(
      targetDomainData.traffic || 0,
      targetDomainData.trafficTrend || "stable",
      targetDomainData.topCountry || "Unknown"
    );

    // 4. Calculate domain authority score
    const domainAuthorityScore = this.calculateDomainAuthorityScore(
      targetDomainData.domainAuthority || 0
    );

    // 5. Calculate language match score
    const languageMatchScore = this.calculateLanguageMatchScore(
      userDomain.language || "en",
      targetDomainData.language || "en"
    );

    // 6. Calculate website age score
    const websiteAgeScore = this.calculateWebsiteAgeScore(
      targetDomainData.websiteAge || 0
    );

    // 7. Calculate backlinks score
    const backlinksScore = this.calculateBacklinksScore(
      targetDomainData.backlinks || 0
    );

    // 8. Calculate category match score
    const categoryMatchScore = this.calculateCategoryMatchScore(
      userDomain.category || "Unknown",
      targetDomainData.category || "Unknown"
    );

    // Calculate weighted average of all scores
    const weightedScore =
      keywordRelevanceScore * weights.keywordRelevance +
      industryMatchScore * weights.industryMatch +
      trafficQualityScore * weights.trafficQuality +
      domainAuthorityScore * weights.domainAuthority +
      languageMatchScore * weights.languageMatch +
      websiteAgeScore * weights.websiteAge +
      backlinksScore * weights.backlinks +
      categoryMatchScore * weights.categoryMatch;

    // Round to nearest integer and ensure it's between 0-100
    return Math.min(Math.max(Math.round(weightedScore), 0), 100);
  }

  private async calculateKeywordRelevanceScore(
    userKeywords: string[],
    targetKeywords: string[],
    userDomain: string,
    targetDomain: string
  ): Promise<number> {
    // If no keywords available, use more sophisticated analysis
    if (!userKeywords.length || !targetKeywords.length) {
      return await this.calculateContentSimilarity(userDomain, targetDomain);
    }

    // Calculate keyword match score
    const matchingKeywords = userKeywords.filter((keyword) =>
      targetKeywords.some(
        (targetKeyword) =>
          targetKeyword.toLowerCase().includes(keyword.toLowerCase()) ||
          keyword.toLowerCase().includes(targetKeyword.toLowerCase())
      )
    );

    // Calculate direct match percentage
    const directMatchScore =
      userKeywords.length > 0
        ? (matchingKeywords.length / userKeywords.length) * 100
        : 0;

    // Calculate semantic similarity for non-matching keywords
    const nonMatchingUserKeywords = userKeywords.filter(
      (keyword) => !matchingKeywords.includes(keyword)
    );

    let semanticScore = 0;
    if (nonMatchingUserKeywords.length > 0) {
      // In a real implementation, you would use a semantic similarity API or model
      // Here we'll simulate it with a simple heuristic
      semanticScore = await this.calculateSemanticSimilarity(
        nonMatchingUserKeywords,
        targetKeywords
      );
    }

    // Combine direct matches and semantic similarity
    // Weight direct matches more heavily (70%) than semantic matches (30%)
    const combinedScore = directMatchScore * 0.7 + semanticScore * 0.3;

    // Ensure score is between 0-100
    return Math.min(Math.max(Math.round(combinedScore), 0), 100);
  }

  private calculateIndustryMatchScore(
    userIndustry: string,
    targetIndustry: string
  ): number {
    // Define industry relationships (which industries are related)
    const relatedIndustries: Record<string, string[]> = {
      Technology: ["Business", "Education", "Science"],
      Marketing: ["Business", "Technology", "Entertainment"],
      Finance: ["Business", "Real Estate"],
      Healthcare: ["Science", "Education"],
      Education: ["Science", "Technology", "Healthcare"],
      Entertainment: ["Marketing", "Art", "Technology"],
      Travel: ["Entertainment", "Food"],
      Food: ["Travel", "Health"],
      Fashion: ["Entertainment", "Art"],
      Sports: ["Health", "Entertainment"],
      Business: ["Finance", "Technology", "Marketing"],
      Science: ["Technology", "Healthcare", "Education"],
      Art: ["Entertainment", "Fashion"],
      News: ["Entertainment", "Business", "Technology"],
      "Real Estate": ["Business", "Finance"],
    };

    // Exact match
    if (userIndustry === targetIndustry) {
      return 100;
    }

    // Related industry match
    if (
      relatedIndustries[userIndustry] &&
      relatedIndustries[userIndustry].includes(targetIndustry)
    ) {
      return 70; // 70% score for related industries
    }

    // Secondary relationship (2 degrees of separation)
    for (const relatedIndustry of relatedIndustries[userIndustry] || []) {
      if (
        relatedIndustries[relatedIndustry] &&
        relatedIndustries[relatedIndustry].includes(targetIndustry)
      ) {
        return 40; // 40% score for secondary relationship
      }
    }

    // No relationship
    return 10; // 10% base score for any industry
  }

  private calculateTrafficQualityScore(
    traffic: number,
    trafficTrend: string,
    topCountry: string
  ): number {
    // Base score from traffic volume (0-50 points)
    let trafficScore = 0;
    if (traffic > 1000000) trafficScore = 50;
    else if (traffic > 500000) trafficScore = 45;
    else if (traffic > 100000) trafficScore = 40;
    else if (traffic > 50000) trafficScore = 35;
    else if (traffic > 10000) trafficScore = 30;
    else if (traffic > 5000) trafficScore = 25;
    else if (traffic > 1000) trafficScore = 20;
    else if (traffic > 500) trafficScore = 15;
    else if (traffic > 100) trafficScore = 10;
    else if (traffic > 0) trafficScore = 5;

    // Traffic trend bonus (0-30 points)
    let trendScore = 0;
    if (trafficTrend === "upward") trendScore = 30;
    else if (trafficTrend === "stable") trendScore = 15;

    // Country relevance (0-20 points)
    // In a real implementation, you would compare with the user's target countries
    // For now, we'll give a bonus for major English-speaking markets
    const majorMarkets = [
      "United States",
      "United Kingdom",
      "Canada",
      "Australia",
    ];
    const countryScore = majorMarkets.includes(topCountry) ? 20 : 10;

    // Combine scores
    return trafficScore + trendScore + countryScore;
  }

  private calculateDomainAuthorityScore(domainAuthority: number): number {
    // Domain authority is already on a 0-100 scale
    return domainAuthority;
  }

  private calculateLanguageMatchScore(
    userLanguage: string,
    targetLanguage: string
  ): number {
    // Exact language match
    if (userLanguage === targetLanguage) {
      return 100;
    }

    // Define language families
    const languageFamilies: Record<string, string[]> = {
      en: ["en-US", "en-GB", "en-CA", "en-AU"],
      es: ["es-ES", "es-MX", "es-AR"],
      fr: ["fr-FR", "fr-CA", "fr-BE"],
      de: ["de-DE", "de-AT", "de-CH"],
      pt: ["pt-PT", "pt-BR"],
      zh: ["zh-CN", "zh-TW", "zh-HK"],
    };

    // Check if languages are in the same family
    const userBaseLanguage = userLanguage.split("-")[0];
    const targetBaseLanguage = targetLanguage.split("-")[0];

    if (userBaseLanguage === targetBaseLanguage) {
      return 90; // Same base language
    }

    // Check if target language is in user's language family
    if (
      languageFamilies[userBaseLanguage] &&
      languageFamilies[userBaseLanguage].includes(targetLanguage)
    ) {
      return 80;
    }

    // English is widely understood, so give a higher score if either language is English
    if (userBaseLanguage === "en" || targetBaseLanguage === "en") {
      return 60;
    }

    // Different languages
    return 30;
  }

  private calculateWebsiteAgeScore(ageInMonths: number): number {
    // Older websites tend to be more established and trustworthy
    if (ageInMonths >= 120) return 100; // 10+ years
    if (ageInMonths >= 60) return 90; // 5+ years
    if (ageInMonths >= 36) return 80; // 3+ years
    if (ageInMonths >= 24) return 70; // 2+ years
    if (ageInMonths >= 12) return 60; // 1+ year
    if (ageInMonths >= 6) return 50; // 6+ months
    if (ageInMonths >= 3) return 40; // 3+ months
    if (ageInMonths >= 1) return 30; // 1+ month
    return 20; // Less than 1 month
  }

  private calculateBacklinksScore(backlinks: number): number {
    // More backlinks generally indicate higher authority
    if (backlinks >= 10000) return 100;
    if (backlinks >= 5000) return 90;
    if (backlinks >= 1000) return 80;
    if (backlinks >= 500) return 70;
    if (backlinks >= 100) return 60;
    if (backlinks >= 50) return 50;
    if (backlinks >= 20) return 40;
    if (backlinks >= 10) return 30;
    if (backlinks >= 1) return 20;
    return 10;
  }

  private calculateCategoryMatchScore(
    userCategory: string,
    targetCategory: string
  ): number {
    // Define category relationships
    const relatedCategories: Record<string, string[]> = {
      SaaS: ["Corporate", "Services", "Technology"],
      Blog: ["News", "Educational"],
      "E-commerce": ["Services", "Corporate"],
      Services: ["SaaS", "Corporate", "E-commerce"],
      Portfolio: ["Corporate", "Services"],
      Corporate: ["Services", "SaaS"],
      News: ["Blog", "Educational"],
      Forum: ["Social Network", "Educational"],
      "Social Network": ["Forum", "Blog"],
      Educational: ["Blog", "News"],
    };

    // Exact match
    if (userCategory === targetCategory) {
      return 100;
    }

    // Related category match
    if (
      relatedCategories[userCategory] &&
      relatedCategories[userCategory].includes(targetCategory)
    ) {
      return 70;
    }

    // No direct relationship
    return 30;
  }

  private async calculateSemanticSimilarity(
    sourceKeywords: string[],
    targetKeywords: string[]
  ): Promise<number> {
    // In a real implementation, you would use:
    // 1. A word embedding model (Word2Vec, GloVe, etc.)
    // 2. An NLP API like OpenAI's text-embedding-ada or similar

    // For demo purposes, we'll use a simple heuristic based on word length and character overlap
    let totalSimilarity = 0;
    let comparisonCount = 0;

    for (const sourceKeyword of sourceKeywords) {
      let bestSimilarity = 0;

      for (const targetKeyword of targetKeywords) {
        // Calculate character overlap
        const sourceChars = new Set(sourceKeyword.toLowerCase());
        const targetChars = new Set(targetKeyword.toLowerCase());

        const intersection = new Set(
          [...sourceChars].filter((char) => targetChars.has(char))
        );
        const union = new Set([...sourceChars, ...targetChars]);

        const similarity = intersection.size / union.size;
        bestSimilarity = Math.max(bestSimilarity, similarity);
      }

      totalSimilarity += bestSimilarity;
      comparisonCount++;
    }

    // Convert to a 0-100 scale
    return comparisonCount > 0
      ? Math.round((totalSimilarity / comparisonCount) * 100)
      : 50; // Default to 50% if no comparisons were made
  }

  private async calculateContentSimilarity(
    userDomain: string,
    targetDomain: string
  ): Promise<number> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 200));

      const userDomainName = userDomain.split(".")[0];
      const targetDomainName = targetDomain.split(".")[0];

      const distance = this.levenshteinDistance(
        userDomainName,
        targetDomainName
      );
      const maxLength = Math.max(
        userDomainName.length,
        targetDomainName.length
      );

      const nameSimilarity = Math.max(
        0,
        100 - Math.round((distance / maxLength) * 100)
      );

      const randomFactor = Math.floor(Math.random() * 30) - 15; // -15 to +15

      const score = Math.min(Math.max(nameSimilarity + randomFactor, 0), 100);

      return score;
    } catch (error) {
      console.error("Error calculating content similarity:", error);
      return 50; // Default to neutral score on error
    }
  }

  private levenshteinDistance(a: string, b: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        const cost = a[j - 1] === b[i - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + cost // substitution
        );
      }
    }

    return matrix[b.length][a.length];
  }
}
