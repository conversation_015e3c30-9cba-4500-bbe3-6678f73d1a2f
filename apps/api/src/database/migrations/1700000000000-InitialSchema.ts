import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1700000000000 implements MigrationInterface {
    name = 'InitialSchema1700000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create users table
        await queryRunner.query(`
            CREATE TABLE "users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "email" character varying NOT NULL,
                "name" character varying NOT NULL,
                "password" character varying NOT NULL,
                "domain" character varying,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
                CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
            )
        `);

        // Create domains table
        await queryRunner.query(`
            CREATE TABLE "domains" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "domain" character varying NOT NULL,
                "title" character varying,
                "description" text,
                "verified" boolean NOT NULL DEFAULT false,
                "verificationMethod" character varying,
                "verificationCode" character varying,
                "verifiedAt" TIMESTAMP,
                "keywords" text,
                "userId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_8bf0af9e4531c5ca2b6b3d171dc" PRIMARY KEY ("id")
            )
        `);

        // Create target_domains table
        await queryRunner.query(`
            CREATE TABLE "target_domains" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "domain" character varying NOT NULL,
                "title" character varying,
                "description" text,
                "industry" character varying,
                "category" character varying,
                "contactEmail" character varying,
                "contactPage" character varying,
                "socialHandles" text,
                "keywords" text,
                "notes" text,
                "status" character varying NOT NULL DEFAULT 'pending',
                "priority" character varying NOT NULL DEFAULT 'medium',
                "lastAnalyzed" TIMESTAMP,
                "userId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_target_domains" PRIMARY KEY ("id")
            )
        `);

        // Create domain_analysis table
        await queryRunner.query(`
            CREATE TABLE "domain_analysis" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "domain" character varying NOT NULL,
                "domainRating" integer,
                "traffic" bigint,
                "trafficTrend" character varying,
                "topKeywords" text,
                "topPages" text,
                "industry" character varying,
                "category" character varying,
                "topCountry" character varying,
                "websiteAge" integer,
                "language" character varying,
                "contactPage" character varying,
                "contactEmail" character varying,
                "socialHandles" text,
                "backlinkScore" numeric(5,2),
                "scoreBreakdown" text,
                "analyzedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "userId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_domain_analysis" PRIMARY KEY ("id")
            )
        `);

        // Create email_templates table
        await queryRunner.query(`
            CREATE TABLE "email_templates" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "subject" character varying NOT NULL,
                "body" text NOT NULL,
                "variables" text,
                "userId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_email_templates" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "domains" 
            ADD CONSTRAINT "FK_domains_userId" 
            FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "target_domains" 
            ADD CONSTRAINT "FK_target_domains_userId" 
            FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "domain_analysis" 
            ADD CONSTRAINT "FK_domain_analysis_userId" 
            FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "email_templates" 
            ADD CONSTRAINT "FK_email_templates_userId" 
            FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        // Create indexes for better performance
        await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
        await queryRunner.query(`CREATE INDEX "IDX_domains_domain" ON "domains" ("domain")`);
        await queryRunner.query(`CREATE INDEX "IDX_domains_userId" ON "domains" ("userId")`);
        await queryRunner.query(`CREATE INDEX "IDX_target_domains_domain" ON "target_domains" ("domain")`);
        await queryRunner.query(`CREATE INDEX "IDX_target_domains_userId" ON "target_domains" ("userId")`);
        await queryRunner.query(`CREATE INDEX "IDX_domain_analysis_domain" ON "domain_analysis" ("domain")`);
        await queryRunner.query(`CREATE INDEX "IDX_domain_analysis_userId" ON "domain_analysis" ("userId")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "email_templates" DROP CONSTRAINT "FK_email_templates_userId"`);
        await queryRunner.query(`ALTER TABLE "domain_analysis" DROP CONSTRAINT "FK_domain_analysis_userId"`);
        await queryRunner.query(`ALTER TABLE "target_domains" DROP CONSTRAINT "FK_target_domains_userId"`);
        await queryRunner.query(`ALTER TABLE "domains" DROP CONSTRAINT "FK_domains_userId"`);

        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_domain_analysis_userId"`);
        await queryRunner.query(`DROP INDEX "IDX_domain_analysis_domain"`);
        await queryRunner.query(`DROP INDEX "IDX_target_domains_userId"`);
        await queryRunner.query(`DROP INDEX "IDX_target_domains_domain"`);
        await queryRunner.query(`DROP INDEX "IDX_domains_userId"`);
        await queryRunner.query(`DROP INDEX "IDX_domains_domain"`);
        await queryRunner.query(`DROP INDEX "IDX_users_email"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE "email_templates"`);
        await queryRunner.query(`DROP TABLE "domain_analysis"`);
        await queryRunner.query(`DROP TABLE "target_domains"`);
        await queryRunner.query(`DROP TABLE "domains"`);
        await queryRunner.query(`DROP TABLE "users"`);
    }
}
