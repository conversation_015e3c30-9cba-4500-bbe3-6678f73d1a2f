import { Controller, Get, Inject } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { AppService } from "./app.service";

@ApiTags("App")
@Controller()
export class AppController {
  constructor(@Inject(AppService) private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: "Get welcome message" })
  @ApiResponse({ status: 200, description: "Welcome message" })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get("health")
  @ApiOperation({ summary: "Health check endpoint" })
  @ApiResponse({ status: 200, description: "Health status" })
  healthCheck() {
    return {
      status: "ok",
      timestamp: new Date().toISOString(),
      service: "RankMesh Backend API",
      version: "1.0.0",
    };
  }
}
