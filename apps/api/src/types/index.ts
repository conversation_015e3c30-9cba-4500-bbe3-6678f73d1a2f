/**
 * Internal type definitions for the API
 * These types are used internally by the API and may extend or modify the shared types
 */

import {
  AnalysisResultDto,
  DomainAnalysis as SharedDomainAnalysis,
  TargetDomain as SharedTargetDomain,
} from 'shared-types';

/**
 * Extended DomainAnalysis type with nullable fields
 * This is used internally by the API to handle nullable fields
 */
export interface DomainAnalysisInternal extends Omit<SharedDomainAnalysis, 
  'contactEmail' | 'contactPage' | 'lastUpdated' | 'topKeywords' | 'topPages' | 
  'categories' | 'keywords' | 'industry' | 'category' | 'topCountry' | 'language'> {
  contactEmail: string | null;
  contactPage: string | null;
  lastUpdated: Date | null;
  topKeywords: string[] | null;
  topPages: string[] | null;
  categories: string[] | null;
  keywords: string[] | null;
  industry: string | null;
  category: string | null;
  topCountry: string | null;
  language: string | null;
}

/**
 * Extended AnalysisResultDto type with nullable fields
 * This is used internally by the API to handle nullable fields
 */
export interface AnalysisResultDtoInternal extends Omit<AnalysisResultDto, 
  'contactEmail' | 'contactPage' | 'lastUpdated' | 'topKeywords' | 'topPages' | 
  'categories' | 'keywords' | 'industry' | 'category' | 'topCountry' | 'language'> {
  contactEmail: string | null;
  contactPage: string | null;
  lastUpdated: Date | null;
  topKeywords: string[] | null;
  topPages: string[] | null;
  categories: string[] | null;
  keywords: string[] | null;
  industry: string | null;
  category: string | null;
  topCountry: string | null;
  language: string | null;
}

/**
 * Extended TargetDomain type with nullable fields
 * This is used internally by the API to handle nullable fields
 */
export interface TargetDomainInternal extends Omit<SharedTargetDomain, 
  'contactEmail' | 'contactPage' | 'categories' | 'keywords' | 'lastUpdated'> {
  contactEmail: string | null;
  contactPage: string | null;
  categories: string[] | null;
  keywords: string[] | null;
  lastUpdated: Date | null;
}

/**
 * Type for domain analysis data
 */
export interface DomainAnalysisData {
  relevanceScore: number;
  domainAuthority: number;
  backlinks: number;
  traffic: number;
  trafficTrend: "upward" | "downward" | "stable";
  topKeywords: string[];
  topPages: string[];
  industry: string;
  category: string;
  topCountry: string;
  websiteAge: number;
  language: string;
  contactEmail: string | null;
  contactPage: string | null;
  categories: string[];
  keywords: string[];
  lastUpdated: Date;
}

/**
 * Type for contact information
 */
export interface ContactInfo {
  emails: string[];
  phoneNumbers: string[];
  socialProfiles: string[];
  contactForm: string | null;
  address: string | null;
}

/**
 * Type for domain data
 */
export interface DomainData {
  title: string;
  description: string;
  keywords: string[];
  relevanceScore: number;
  industry: string;
  category: string;
  language: string;
  domainAuthority: number;
  backlinks: number;
  traffic: number;
  trafficTrend: "upward" | "downward" | "stable";
  topKeywords: string[];
  topPages: string[];
  websiteAge: number;
  topCountry: string;
  contactEmail: string | null;
  contactPage: string | null;
  contactName: string | null;
  contactPosition: string | null;
  contactForm: string | null;
  address: string | null;
  socialProfiles: string[];
  phoneNumbers: string[];
  chatSupport: boolean;
  favicon: string | null;
  author: string | null;
  publishDate: Date | null;
  lastModified: Date | null;
  headings: {
    h1: string[];
    h2: string[];
    h3: string[];
  };
  sentiment: number;
  topTopics: string[];
}
