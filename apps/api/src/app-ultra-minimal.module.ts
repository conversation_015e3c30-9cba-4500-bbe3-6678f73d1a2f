import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";

/**
 * Ultra-minimal app module for testing without any external dependencies
 * This module only includes:
 * - Basic configuration
 * - App controller and service
 * - No third-party services, no cache, no queue, no database
 */
@Module({
  imports: [
    // Configuration only
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.development", ".env.local", ".env"],
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppUltraMinimalModule {}
