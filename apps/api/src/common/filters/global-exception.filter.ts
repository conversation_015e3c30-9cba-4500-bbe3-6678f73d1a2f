import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { Request, Response } from "express";
import { QueryFailedError } from "typeorm";
import { ValidationError } from "class-validator";

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
    method: string;
    requestId?: string;
  };
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const errorResponse = this.buildErrorResponse(exception, request);

    // Log the error
    this.logError(exception, request, errorResponse);

    response.status(errorResponse.error.code === "INTERNAL_SERVER_ERROR" ? 500 : this.getHttpStatus(exception))
      .json(errorResponse);
  }

  private buildErrorResponse(exception: unknown, request: Request): ErrorResponse {
    const timestamp = new Date().toISOString();
    const path = request.url;
    const method = request.method;
    const requestId = request.headers["x-request-id"] as string;

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      return this.handleHttpException(exception, { timestamp, path, method, requestId });
    }

    if (exception instanceof QueryFailedError) {
      return this.handleDatabaseException(exception, { timestamp, path, method, requestId });
    }

    if (this.isValidationError(exception)) {
      return this.handleValidationException(exception as ValidationError[], { timestamp, path, method, requestId });
    }

    // Handle unknown errors
    return this.handleUnknownException(exception, { timestamp, path, method, requestId });
  }

  private handleHttpException(
    exception: HttpException,
    context: { timestamp: string; path: string; method: string; requestId?: string }
  ): ErrorResponse {
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    let message: string;
    let details: any;
    let code: string;

    if (typeof exceptionResponse === "string") {
      message = exceptionResponse;
      code = this.getErrorCode(status);
    } else if (typeof exceptionResponse === "object") {
      const response = exceptionResponse as any;
      message = response.message || response.error || "An error occurred";
      details = response.details;
      code = response.code || this.getErrorCode(status);

      // Handle validation errors from class-validator
      if (Array.isArray(response.message)) {
        message = "Validation failed";
        details = response.message;
        code = "VALIDATION_ERROR";
      }
    } else {
      message = "An error occurred";
      code = this.getErrorCode(status);
    }

    return {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: context.timestamp,
        path: context.path,
        method: context.method,
        requestId: context.requestId,
      },
    };
  }

  private handleDatabaseException(
    exception: QueryFailedError,
    context: { timestamp: string; path: string; method: string; requestId?: string }
  ): ErrorResponse {
    let message = "Database operation failed";
    let code = "DATABASE_ERROR";
    let details: any;

    // Handle specific database errors
    const error = exception as any;
    
    if (error.code === "23505") { // Unique constraint violation
      message = "Resource already exists";
      code = "DUPLICATE_RESOURCE";
      details = { constraint: error.constraint };
    } else if (error.code === "23503") { // Foreign key constraint violation
      message = "Referenced resource not found";
      code = "FOREIGN_KEY_VIOLATION";
      details = { constraint: error.constraint };
    } else if (error.code === "23502") { // Not null constraint violation
      message = "Required field is missing";
      code = "MISSING_REQUIRED_FIELD";
      details = { column: error.column };
    } else if (error.code === "22001") { // String data too long
      message = "Data too long for field";
      code = "DATA_TOO_LONG";
    }

    return {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: context.timestamp,
        path: context.path,
        method: context.method,
        requestId: context.requestId,
      },
    };
  }

  private handleValidationException(
    errors: ValidationError[],
    context: { timestamp: string; path: string; method: string; requestId?: string }
  ): ErrorResponse {
    const details = errors.map(error => ({
      property: error.property,
      value: error.value,
      constraints: error.constraints,
    }));

    return {
      success: false,
      error: {
        code: "VALIDATION_ERROR",
        message: "Input validation failed",
        details,
        timestamp: context.timestamp,
        path: context.path,
        method: context.method,
        requestId: context.requestId,
      },
    };
  }

  private handleUnknownException(
    exception: unknown,
    context: { timestamp: string; path: string; method: string; requestId?: string }
  ): ErrorResponse {
    let message = "Internal server error";
    let details: any;

    if (exception instanceof Error) {
      message = exception.message || message;
      details = {
        name: exception.name,
        stack: process.env.NODE_ENV === "development" ? exception.stack : undefined,
      };
    }

    return {
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message,
        details,
        timestamp: context.timestamp,
        path: context.path,
        method: context.method,
        requestId: context.requestId,
      },
    };
  }

  private getHttpStatus(exception: unknown): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }

    if (exception instanceof QueryFailedError) {
      const error = exception as any;
      if (error.code === "23505" || error.code === "23503") {
        return HttpStatus.CONFLICT;
      }
      if (error.code === "23502" || error.code === "22001") {
        return HttpStatus.BAD_REQUEST;
      }
      return HttpStatus.INTERNAL_SERVER_ERROR;
    }

    if (this.isValidationError(exception)) {
      return HttpStatus.BAD_REQUEST;
    }

    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  private getErrorCode(status: number): string {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return "BAD_REQUEST";
      case HttpStatus.UNAUTHORIZED:
        return "UNAUTHORIZED";
      case HttpStatus.FORBIDDEN:
        return "FORBIDDEN";
      case HttpStatus.NOT_FOUND:
        return "NOT_FOUND";
      case HttpStatus.CONFLICT:
        return "CONFLICT";
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return "UNPROCESSABLE_ENTITY";
      case HttpStatus.TOO_MANY_REQUESTS:
        return "RATE_LIMIT_EXCEEDED";
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return "INTERNAL_SERVER_ERROR";
      case HttpStatus.BAD_GATEWAY:
        return "BAD_GATEWAY";
      case HttpStatus.SERVICE_UNAVAILABLE:
        return "SERVICE_UNAVAILABLE";
      case HttpStatus.GATEWAY_TIMEOUT:
        return "GATEWAY_TIMEOUT";
      default:
        return "UNKNOWN_ERROR";
    }
  }

  private isValidationError(exception: unknown): boolean {
    return Array.isArray(exception) && 
           exception.length > 0 && 
           exception[0] instanceof ValidationError;
  }

  private logError(
    exception: unknown,
    request: Request,
    errorResponse: ErrorResponse
  ): void {
    const { error } = errorResponse;
    const logContext = {
      requestId: error.requestId,
      method: error.method,
      path: error.path,
      userAgent: request.headers["user-agent"],
      ip: request.ip,
      userId: (request as any).user?.id,
    };

    if (error.code === "INTERNAL_SERVER_ERROR") {
      this.logger.error(
        `${error.code}: ${error.message}`,
        exception instanceof Error ? exception.stack : undefined,
        JSON.stringify(logContext)
      );
    } else if (error.code === "DATABASE_ERROR") {
      this.logger.error(
        `${error.code}: ${error.message}`,
        JSON.stringify({ ...logContext, details: error.details })
      );
    } else {
      this.logger.warn(
        `${error.code}: ${error.message}`,
        JSON.stringify({ ...logContext, details: error.details })
      );
    }
  }
}
