import { Injectable, Logger } from "@nestjs/common";

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number) => void;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

@Injectable()
export class RetryService {
  private readonly logger = new Logger(RetryService.name);

  /**
   * Execute a function with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<RetryResult<T>> {
    const config: RetryOptions = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      retryCondition: this.defaultRetryCondition,
      ...options,
    };

    const startTime = Date.now();
    let lastError: any;
    let attempt = 0;

    while (attempt < config.maxAttempts) {
      attempt++;

      try {
        const result = await operation();
        const totalTime = Date.now() - startTime;

        if (attempt > 1) {
          this.logger.log(
            `Operation succeeded on attempt ${attempt}/${config.maxAttempts} after ${totalTime}ms`
          );
        }

        return {
          success: true,
          result,
          attempts: attempt,
          totalTime,
        };
      } catch (error) {
        lastError = error;

        // Check if we should retry this error
        if (!config.retryCondition!(error)) {
          this.logger.warn(
            `Operation failed with non-retryable error on attempt ${attempt}: ${error.message}`
          );
          break;
        }

        // If this is the last attempt, don't wait
        if (attempt >= config.maxAttempts) {
          break;
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt, config);

        this.logger.warn(
          `Operation failed on attempt ${attempt}/${config.maxAttempts}: ${error.message}. Retrying in ${delay}ms...`
        );

        // Call onRetry callback if provided
        if (config.onRetry) {
          try {
            config.onRetry(error, attempt);
          } catch (callbackError) {
            this.logger.error(
              `Error in retry callback: ${callbackError.message}`
            );
          }
        }

        // Wait before next attempt
        await this.delay(delay);
      }
    }

    const totalTime = Date.now() - startTime;
    this.logger.error(
      `Operation failed after ${attempt} attempts in ${totalTime}ms. Last error: ${lastError?.message}`
    );

    return {
      success: false,
      error: lastError,
      attempts: attempt,
      totalTime,
    };
  }

  /**
   * Execute multiple operations with retry logic in parallel
   */
  async executeMultipleWithRetry<T>(
    operations: Array<() => Promise<T>>,
    options: Partial<RetryOptions> = {}
  ): Promise<Array<RetryResult<T>>> {
    const promises = operations.map(operation =>
      this.executeWithRetry(operation, options)
    );

    return Promise.all(promises);
  }

  /**
   * Execute operations with retry logic in sequence (one after another)
   */
  async executeSequentialWithRetry<T>(
    operations: Array<() => Promise<T>>,
    options: Partial<RetryOptions> = {}
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = [];

    for (const operation of operations) {
      const result = await this.executeWithRetry(operation, options);
      results.push(result);

      // If operation failed and we want to stop on first failure
      if (!result.success && options.retryCondition) {
        break;
      }
    }

    return results;
  }

  /**
   * Retry with exponential backoff and jitter
   */
  async retryWithExponentialBackoff<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 30000,
    jitter: boolean = true
  ): Promise<T> {
    const result = await this.executeWithRetry(operation, {
      maxAttempts,
      baseDelay,
      maxDelay,
      backoffMultiplier: 2,
      retryCondition: (error) => this.isRetryableError(error),
    });

    if (result.success) {
      return result.result!;
    }

    throw result.error;
  }

  /**
   * Retry with linear backoff
   */
  async retryWithLinearBackoff<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> {
    const result = await this.executeWithRetry(operation, {
      maxAttempts,
      baseDelay: delay,
      maxDelay: delay * maxAttempts,
      backoffMultiplier: 1,
      retryCondition: (error) => this.isRetryableError(error),
    });

    if (result.success) {
      return result.result!;
    }

    throw result.error;
  }

  /**
   * Retry with fixed delay
   */
  async retryWithFixedDelay<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> {
    const result = await this.executeWithRetry(operation, {
      maxAttempts,
      baseDelay: delay,
      maxDelay: delay,
      backoffMultiplier: 1,
      retryCondition: (error) => this.isRetryableError(error),
    });

    if (result.success) {
      return result.result!;
    }

    throw result.error;
  }

  /**
   * Calculate delay for next retry attempt
   */
  private calculateDelay(attempt: number, config: RetryOptions): number {
    let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    
    // Apply maximum delay limit
    delay = Math.min(delay, config.maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    delay += jitter;
    
    return Math.floor(delay);
  }

  /**
   * Default retry condition - retries on network and temporary errors
   */
  private defaultRetryCondition(error: any): boolean {
    return this.isRetryableError(error);
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors
    if (error.code === "ECONNRESET" || 
        error.code === "ECONNREFUSED" || 
        error.code === "ETIMEDOUT" ||
        error.code === "ENOTFOUND") {
      return true;
    }

    // HTTP status codes that are retryable
    if (error.response?.status) {
      const status = error.response.status;
      return status === 408 || // Request Timeout
             status === 429 || // Too Many Requests
             status === 502 || // Bad Gateway
             status === 503 || // Service Unavailable
             status === 504;   // Gateway Timeout
    }

    // Axios network errors
    if (error.isAxiosError && !error.response) {
      return true;
    }

    // Database connection errors
    if (error.name === "ConnectionError" || 
        error.name === "TimeoutError" ||
        error.message?.includes("connection")) {
      return true;
    }

    // Rate limiting errors
    if (error.message?.toLowerCase().includes("rate limit") ||
        error.message?.toLowerCase().includes("too many requests")) {
      return true;
    }

    return false;
  }

  /**
   * Create a delay promise
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create retry decorator for methods
   */
  static createRetryDecorator(options: Partial<RetryOptions> = {}) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        const retryService = new RetryService();
        const result = await retryService.executeWithRetry(
          () => method.apply(this, args),
          options
        );

        if (result.success) {
          return result.result;
        }

        throw result.error;
      };
    };
  }
}

/**
 * Retry decorator for class methods
 */
export function Retry(options: Partial<RetryOptions> = {}) {
  return RetryService.createRetryDecorator(options);
}

/**
 * Circuit breaker pattern implementation
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000, // 1 minute
    private readonly logger: Logger = new Logger(CircuitBreaker.name)
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === "OPEN") {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = "HALF_OPEN";
        this.logger.log("Circuit breaker transitioning to HALF_OPEN state");
      } else {
        throw new Error("Circuit breaker is OPEN");
      }
    }

    try {
      const result = await operation();
      
      if (this.state === "HALF_OPEN") {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = "OPEN";
      this.logger.warn(
        `Circuit breaker opened after ${this.failures} failures`
      );
    }
  }

  private reset(): void {
    this.failures = 0;
    this.state = "CLOSED";
    this.logger.log("Circuit breaker reset to CLOSED state");
  }

  getState(): string {
    return this.state;
  }

  getFailures(): number {
    return this.failures;
  }
}
