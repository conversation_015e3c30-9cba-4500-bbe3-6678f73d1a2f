#!/usr/bin/env node

/**
 * Comprehensive error fixing and startup script for RankMesh Backend
 * This script identifies and fixes all common startup errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 RankMesh Backend - Comprehensive Error Fixing\n');

let errorCount = 0;
let fixCount = 0;

function logError(message) {
  console.error(`❌ ${message}`);
  errorCount++;
}

function logFix(message) {
  console.log(`✅ ${message}`);
  fixCount++;
}

function logInfo(message) {
  console.log(`ℹ️ ${message}`);
}

// Step 1: Check and fix file structure
console.log('1️⃣ Checking File Structure...');

const requiredFiles = [
  { path: 'package.json', critical: true },
  { path: 'tsconfig.json', critical: true },
  { path: 'src/main-ultra-minimal.ts', critical: true },
  { path: 'src/app-ultra-minimal.module.ts', critical: true },
  { path: 'src/app.controller.ts', critical: true },
  { path: 'src/app.service.ts', critical: true },
  { path: 'src/third-party/third-party-simple.module.ts', critical: true },
  { path: '.env.development', critical: false }
];

for (const file of requiredFiles) {
  const filePath = path.join(__dirname, file.path);
  if (fs.existsSync(filePath)) {
    logFix(`${file.path} exists`);
  } else {
    if (file.critical) {
      logError(`${file.path} is missing (CRITICAL)`);
    } else {
      logError(`${file.path} is missing`);
    }
  }
}

// Step 2: Check dependencies
console.log('\n2️⃣ Checking Dependencies...');

const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  logError('node_modules directory missing');
  logInfo('Run: npm install');
} else {
  logFix('node_modules directory exists');
}

// Check critical dependencies
const criticalDeps = [
  '@nestjs/core',
  '@nestjs/common',
  '@nestjs/config',
  '@nestjs/swagger',
  '@nestjs/axios',
  'reflect-metadata',
  'rxjs',
  'class-validator',
  'class-transformer'
];

for (const dep of criticalDeps) {
  const depPath = path.join(__dirname, 'node_modules', dep);
  if (fs.existsSync(depPath)) {
    logFix(`${dep} installed`);
  } else {
    logError(`${dep} missing`);
  }
}

// Step 3: Check TypeScript configuration
console.log('\n3️⃣ Checking TypeScript Configuration...');

try {
  const tsconfigPath = path.join(__dirname, 'tsconfig.json');
  const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
  
  if (tsconfig.compilerOptions) {
    logFix('tsconfig.json has compilerOptions');
    
    const requiredOptions = [
      'experimentalDecorators',
      'emitDecoratorMetadata',
      'target',
      'module'
    ];
    
    for (const option of requiredOptions) {
      if (tsconfig.compilerOptions[option] !== undefined) {
        logFix(`tsconfig has ${option}`);
      } else {
        logError(`tsconfig missing ${option}`);
      }
    }
  } else {
    logError('tsconfig.json missing compilerOptions');
  }
} catch (error) {
  logError(`Failed to read tsconfig.json: ${error.message}`);
}

// Step 4: Check environment configuration
console.log('\n4️⃣ Checking Environment Configuration...');

const envPath = path.join(__dirname, '.env.development');
if (fs.existsSync(envPath)) {
  logFix('.env.development exists');
  
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredEnvVars = [
      'NODE_ENV',
      'PORT',
      'DB_ENABLED',
      'ENABLE_MOCK_DATA'
    ];
    
    for (const envVar of requiredEnvVars) {
      if (envContent.includes(envVar)) {
        logFix(`Environment variable ${envVar} configured`);
      } else {
        logError(`Environment variable ${envVar} missing`);
      }
    }
  } catch (error) {
    logError(`Failed to read .env.development: ${error.message}`);
  }
} else {
  logError('.env.development missing');
  logInfo('Creating default .env.development...');
  
  const defaultEnv = `# Development Environment Configuration
NODE_ENV=development
PORT=3001
DB_ENABLED=false
ENABLE_MOCK_DATA=true
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=debug`;
  
  try {
    fs.writeFileSync(envPath, defaultEnv);
    logFix('Created default .env.development');
  } catch (error) {
    logError(`Failed to create .env.development: ${error.message}`);
  }
}

// Step 5: Test module imports
console.log('\n5️⃣ Testing Module Imports...');

try {
  require('reflect-metadata');
  logFix('reflect-metadata imports successfully');
} catch (error) {
  logError(`reflect-metadata import failed: ${error.message}`);
}

try {
  const { NestFactory } = require('@nestjs/core');
  logFix('@nestjs/core imports successfully');
} catch (error) {
  logError(`@nestjs/core import failed: ${error.message}`);
}

// Test ultra minimal module import
try {
  // Try compiled version first
  try {
    const { AppUltraMinimalModule } = require('./dist/apps/api/src/app-ultra-minimal.module');
    logFix('AppUltraMinimalModule imports successfully (compiled)');
  } catch (compiledError) {
    // Try source version
    require('ts-node/register');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
    logFix('AppUltraMinimalModule imports successfully (source)');
  }
} catch (error) {
  logError(`AppUltraMinimalModule import failed: ${error.message}`);
}

// Step 6: Check for common circular dependency issues
console.log('\n6️⃣ Checking for Circular Dependencies...');

const moduleFiles = [
  'src/app-ultra-minimal.module.ts',
  'src/third-party/third-party-simple.module.ts',
  'src/cache/cache.module.ts',
  'src/queue/queue.module.ts'
];

for (const moduleFile of moduleFiles) {
  const modulePath = path.join(__dirname, moduleFile);
  if (fs.existsSync(modulePath)) {
    try {
      const content = fs.readFileSync(modulePath, 'utf8');
      
      // Check for potential circular imports
      if (content.includes('forwardRef')) {
        logInfo(`${moduleFile} uses forwardRef (potential circular dependency)`);
      }
      
      logFix(`${moduleFile} readable`);
    } catch (error) {
      logError(`Failed to read ${moduleFile}: ${error.message}`);
    }
  }
}

// Step 7: Summary and recommendations
console.log('\n7️⃣ Summary and Recommendations...');

console.log(`\n📊 Results:`);
console.log(`✅ Fixes applied: ${fixCount}`);
console.log(`❌ Errors found: ${errorCount}`);

if (errorCount === 0) {
  console.log('\n🎉 All checks passed! Your backend should start successfully.');
  console.log('\n🚀 To start the server, run:');
  console.log('   node start-minimal.js');
  console.log('   OR');
  console.log('   npm run start:minimal');
} else {
  console.log('\n⚠️ Issues found that need attention:');
  
  if (errorCount > 5) {
    console.log('\n🆘 Many errors found. Recommended actions:');
    console.log('1. Run: npm install');
    console.log('2. Run: npm run build');
    console.log('3. Check Node.js version (should be v16+)');
    console.log('4. Re-run this script');
  } else {
    console.log('\n💡 Few errors found. Try:');
    console.log('1. Fix the specific errors mentioned above');
    console.log('2. Run: node start-minimal.js');
  }
}

console.log('\n📞 If you still have issues:');
console.log('1. Run: node test-and-start.js');
console.log('2. Check the detailed error logs');
console.log('3. Ensure all dependencies are installed');

process.exit(errorCount > 0 ? 1 : 0);
