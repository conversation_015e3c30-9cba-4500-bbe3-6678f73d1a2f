import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';

describe('AnalysisController (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  const mockJwtAuthGuard = {
    canActivate: () => true,
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test', '.env'],
        }),
        AppModule,
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Mock authentication for tests
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/analysis (POST)', () => {
    it('should create a new analysis', async () => {
      const createAnalysisDto = {
        targetDomainId: '1',
        userDomainId: '2',
      };

      const response = await request(app.getHttpServer())
        .post('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createAnalysisDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.targetDomainId).toBe(createAnalysisDto.targetDomainId);
      expect(response.body.userDomainId).toBe(createAnalysisDto.userDomainId);
      expect(response.body).toHaveProperty('score');
      expect(response.body).toHaveProperty('createdAt');
    });

    it('should return 400 for invalid input', async () => {
      const invalidDto = {
        targetDomainId: '', // Invalid empty string
        userDomainId: '2',
      };

      await request(app.getHttpServer())
        .post('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDto)
        .expect(400);
    });

    it('should return 401 without authentication', async () => {
      const createAnalysisDto = {
        targetDomainId: '1',
        userDomainId: '2',
      };

      // Override guard to reject authentication
      const moduleWithAuth = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      const appWithAuth = moduleWithAuth.createNestApplication();
      await appWithAuth.init();

      await request(appWithAuth.getHttpServer())
        .post('/analysis')
        .send(createAnalysisDto)
        .expect(401);

      await appWithAuth.close();
    });
  });

  describe('/analysis (GET)', () => {
    it('should return all analyses', async () => {
      const response = await request(app.getHttpServer())
        .get('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      if (response.body.length > 0) {
        expect(response.body[0]).toHaveProperty('id');
        expect(response.body[0]).toHaveProperty('targetDomainId');
        expect(response.body[0]).toHaveProperty('userDomainId');
        expect(response.body[0]).toHaveProperty('score');
      }
    });
  });

  describe('/analysis/:id (GET)', () => {
    let analysisId: string;

    beforeAll(async () => {
      // Create an analysis for testing
      const createResponse = await request(app.getHttpServer())
        .post('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          targetDomainId: '1',
          userDomainId: '2',
        });

      analysisId = createResponse.body.id;
    });

    it('should return a specific analysis', async () => {
      const response = await request(app.getHttpServer())
        .get(`/analysis/${analysisId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(analysisId);
      expect(response.body).toHaveProperty('targetDomainId');
      expect(response.body).toHaveProperty('userDomainId');
      expect(response.body).toHaveProperty('score');
    });

    it('should return 404 for non-existent analysis', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';

      await request(app.getHttpServer())
        .get(`/analysis/${nonExistentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('/analysis/analyze (GET)', () => {
    it('should analyze a domain and return results', async () => {
      const targetDomain = 'example.com';
      const userDomain = 'user.com';

      const response = await request(app.getHttpServer())
        .get('/analysis/analyze')
        .query({
          targetDomain,
          userDomain,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('title');
      expect(response.body).toHaveProperty('description');
      expect(response.body).toHaveProperty('keywords');
      expect(response.body).toHaveProperty('relevanceScore');
      expect(response.body).toHaveProperty('industry');
      expect(response.body).toHaveProperty('category');
      expect(response.body).toHaveProperty('language');
      expect(response.body).toHaveProperty('domainAuthority');
      expect(response.body).toHaveProperty('backlinks');
      expect(response.body).toHaveProperty('traffic');
      expect(response.body).toHaveProperty('trafficTrend');
      expect(response.body).toHaveProperty('topKeywords');
      expect(response.body).toHaveProperty('topPages');
      expect(response.body).toHaveProperty('websiteAge');
      expect(response.body).toHaveProperty('topCountry');
      expect(response.body).toHaveProperty('contactEmail');
      expect(response.body).toHaveProperty('socialProfiles');

      expect(typeof response.body.relevanceScore).toBe('number');
      expect(response.body.relevanceScore).toBeGreaterThanOrEqual(0);
      expect(response.body.relevanceScore).toBeLessThanOrEqual(100);
    });

    it('should return 400 for missing query parameters', async () => {
      await request(app.getHttpServer())
        .get('/analysis/analyze')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 400 for invalid domain format', async () => {
      await request(app.getHttpServer())
        .get('/analysis/analyze')
        .query({
          targetDomain: 'invalid-domain',
          userDomain: 'also-invalid',
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });
  });

  describe('/analysis/:id (PATCH)', () => {
    let analysisId: string;

    beforeAll(async () => {
      // Create an analysis for testing
      const createResponse = await request(app.getHttpServer())
        .post('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          targetDomainId: '1',
          userDomainId: '2',
        });

      analysisId = createResponse.body.id;
    });

    it('should update an analysis', async () => {
      const updateData = {
        score: 95,
      };

      const response = await request(app.getHttpServer())
        .patch(`/analysis/${analysisId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.id).toBe(analysisId);
      expect(response.body.score).toBe(95);
    });

    it('should return 404 for non-existent analysis', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';
      const updateData = { score: 95 };

      await request(app.getHttpServer())
        .patch(`/analysis/${nonExistentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(404);
    });
  });

  describe('/analysis/:id (DELETE)', () => {
    let analysisId: string;

    beforeEach(async () => {
      // Create an analysis for testing
      const createResponse = await request(app.getHttpServer())
        .post('/analysis')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          targetDomainId: '1',
          userDomainId: '2',
        });

      analysisId = createResponse.body.id;
    });

    it('should delete an analysis', async () => {
      await request(app.getHttpServer())
        .delete(`/analysis/${analysisId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify it's deleted
      await request(app.getHttpServer())
        .get(`/analysis/${analysisId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 404 for non-existent analysis', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';

      await request(app.getHttpServer())
        .delete(`/analysis/${nonExistentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
