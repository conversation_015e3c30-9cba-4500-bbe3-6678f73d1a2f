import 'reflect-metadata';

// E2E test setup
beforeAll(() => {
  // Set test environment variables for E2E tests
  process.env.NODE_ENV = 'test';
  process.env.DB_HOST = 'localhost';
  process.env.DB_PORT = '5432';
  process.env.DB_USERNAME = 'test';
  process.env.DB_PASSWORD = 'test';
  process.env.DB_DATABASE = 'test_backlink_e2e';
  process.env.JWT_SECRET = 'test-jwt-secret-e2e';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
  process.env.REDIS_DB = '15'; // Use different DB for E2E tests
  process.env.AHREFS_API_KEY = 'test-ahrefs-key';
  process.env.SIMILARWEB_API_KEY = 'test-similarweb-key';
  process.env.WHOISXML_API_KEY = 'test-whoisxml-key';
  process.env.ENABLE_MOCK_DATA = 'true'; // Use mock data for E2E tests
});

// Increase timeout for E2E tests
jest.setTimeout(30000);
