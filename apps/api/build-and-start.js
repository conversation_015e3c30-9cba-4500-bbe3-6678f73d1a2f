#!/usr/bin/env node

/**
 * Build and start script for RankMesh Backend
 * This script builds the TypeScript and starts the server
 */

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 RankMesh Backend - Build and Start\n');

// Step 1: Check if build is needed
console.log('1️⃣ Checking build status...');

const distPath = path.join(__dirname, 'dist');
const mainJsPath = path.join(__dirname, 'dist/apps/api/src/main-ultra-minimal.js');

if (!fs.existsSync(distPath) || !fs.existsSync(mainJsPath)) {
  console.log('❌ Build files missing - building project...');
  
  try {
    console.log('Running: npm run build');
    execSync('npm run build', { 
      stdio: 'inherit',
      cwd: __dirname 
    });
    console.log('✅ Build completed successfully');
  } catch (buildError) {
    console.error('❌ Build failed:', buildError.message);
    console.log('\n💡 Trying alternative build method...');
    
    try {
      console.log('Running: npx tsc');
      execSync('npx tsc', { 
        stdio: 'inherit',
        cwd: __dirname 
      });
      console.log('✅ TypeScript compilation completed');
    } catch (tscError) {
      console.error('❌ TypeScript compilation failed:', tscError.message);
      console.log('\n💡 Starting with ts-node instead...');
      startWithTsNode();
      return;
    }
  }
} else {
  console.log('✅ Build files exist');
}

// Step 2: Start the server
console.log('\n2️⃣ Starting server...');

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  DB_ENABLED: process.env.DB_ENABLED,
  MOCK_DATA: process.env.ENABLE_MOCK_DATA
});

try {
  console.log('Loading compiled application...');
  require('./dist/apps/api/src/main-ultra-minimal.js');
} catch (error) {
  console.error('❌ Failed to start compiled version:', error.message);
  console.log('\n💡 Trying ts-node fallback...');
  startWithTsNode();
}

function startWithTsNode() {
  console.log('\n3️⃣ Starting with ts-node...');
  
  try {
    // Register ts-node
    require('ts-node/register');
    
    // Set environment
    process.env.NODE_ENV = process.env.NODE_ENV || 'development';
    process.env.PORT = process.env.PORT || '3001';
    process.env.DB_ENABLED = 'false';
    process.env.ENABLE_MOCK_DATA = 'true';
    
    console.log('Loading TypeScript source...');
    require('./src/main-ultra-minimal.ts');
    
  } catch (tsError) {
    console.error('❌ ts-node startup failed:', tsError.message);
    console.error('Stack:', tsError.stack);
    
    console.log('\n🆘 Manual steps to try:');
    console.log('1. npm install');
    console.log('2. npm run build');
    console.log('3. node start-minimal.js');
    console.log('4. Check Node.js version (should be v16+)');
    
    process.exit(1);
  }
}
