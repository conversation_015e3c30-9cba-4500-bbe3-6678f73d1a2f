#!/usr/bin/env node

/**
 * Minimal startup script for RankMesh Backend
 * This script starts the server with minimal dependencies and proper error handling
 */

console.log('🚀 RankMesh Backend - Minimal Startup\n');

// Set environment variables for minimal startup
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment configured:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- PORT:', process.env.PORT);
console.log('- DB_ENABLED:', process.env.DB_ENABLED);
console.log('- MOCK_DATA:', process.env.ENABLE_MOCK_DATA);

console.log('\n📦 Loading dependencies...');

// Load reflect-metadata first
try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata loaded');
} catch (error) {
  console.error('❌ reflect-metadata failed:', error.message);
  process.exit(1);
}

// Load NestJS core
try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ NestJS core loaded');
} catch (error) {
  console.error('❌ NestJS core failed:', error.message);
  process.exit(1);
}

// Load the minimal app module
try {
  const { AppUltraMinimalModule } = require('./dist/apps/api/src/app-ultra-minimal.module');
  console.log('✅ Ultra minimal app module loaded');
} catch (error) {
  console.error('❌ Ultra minimal app module failed:', error.message);
  console.log('Trying to load from source...');
  
  try {
    // Try to load TypeScript directly
    require('ts-node/register');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
    console.log('✅ Ultra minimal app module loaded from source');
  } catch (tsError) {
    console.error('❌ Failed to load from source:', tsError.message);
    console.log('\n💡 Please run: npm run build');
    process.exit(1);
  }
}

console.log('\n🏗️ Creating NestJS application...');

async function startServer() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { ValidationPipe } = require('@nestjs/common');
    
    // Try to load compiled version first, then source
    let AppModule;
    try {
      AppModule = require('./dist/apps/api/src/app-ultra-minimal.module').AppUltraMinimalModule;
    } catch (error) {
      require('ts-node/register');
      AppModule = require('./src/app-ultra-minimal.module').AppUltraMinimalModule;
    }

    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log'],
    });

    console.log('✅ Application created');

    // Enable CORS
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
      credentials: true,
    });
    console.log('✅ CORS enabled');

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );

    // Set global prefix
    app.setGlobalPrefix('');
    console.log('✅ Global prefix set');

    console.log('\n🌐 Starting server...');

    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log('\n🎉 SUCCESS! Server is running!');
    console.log(`🌐 Server URL: http://localhost:${port}`);
    console.log(`❤️ Health Check: http://localhost:${port}/health`);
    console.log(`🏠 Home: http://localhost:${port}/`);
    console.log(`📚 API Docs: http://localhost:${port}/api`);
    console.log('\nPress Ctrl+C to stop the server');

    // Test the health endpoint after a short delay
    setTimeout(async () => {
      try {
        const response = await fetch(`http://localhost:${port}/health`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health endpoint test passed:', data);
        }
      } catch (fetchError) {
        console.log('⚠️ Could not test health endpoint (server might still be starting)');
      }
    }, 2000);

  } catch (error) {
    console.error('\n❌ Server startup failed:');
    console.error('Error:', error.message);
    console.error('Type:', error.constructor.name);
    
    if (error.stack) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }
    
    // Provide specific guidance based on error type
    console.error('\n🔧 Troubleshooting suggestions:');
    
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('- Dependency injection issue: Check service constructors and providers');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('- Missing module: Run npm install');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.error('- Circular dependency: Check module imports');
    }
    
    if (error.message.includes('ECONNREFUSED')) {
      console.error('- Connection refused: Database or Redis not available (should be disabled)');
    }
    
    if (error.message.includes('EADDRINUSE')) {
      console.error('- Port in use: Try a different port with PORT=3002 node start-minimal.js');
    }
    
    process.exit(1);
  }
}

// Start the server
startServer();
