#!/usr/bin/env node

/**
 * Ultimate startup script for RankMesh Backend
 * This script tries multiple approaches to start the server successfully
 */

const fs = require('fs');
const path = require('path');
const { spawn, execSync } = require('child_process');

console.log('🚀 RankMesh Backend - Ultimate Startup Script\n');

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('🌍 Environment Configuration:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`   PORT: ${process.env.PORT}`);
console.log(`   DB_ENABLED: ${process.env.DB_ENABLED}`);
console.log(`   MOCK_DATA: ${process.env.ENABLE_MOCK_DATA}`);

// Startup methods in order of preference
const startupMethods = [
  {
    name: 'Compiled Ultra Minimal',
    description: 'Using pre-compiled ultra minimal module',
    method: startCompiledUltraMinimal
  },
  {
    name: 'Source Ultra Minimal',
    description: 'Using TypeScript source ultra minimal module',
    method: startSourceUltraMinimal
  },
  {
    name: 'Compiled Minimal',
    description: 'Using pre-compiled minimal module',
    method: startCompiledMinimal
  },
  {
    name: 'Source Minimal',
    description: 'Using TypeScript source minimal module',
    method: startSourceMinimal
  },
  {
    name: 'Build and Start',
    description: 'Building project and starting',
    method: buildAndStart
  }
];

async function tryStartupMethods() {
  for (let i = 0; i < startupMethods.length; i++) {
    const method = startupMethods[i];
    console.log(`\n${i + 1}️⃣ Trying: ${method.name}`);
    console.log(`   ${method.description}`);
    
    try {
      const success = await method.method();
      if (success) {
        console.log(`\n🎉 SUCCESS! Server started using: ${method.name}`);
        return true;
      }
    } catch (error) {
      console.log(`❌ ${method.name} failed: ${error.message}`);
    }
  }
  
  console.log('\n💥 All startup methods failed!');
  return false;
}

async function startCompiledUltraMinimal() {
  const compiledPath = path.join(__dirname, 'dist/apps/api/src/main-ultra-minimal.js');
  
  if (!fs.existsSync(compiledPath)) {
    throw new Error('Compiled ultra minimal file not found');
  }
  
  console.log('   Loading compiled ultra minimal module...');
  require(compiledPath);
  
  // Wait a bit to see if it starts successfully
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test if server is responding
  try {
    const response = await fetch(`http://localhost:${process.env.PORT}/health`);
    if (response.ok) {
      console.log('   ✅ Server is responding to health checks');
      return true;
    }
  } catch (fetchError) {
    throw new Error('Server not responding to health checks');
  }
  
  return false;
}

async function startSourceUltraMinimal() {
  const sourcePath = path.join(__dirname, 'src/main-ultra-minimal.ts');
  
  if (!fs.existsSync(sourcePath)) {
    throw new Error('Source ultra minimal file not found');
  }
  
  console.log('   Registering ts-node...');
  require('ts-node/register');
  
  console.log('   Loading source ultra minimal module...');
  require(sourcePath);
  
  // Wait a bit to see if it starts successfully
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test if server is responding
  try {
    const response = await fetch(`http://localhost:${process.env.PORT}/health`);
    if (response.ok) {
      console.log('   ✅ Server is responding to health checks');
      return true;
    }
  } catch (fetchError) {
    throw new Error('Server not responding to health checks');
  }
  
  return false;
}

async function startCompiledMinimal() {
  const compiledPath = path.join(__dirname, 'dist/apps/api/src/main-minimal.js');
  
  if (!fs.existsSync(compiledPath)) {
    throw new Error('Compiled minimal file not found');
  }
  
  console.log('   Loading compiled minimal module...');
  require(compiledPath);
  
  // Wait a bit to see if it starts successfully
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test if server is responding
  try {
    const response = await fetch(`http://localhost:${process.env.PORT}/health`);
    if (response.ok) {
      console.log('   ✅ Server is responding to health checks');
      return true;
    }
  } catch (fetchError) {
    throw new Error('Server not responding to health checks');
  }
  
  return false;
}

async function startSourceMinimal() {
  const sourcePath = path.join(__dirname, 'src/main-minimal.ts');
  
  if (!fs.existsSync(sourcePath)) {
    throw new Error('Source minimal file not found');
  }
  
  console.log('   Registering ts-node...');
  require('ts-node/register');
  
  console.log('   Loading source minimal module...');
  require(sourcePath);
  
  // Wait a bit to see if it starts successfully
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test if server is responding
  try {
    const response = await fetch(`http://localhost:${process.env.PORT}/health`);
    if (response.ok) {
      console.log('   ✅ Server is responding to health checks');
      return true;
    }
  } catch (fetchError) {
    throw new Error('Server not responding to health checks');
  }
  
  return false;
}

async function buildAndStart() {
  console.log('   Building project...');
  
  try {
    execSync('npm run build', { 
      stdio: 'pipe',
      cwd: __dirname 
    });
    console.log('   ✅ Build completed');
  } catch (buildError) {
    console.log('   ❌ npm run build failed, trying tsc...');
    
    try {
      execSync('npx tsc', { 
        stdio: 'pipe',
        cwd: __dirname 
      });
      console.log('   ✅ TypeScript compilation completed');
    } catch (tscError) {
      throw new Error('Both npm run build and tsc failed');
    }
  }
  
  // Now try to start the compiled version
  return await startCompiledUltraMinimal();
}

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('\n💥 Uncaught Exception:', error.message);
  console.error('Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n💥 Unhandled Rejection at:', promise, 'reason:', reason);
});

// Start the process
async function main() {
  console.log('\n🔍 Pre-flight checks...');
  
  // Check if node_modules exists
  if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    console.error('❌ node_modules not found. Please run: npm install');
    process.exit(1);
  }
  
  console.log('✅ node_modules found');
  
  // Check if required files exist
  const requiredFiles = [
    'src/main-ultra-minimal.ts',
    'src/app-ultra-minimal.module.ts',
    'package.json'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, file))) {
      console.error(`❌ Required file missing: ${file}`);
      process.exit(1);
    }
  }
  
  console.log('✅ Required files found');
  
  // Try startup methods
  const success = await tryStartupMethods();
  
  if (success) {
    console.log('\n🎉 Server is running successfully!');
    console.log(`🌐 URL: http://localhost:${process.env.PORT}`);
    console.log(`❤️ Health: http://localhost:${process.env.PORT}/health`);
    console.log(`📚 Docs: http://localhost:${process.env.PORT}/api`);
    console.log('\nPress Ctrl+C to stop the server');
    
    // Keep the process alive
    process.stdin.resume();
  } else {
    console.log('\n💥 Failed to start server with any method');
    console.log('\n🆘 Manual troubleshooting steps:');
    console.log('1. npm install');
    console.log('2. npm run build');
    console.log('3. node fix-all-errors.js');
    console.log('4. Check Node.js version (should be v16+)');
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('\n💥 Main process failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
