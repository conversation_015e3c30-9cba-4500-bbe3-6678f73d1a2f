// Simple test to check if the main modules can be imported
const { NestFactory } = require('@nestjs/core');

async function testCompile() {
  try {
    console.log('Testing basic imports...');
    
    // Test if we can import the main app module
    const { AppModule } = require('./src/app.module');
    console.log('✅ AppModule imported successfully');
    
    // Test if we can import analysis module
    const { AnalysisModule } = require('./src/analysis/analysis.module');
    console.log('✅ AnalysisModule imported successfully');
    
    // Test if we can import cache module
    const { CacheModule } = require('./src/cache/cache.module');
    console.log('✅ CacheModule imported successfully');
    
    // Test if we can import queue module
    const { QueueModule } = require('./src/queue/queue.module');
    console.log('✅ QueueModule imported successfully');
    
    // Test if we can import third-party module
    const { ThirdPartyModule } = require('./src/third-party/third-party.module');
    console.log('✅ ThirdPartyModule imported successfully');
    
    console.log('🎉 All modules imported successfully!');
    
  } catch (error) {
    console.error('❌ Error importing modules:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

testCompile();
