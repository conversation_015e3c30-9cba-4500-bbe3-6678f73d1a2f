#!/usr/bin/env node

/**
 * Simple runner script for RankMesh Backend
 * This is the easiest way to start your server
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 RankMesh Backend - Simple Runner\n');

// Check if we're in the right directory
const currentDir = process.cwd();
const expectedFiles = ['package.json', 'src', 'tsconfig.json'];

for (const file of expectedFiles) {
  if (!fs.existsSync(path.join(currentDir, file))) {
    console.error(`❌ Error: ${file} not found in current directory`);
    console.error('Please run this script from the apps/api directory');
    console.error('\nCorrect usage:');
    console.error('  cd apps/api');
    console.error('  node run.js');
    process.exit(1);
  }
}

console.log('✅ Directory check passed');

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('\n🌍 Environment:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`   PORT: ${process.env.PORT}`);
console.log(`   DB_ENABLED: ${process.env.DB_ENABLED}`);
console.log(`   MOCK_DATA: ${process.env.ENABLE_MOCK_DATA}`);

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
  console.log('\n📦 Installing dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.error('❌ Failed to install dependencies');
    console.error('Please run: npm install');
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies found');
}

// Try to start the server using the best available method
console.log('\n🚀 Starting server...');

async function startServer() {
  // Method 1: Try the ultimate startup script
  if (fs.existsSync('start-server.js')) {
    console.log('Using ultimate startup script...');
    try {
      require('./start-server.js');
      return;
    } catch (error) {
      console.log('❌ Ultimate startup failed, trying alternative...');
    }
  }

  // Method 2: Try the minimal startup script
  if (fs.existsSync('start-minimal.js')) {
    console.log('Using minimal startup script...');
    try {
      require('./start-minimal.js');
      return;
    } catch (error) {
      console.log('❌ Minimal startup failed, trying direct approach...');
    }
  }

  // Method 3: Direct approach
  console.log('Using direct approach...');
  try {
    // Set up environment
    require('reflect-metadata');
    
    // Try compiled version first
    let mainModule;
    try {
      mainModule = './dist/apps/api/src/main-ultra-minimal.js';
      if (fs.existsSync(mainModule)) {
        require(mainModule);
        return;
      }
    } catch (compiledError) {
      // Try source version
      try {
        require('ts-node/register');
        mainModule = './src/main-ultra-minimal.ts';
        require(mainModule);
        return;
      } catch (sourceError) {
        throw new Error('Both compiled and source startup failed');
      }
    }
  } catch (error) {
    console.error('\n💥 All startup methods failed!');
    console.error('Error:', error.message);
    
    console.log('\n🆘 Manual steps to try:');
    console.log('1. npm install');
    console.log('2. npm run build');
    console.log('3. node fix-all-errors.js');
    console.log('4. Check Node.js version: node --version (should be v16+)');
    
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n👋 Shutting down server...');
  process.exit(0);
});

// Start the server
startServer().catch((error) => {
  console.error('\n💥 Failed to start server:', error.message);
  process.exit(1);
});

// Keep the process alive
process.stdin.resume();
