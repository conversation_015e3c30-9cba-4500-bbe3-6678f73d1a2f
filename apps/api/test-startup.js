// Simple startup test to check for basic compilation issues
const { execSync } = require('child_process');

console.log('Testing TypeScript compilation...');

try {
  // Test TypeScript compilation
  execSync('npx tsc --noEmit --skipLibCheck', { 
    stdio: 'inherit',
    cwd: __dirname 
  });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.error('❌ TypeScript compilation failed');
  process.exit(1);
}

console.log('✅ All basic checks passed');
