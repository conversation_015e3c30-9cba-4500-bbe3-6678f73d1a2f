#!/usr/bin/env node

/**
 * Test script to run the compiled server and capture any errors
 */

console.log('🧪 Testing Compiled Server Startup...\n');

// Set environment variables for testing
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_USERNAME = 'postgres';
process.env.DB_PASSWORD = 'password';
process.env.DB_DATABASE = 'backlink_test';
process.env.JWT_SECRET = 'test-jwt-secret';

console.log('📋 Environment Variables Set:');
console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`- PORT: ${process.env.PORT}`);
console.log(`- DB_HOST: ${process.env.DB_HOST}`);
console.log(`- JWT_SECRET: ${process.env.JWT_SECRET ? 'Set' : 'Not Set'}`);

console.log('\n🚀 Attempting to start server...\n');

try {
  // Try to require the compiled main.js
  console.log('Loading compiled application...');
  require('./dist/apps/api/src/main.js');
  
  console.log('✅ Server started successfully!');
  
  // Give it a moment to fully initialize
  setTimeout(() => {
    console.log('\n🎉 Server is running and ready!');
    console.log('Visit: http://localhost:3001');
    console.log('API Docs: http://localhost:3001/api/docs');
    
    // Don't exit - let the server run
  }, 2000);
  
} catch (error) {
  console.error('\n❌ Server startup failed!');
  console.error('Error Type:', error.constructor.name);
  console.error('Error Message:', error.message);
  
  if (error.stack) {
    console.error('\nStack Trace:');
    console.error(error.stack);
  }
  
  console.error('\n🔍 Troubleshooting Tips:');
  
  if (error.message.includes('Cannot find module')) {
    console.error('- Missing dependency. Run: npm install');
  }
  
  if (error.message.includes('ECONNREFUSED') || error.message.includes('database')) {
    console.error('- Database connection issue. Check PostgreSQL is running or disable DB in config');
  }
  
  if (error.message.includes('EADDRINUSE')) {
    console.error('- Port 3001 is already in use. Change PORT environment variable');
  }
  
  if (error.message.includes('Redis') || error.message.includes('ECONNREFUSED')) {
    console.error('- Redis connection issue. Check Redis is running or disable Redis in config');
  }
  
  console.error('\n💡 Quick Fixes:');
  console.error('1. Make sure PostgreSQL is running (or comment out DB config)');
  console.error('2. Make sure Redis is running (or use in-memory cache)');
  console.error('3. Check if port 3001 is available');
  console.error('4. Verify all dependencies are installed');
  
  process.exit(1);
}
