#!/usr/bin/env node

/**
 * Comprehensive script to fix common issues and start the RankMesh backend server
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🔧 RankMesh Backend - Fix and Start Script\n');

// Step 1: Check and fix environment
console.log('1️⃣ Checking Environment...');

// Check if .env.development exists
const envPath = path.join(__dirname, '.env.development');
if (!fs.existsSync(envPath)) {
  console.log('❌ .env.development missing - creating default...');
  
  const defaultEnv = `# Development Environment Configuration
NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=backlink_dev
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_EXPIRES_IN=7d
REDIS_HOST=localhost
REDIS_PORT=6379
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000
AHREFS_RATE_LIMIT=100
SIMILARWEB_RATE_LIMIT=1000
WHOISXML_RATE_LIMIT=1000
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=debug
ENABLE_MOCK_DATA=true`;

  fs.writeFileSync(envPath, defaultEnv);
  console.log('✅ Created .env.development with defaults');
} else {
  console.log('✅ .env.development exists');
}

// Step 2: Check dependencies
console.log('\n2️⃣ Checking Dependencies...');

const packageJsonPath = path.join(__dirname, 'package.json');
const nodeModulesPath = path.join(__dirname, 'node_modules');

if (!fs.existsSync(nodeModulesPath)) {
  console.log('❌ node_modules missing - dependencies need to be installed');
  console.log('Please run: npm install');
  process.exit(1);
} else {
  console.log('✅ node_modules exists');
}

// Step 3: Check if compilation is needed
console.log('\n3️⃣ Checking Compilation...');

const distPath = path.join(__dirname, 'dist');
const mainJsPath = path.join(__dirname, 'dist/apps/api/src/main.js');
const minimalJsPath = path.join(__dirname, 'dist/apps/api/src/main-minimal.js');

if (!fs.existsSync(distPath) || !fs.existsSync(mainJsPath)) {
  console.log('❌ Compiled files missing - compilation needed');
  console.log('Building project...');
  
  const buildProcess = spawn('npm', ['run', 'build'], {
    stdio: 'inherit',
    cwd: __dirname
  });
  
  buildProcess.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Build successful');
      startServer();
    } else {
      console.log('❌ Build failed');
      console.log('Try running manually: npm run build');
      process.exit(1);
    }
  });
  
  buildProcess.on('error', (err) => {
    console.log('❌ Build process failed:', err.message);
    console.log('Trying alternative approach...');
    startServerDirect();
  });
  
} else {
  console.log('✅ Compiled files exist');
  startServer();
}

function startServer() {
  console.log('\n4️⃣ Starting Server...');
  
  // Try minimal version first (fewer dependencies)
  if (fs.existsSync(minimalJsPath)) {
    console.log('🚀 Starting in minimal mode (fewer dependencies)...');
    startServerFile(minimalJsPath);
  } else if (fs.existsSync(mainJsPath)) {
    console.log('🚀 Starting full server...');
    startServerFile(mainJsPath);
  } else {
    console.log('❌ No compiled main files found');
    startServerDirect();
  }
}

function startServerFile(filePath) {
  // Set environment variables
  process.env.NODE_ENV = 'development';
  process.env.PORT = '3001';
  
  try {
    console.log(`Loading: ${filePath}`);
    require(filePath);
    
    setTimeout(() => {
      console.log('\n🎉 Server should be running!');
      console.log('🌐 Visit: http://localhost:3001');
      console.log('📚 API Docs: http://localhost:3001/api/docs');
      console.log('❤️ Health: http://localhost:3001/health');
      console.log('\nPress Ctrl+C to stop the server');
    }, 2000);
    
  } catch (error) {
    console.error('\n❌ Server failed to start:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Database connection failed - trying without database...');
      startServerDirect();
    } else if (error.message.includes('EADDRINUSE')) {
      console.log('\n💡 Port 3001 in use - trying port 3002...');
      process.env.PORT = '3002';
      startServerFile(filePath);
    } else {
      console.error('Stack:', error.stack);
      startServerDirect();
    }
  }
}

function startServerDirect() {
  console.log('\n5️⃣ Trying direct TypeScript execution...');
  
  const tsNodeProcess = spawn('npx', ['ts-node', 'src/main-minimal.ts'], {
    stdio: 'inherit',
    cwd: __dirname,
    env: {
      ...process.env,
      NODE_ENV: 'development',
      PORT: '3001'
    }
  });
  
  tsNodeProcess.on('error', (err) => {
    console.error('❌ ts-node failed:', err.message);
    console.log('\n🆘 Manual steps to try:');
    console.log('1. npm install');
    console.log('2. npm run build');
    console.log('3. npm run dev:minimal');
    console.log('4. Check if PostgreSQL/Redis are running (or disable them)');
  });
  
  tsNodeProcess.on('close', (code) => {
    if (code !== 0) {
      console.log('\n❌ Server stopped with code:', code);
    }
  });
}
