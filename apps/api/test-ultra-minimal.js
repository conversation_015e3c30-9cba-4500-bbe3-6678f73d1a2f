#!/usr/bin/env node

/**
 * Test script specifically for the ultra-minimal configuration
 * This will identify the exact errors you're seeing
 */

console.log('🔍 Testing Ultra-Minimal Configuration\n');

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  DB_ENABLED: process.env.DB_ENABLED,
  MOCK_DATA: process.env.ENABLE_MOCK_DATA
});

console.log('\n1️⃣ Testing Basic Imports...');

// Test 1: reflect-metadata
try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata loaded');
} catch (error) {
  console.error('❌ reflect-metadata failed:', error.message);
  process.exit(1);
}

// Test 2: NestJS core
try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core loaded');
} catch (error) {
  console.error('❌ @nestjs/core failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

// Test 3: NestJS common
try {
  const { ValidationPipe } = require('@nestjs/common');
  console.log('✅ @nestjs/common loaded');
} catch (error) {
  console.error('❌ @nestjs/common failed:', error.message);
  process.exit(1);
}

// Test 4: NestJS config
try {
  const { ConfigModule } = require('@nestjs/config');
  console.log('✅ @nestjs/config loaded');
} catch (error) {
  console.error('❌ @nestjs/config failed:', error.message);
  process.exit(1);
}

console.log('\n2️⃣ Testing Individual Files...');

// Test app service
try {
  require('ts-node/register');
  const { AppService } = require('./src/app.service');
  console.log('✅ AppService loaded');
} catch (error) {
  console.error('❌ AppService failed:', error.message);
  console.error('Stack:', error.stack);
}

// Test app controller
try {
  const { AppController } = require('./src/app.controller');
  console.log('✅ AppController loaded');
} catch (error) {
  console.error('❌ AppController failed:', error.message);
  console.error('Stack:', error.stack);
}

// Test ultra minimal module
try {
  const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
  console.log('✅ AppUltraMinimalModule loaded');
} catch (error) {
  console.error('❌ AppUltraMinimalModule failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

console.log('\n3️⃣ Testing Application Creation...');

async function testAppCreation() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');

    console.log('Creating NestJS application...');
    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: false,
    });

    console.log('✅ Application created successfully');

    // Test basic configuration
    app.enableCors();
    console.log('✅ CORS enabled');

    await app.close();
    console.log('✅ Application closed cleanly');

    return true;
  } catch (error) {
    console.error('❌ Application creation failed:', error.message);
    console.error('Stack:', error.stack);
    
    // Detailed error analysis
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('\n🔍 Dependency Injection Error Details:');
      console.error('This usually means a service constructor has an unresolvable dependency');
      console.error('Check that all @Injectable() decorators are present');
      console.error('Check that all dependencies are properly imported in the module');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.error('\n🔍 Circular Dependency Error Details:');
      console.error('Two or more modules are importing each other');
      console.error('Use forwardRef() or restructure the modules');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('\n🔍 Module Not Found Error Details:');
      console.error('A required module or file is missing');
      console.error('Check import paths and ensure all files exist');
    }
    
    return false;
  }
}

console.log('\n4️⃣ Testing Server Startup...');

async function testServerStartup() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { ValidationPipe } = require('@nestjs/common');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');

    console.log('Creating application for server test...');
    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: ['error', 'warn'],
    });

    // Configure app
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    });

    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );

    const port = process.env.PORT || 3001;
    console.log(`Starting server on port ${port}...`);
    
    await app.listen(port);

    console.log(`✅ Server started successfully on port ${port}!`);
    console.log(`🌐 URL: http://localhost:${port}`);
    console.log(`❤️ Health: http://localhost:${port}/health`);

    // Test health endpoint
    setTimeout(async () => {
      try {
        const response = await fetch(`http://localhost:${port}/health`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health endpoint working:', data);
        } else {
          console.log('❌ Health endpoint returned error:', response.status);
        }
      } catch (fetchError) {
        console.log('⚠️ Could not test health endpoint:', fetchError.message);
      }

      console.log('\n🎉 SUCCESS! Ultra-minimal server is working!');
      console.log('Press Ctrl+C to stop the server');
    }, 2000);

    return true;
  } catch (error) {
    console.error('❌ Server startup failed:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Run all tests
async function runTests() {
  try {
    console.log('\n🧪 Running Application Creation Test...');
    const appCreationSuccess = await testAppCreation();

    if (appCreationSuccess) {
      console.log('\n🚀 Application creation successful! Starting server...');
      await testServerStartup();
    } else {
      console.log('\n💥 Application creation failed. Cannot start server.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Shutting down...');
  process.exit(0);
});

runTests();
