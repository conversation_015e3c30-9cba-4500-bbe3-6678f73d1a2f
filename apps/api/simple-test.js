#!/usr/bin/env node

/**
 * Simple test to identify the exact error
 */

console.log('🔍 Simple Error Test\n');

// Set environment
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.DB_ENABLED = 'false';

console.log('Step 1: Loading reflect-metadata...');
try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata loaded');
} catch (error) {
  console.error('❌ reflect-metadata failed:', error.message);
  process.exit(1);
}

console.log('\nStep 2: Loading NestJS core...');
try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ NestJS core loaded');
} catch (error) {
  console.error('❌ NestJS core failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

console.log('\nStep 3: Registering ts-node...');
try {
  require('ts-node/register');
  console.log('✅ ts-node registered');
} catch (error) {
  console.error('❌ ts-node failed:', error.message);
  process.exit(1);
}

console.log('\nStep 4: Loading AppService...');
try {
  const { AppService } = require('./src/app.service');
  console.log('✅ AppService loaded');
} catch (error) {
  console.error('❌ AppService failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

console.log('\nStep 5: Loading AppController...');
try {
  const { AppController } = require('./src/app.controller');
  console.log('✅ AppController loaded');
} catch (error) {
  console.error('❌ AppController failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

console.log('\nStep 6: Loading AppUltraMinimalModule...');
try {
  const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
  console.log('✅ AppUltraMinimalModule loaded');
} catch (error) {
  console.error('❌ AppUltraMinimalModule failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

console.log('\nStep 7: Creating NestJS application...');
async function createApp() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
    
    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: ['error', 'warn', 'log'],
    });
    
    console.log('✅ NestJS application created successfully!');
    
    // Configure CORS
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    });
    
    console.log('✅ CORS configured');
    
    // Start the server
    const port = process.env.PORT || 3001;
    await app.listen(port);
    
    console.log(`\n🎉 SUCCESS! Server is running on http://localhost:${port}`);
    console.log(`❤️ Health check: http://localhost:${port}/health`);
    console.log(`🏠 Home: http://localhost:${port}/`);
    
    // Test the health endpoint
    setTimeout(async () => {
      try {
        const response = await fetch(`http://localhost:${port}/health`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health endpoint test passed:', data);
        } else {
          console.log('❌ Health endpoint returned error:', response.status);
        }
      } catch (fetchError) {
        console.log('⚠️ Could not test health endpoint (might still be starting)');
      }
    }, 2000);
    
  } catch (error) {
    console.error('\n❌ Application creation failed:', error.message);
    console.error('Error type:', error.constructor.name);
    console.error('Stack:', error.stack);
    
    // Provide specific guidance
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('\n🔍 DEPENDENCY INJECTION ERROR:');
      console.error('- A service constructor has an unresolvable dependency');
      console.error('- Check @Injectable() decorators');
      console.error('- Check module providers array');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.error('\n🔍 CIRCULAR DEPENDENCY ERROR:');
      console.error('- Modules are importing each other in a loop');
      console.error('- Use forwardRef() or restructure modules');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('\n🔍 MODULE NOT FOUND ERROR:');
      console.error('- Import path is incorrect');
      console.error('- File does not exist');
      console.error('- Check for typos');
    }
    
    process.exit(1);
  }
}

createApp();
