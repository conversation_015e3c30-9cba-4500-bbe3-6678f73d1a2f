#!/usr/bin/env node

/**
 * Error detection script - this will show you the ACTUAL errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 RankMesh Backend - Error Detection\n');

// Check current directory
console.log('Current directory:', process.cwd());
console.log('Script location:', __dirname);

// Check if we're in the right place
const requiredFiles = ['package.json', 'src', 'tsconfig.json'];
for (const file of requiredFiles) {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file} found`);
  } else {
    console.log(`❌ ${file} NOT found`);
  }
}

console.log('\n1️⃣ Checking TypeScript Compilation...');

try {
  console.log('Running: npx tsc --noEmit --skipLibCheck');
  const output = execSync('npx tsc --noEmit --skipLibCheck', { 
    encoding: 'utf8',
    cwd: __dirname,
    stdio: 'pipe'
  });
  console.log('✅ TypeScript compilation successful');
  if (output.trim()) {
    console.log('Output:', output);
  }
} catch (error) {
  console.log('❌ TypeScript compilation failed:');
  console.log('STDERR:', error.stderr);
  console.log('STDOUT:', error.stdout);
  console.log('Status:', error.status);
}

console.log('\n2️⃣ Checking Node.js Module Loading...');

// Test basic Node.js module loading
try {
  console.log('Testing reflect-metadata...');
  require('reflect-metadata');
  console.log('✅ reflect-metadata loaded');
} catch (error) {
  console.log('❌ reflect-metadata failed:', error.message);
}

try {
  console.log('Testing @nestjs/core...');
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core loaded');
} catch (error) {
  console.log('❌ @nestjs/core failed:', error.message);
  console.log('Stack:', error.stack);
}

console.log('\n3️⃣ Testing TypeScript Module Loading...');

try {
  console.log('Registering ts-node...');
  require('ts-node/register');
  console.log('✅ ts-node registered');
} catch (error) {
  console.log('❌ ts-node registration failed:', error.message);
}

// Test individual TypeScript files
const testFiles = [
  './src/app.service.ts',
  './src/app.controller.ts',
  './src/app-ultra-minimal.module.ts'
];

for (const file of testFiles) {
  try {
    console.log(`Testing ${file}...`);
    const module = require(file);
    console.log(`✅ ${file} loaded successfully`);
    console.log(`   Exports:`, Object.keys(module));
  } catch (error) {
    console.log(`❌ ${file} failed:`, error.message);
    console.log('   Stack:', error.stack);
  }
}

console.log('\n4️⃣ Testing NestJS Application Creation...');

async function testNestApp() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
    
    console.log('Creating NestJS application...');
    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: false,
    });
    
    console.log('✅ NestJS application created successfully');
    
    await app.close();
    console.log('✅ Application closed successfully');
    
  } catch (error) {
    console.log('❌ NestJS application creation failed:', error.message);
    console.log('Stack:', error.stack);
    
    // Detailed error analysis
    if (error.message.includes('Cannot resolve dependency')) {
      console.log('\n🔍 DEPENDENCY INJECTION ERROR:');
      console.log('- Check that all services have @Injectable() decorator');
      console.log('- Check that all dependencies are provided in the module');
      console.log('- Check constructor parameter types');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.log('\n🔍 CIRCULAR DEPENDENCY ERROR:');
      console.log('- Two modules are importing each other');
      console.log('- Use forwardRef() or restructure modules');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.log('\n🔍 MODULE NOT FOUND ERROR:');
      console.log('- Check import paths');
      console.log('- Ensure all files exist');
      console.log('- Check for typos in file names');
    }
  }
}

console.log('\n5️⃣ Testing Package Dependencies...');

const criticalPackages = [
  '@nestjs/core',
  '@nestjs/common',
  '@nestjs/config',
  'reflect-metadata',
  'rxjs',
  'typescript',
  'ts-node'
];

for (const pkg of criticalPackages) {
  try {
    const packagePath = path.join(__dirname, 'node_modules', pkg);
    if (fs.existsSync(packagePath)) {
      console.log(`✅ ${pkg} installed`);
    } else {
      console.log(`❌ ${pkg} NOT installed`);
    }
  } catch (error) {
    console.log(`❌ ${pkg} check failed:`, error.message);
  }
}

console.log('\n6️⃣ Running NestJS Test...');
testNestApp().then(() => {
  console.log('\n🎉 All tests completed!');
}).catch((error) => {
  console.log('\n💥 Test execution failed:', error.message);
});

console.log('\n7️⃣ Environment Information...');
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);
console.log('Working directory:', process.cwd());

// Check package.json
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  console.log('Package name:', packageJson.name);
  console.log('Package version:', packageJson.version);
  console.log('Main dependencies:', Object.keys(packageJson.dependencies || {}).slice(0, 5));
} catch (error) {
  console.log('❌ Could not read package.json:', error.message);
}
