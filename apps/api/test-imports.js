// Test script to check if modules can be imported
console.log('Testing module imports...\n');

try {
  // Test reflect-metadata
  require('reflect-metadata');
  console.log('✅ reflect-metadata');
} catch (e) {
  console.log('❌ reflect-metadata:', e.message);
}

try {
  // Test NestJS core
  const { Module } = require('@nestjs/common');
  console.log('✅ @nestjs/common');
} catch (e) {
  console.log('❌ @nestjs/common:', e.message);
}

try {
  // Test TypeORM
  const { Entity } = require('typeorm');
  console.log('✅ typeorm');
} catch (e) {
  console.log('❌ typeorm:', e.message);
}

try {
  // Test config
  const { ConfigModule } = require('@nestjs/config');
  console.log('✅ @nestjs/config');
} catch (e) {
  console.log('❌ @nestjs/config:', e.message);
}

try {
  // Test axios
  const { HttpModule } = require('@nestjs/axios');
  console.log('✅ @nestjs/axios');
} catch (e) {
  console.log('❌ @nestjs/axios:', e.message);
}

try {
  // Test cache manager
  const { CacheModule } = require('@nestjs/cache-manager');
  console.log('✅ @nestjs/cache-manager');
} catch (e) {
  console.log('❌ @nestjs/cache-manager:', e.message);
}

console.log('\nTesting compiled modules...\n');

// Test if TypeScript files can be required (if compiled)
const fs = require('fs');
const path = require('path');

if (fs.existsSync(path.join(__dirname, 'dist'))) {
  console.log('✅ dist folder exists');
  
  try {
    const appModule = require('./dist/app.module.js');
    console.log('✅ AppModule compiled');
  } catch (e) {
    console.log('❌ AppModule compilation error:', e.message);
  }
} else {
  console.log('❌ dist folder not found - run build first');
}

console.log('\nDone testing imports.');
