#!/usr/bin/env node

/**
 * Debug script to capture exact startup errors
 */

console.log('🔍 Debug: Capturing startup errors...\n');

// Set basic environment
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment set:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- PORT:', process.env.PORT);
console.log('- DB_ENABLED:', process.env.DB_ENABLED);

console.log('\n📋 Testing basic imports...');

try {
  // Test reflect-metadata
  require('reflect-metadata');
  console.log('✅ reflect-metadata imported');
} catch (error) {
  console.error('❌ reflect-metadata failed:', error.message);
  process.exit(1);
}

try {
  // Test NestJS core
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core imported');
} catch (error) {
  console.error('❌ @nestjs/core failed:', error.message);
  process.exit(1);
}

try {
  // Test if we can import the app module
  console.log('\n📦 Testing app module import...');
  const { AppModule } = require('./src/app.module');
  console.log('✅ AppModule imported successfully');
} catch (error) {
  console.error('❌ AppModule import failed:', error.message);
  console.error('Stack trace:', error.stack);
  
  // Try to identify the specific module causing issues
  console.log('\n🔍 Testing individual modules...');
  
  const modules = [
    './src/cache/cache.module',
    './src/queue/queue.module',
    './src/third-party/third-party.module',
    './src/analysis/analysis.module',
    './src/auth/auth.module',
    './src/users/users.module',
    './src/domains/domains.module',
    './src/target-domains/target-domains.module',
    './src/emails/emails.module',
    './src/dashboard/dashboard.module'
  ];
  
  for (const modulePath of modules) {
    try {
      require(modulePath);
      console.log(`✅ ${modulePath}`);
    } catch (moduleError) {
      console.error(`❌ ${modulePath}: ${moduleError.message}`);
    }
  }
  
  process.exit(1);
}

try {
  // Test minimal app module
  console.log('\n📦 Testing minimal app module...');
  const { AppMinimalModule } = require('./src/app-minimal.module');
  console.log('✅ AppMinimalModule imported successfully');
} catch (error) {
  console.error('❌ AppMinimalModule import failed:', error.message);
}

console.log('\n🚀 Testing NestJS app creation...');

async function testAppCreation() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppMinimalModule } = require('./src/app-minimal.module');
    
    console.log('Creating NestJS application...');
    const app = await NestFactory.create(AppMinimalModule, {
      logger: ['error', 'warn'],
    });
    
    console.log('✅ App created successfully');
    
    console.log('Testing app initialization...');
    await app.init();
    console.log('✅ App initialized successfully');
    
    console.log('Testing app listen...');
    await app.listen(3001);
    console.log('✅ App listening on port 3001');
    
    console.log('\n🎉 SUCCESS! Server is running on http://localhost:3001');
    console.log('Press Ctrl+C to stop');
    
  } catch (error) {
    console.error('\n❌ App creation/startup failed:', error.message);
    console.error('Error type:', error.constructor.name);
    console.error('Stack trace:', error.stack);
    
    // Provide specific troubleshooting based on error type
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('\n💡 Dependency injection issue detected');
      console.error('This usually means a service is missing or has circular dependencies');
    }
    
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\n💡 Connection refused - likely database or Redis');
      console.error('Make sure DB_ENABLED=false in environment');
    }
    
    if (error.message.includes('EADDRINUSE')) {
      console.error('\n💡 Port already in use');
      console.error('Try a different port or kill the process using port 3001');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('\n💡 Missing module dependency');
      console.error('Run: npm install');
    }
    
    process.exit(1);
  }
}

testAppCreation();
