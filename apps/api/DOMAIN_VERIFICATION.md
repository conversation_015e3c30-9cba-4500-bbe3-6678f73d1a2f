# Domain Verification in Backlink

This document explains how domain verification works in the Backlink application and how to use the bypass feature for testing and development.

## Domain Verification Methods

The Backlink application supports three methods for verifying domain ownership:

1. **DNS TXT Record** - Add a TXT record to your domain's DNS settings
2. **Meta Tag** - Add a meta tag to your website's homepage
3. **File Upload** - Upload a verification file to your website's root directory

## Bypass for Testing and Development

For testing and development purposes, you can bypass the domain verification process in two ways:

1. **Environment Variable**: Set `BYPASS_DOMAIN_VERIFICATION=true` in your environment
2. **Bypass Code**: Set `DOMAIN_VERIFICATION_BYPASS_CODE=your_secret_code` in your environment, then use this code in any of the verification detail fields

## Verification Process

When a user adds a domain to their account, the system generates a unique verification code. The user must then prove ownership of the domain by adding this code using one of the verification methods above.

### DNS TXT Record Verification

1. Add a TXT record to your domain's DNS settings with the following configuration:
   - Name/Host: `_backlink-verify.yourdomain.com`
   - Value/Content: Your verification code

### Meta Tag Verification

1. Add the following meta tag to the `<head>` section of your website's homepage:
   ```html
   <meta name="backlink-verification" content="your-verification-code" />
   ```

### File Verification

1. Create a file named `backlink-verify.html` in your website's root directory
2. Add your verification code to the file content

## Bypassing Domain Verification for Testing/Development

For testing and development purposes, you can bypass the domain verification process by setting the `BYPASS_DOMAIN_VERIFICATION` environment variable to `true`.

### How to Enable Bypass Mode

1. Add the following to your `.env` file:

   ```
   BYPASS_DOMAIN_VERIFICATION=true
   ```

2. Restart the API server for the changes to take effect

### How Bypass Mode Works

When bypass mode is enabled:

- All domain verification requests will automatically succeed
- The verification method specified in the request will still be recorded
- The domain will be marked as verified with the current timestamp
- No actual verification checks will be performed

### Security Considerations

- **IMPORTANT**: Never enable bypass mode in production environments
- This feature is intended only for testing and development
- In production, always require proper domain verification to prevent unauthorized use

## Troubleshooting Verification Issues

If you're having trouble with domain verification:

1. Make sure you're using the correct verification code
2. Check that you've implemented the verification method correctly
3. DNS changes can take time to propagate (up to 48 hours in some cases)
4. For file verification, ensure the file is accessible at the root of your domain
5. For meta tag verification, make sure the tag is in the `<head>` section of your homepage

## API Endpoints

- `POST /domains/verify` - Verify domain ownership
  - Request body:
    ```json
    {
      "domain": "example.com",
      "verificationMethod": "dns" | "meta" | "file"
    }
    ```
  - Response: Domain object with `verified: true` if successful
