#!/usr/bin/env node

/**
 * Comprehensive test and startup script for RankMesh Backend
 * This script tests all components and starts the server if everything passes
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 RankMesh Backend - Comprehensive Test and Startup\n');

// Step 1: Environment Check
console.log('1️⃣ Environment Check...');

const requiredFiles = [
  'package.json',
  'tsconfig.json',
  'src/main-ultra-minimal.ts',
  'src/app-ultra-minimal.module.ts',
  'src/app.controller.ts',
  'src/app.service.ts',
  '.env.development'
];

let allFilesExist = true;
for (const file of requiredFiles) {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
}

if (!allFilesExist) {
  console.error('\n💥 Missing required files. Please check your project structure.');
  process.exit(1);
}

// Step 2: Dependencies Check
console.log('\n2️⃣ Dependencies Check...');

const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.error('❌ node_modules missing');
  console.log('\n💡 Please run: npm install');
  process.exit(1);
}
console.log('✅ node_modules exists');

// Check critical dependencies
const criticalDeps = [
  '@nestjs/core',
  '@nestjs/common',
  '@nestjs/config',
  'reflect-metadata',
  'rxjs'
];

for (const dep of criticalDeps) {
  const depPath = path.join(__dirname, 'node_modules', dep);
  if (fs.existsSync(depPath)) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
    console.log('\n💡 Please run: npm install');
    process.exit(1);
  }
}

// Step 3: Module Import Test
console.log('\n3️⃣ Module Import Test...');

try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata');
} catch (error) {
  console.error('❌ reflect-metadata:', error.message);
  process.exit(1);
}

try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core');
} catch (error) {
  console.error('❌ @nestjs/core:', error.message);
  process.exit(1);
}

// Try to import the ultra minimal module
try {
  // First try compiled version
  let moduleLoaded = false;
  try {
    const { AppUltraMinimalModule } = require('./dist/apps/api/src/app-ultra-minimal.module');
    console.log('✅ AppUltraMinimalModule (compiled)');
    moduleLoaded = true;
  } catch (compiledError) {
    // Try TypeScript source
    try {
      require('ts-node/register');
      const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module');
      console.log('✅ AppUltraMinimalModule (source)');
      moduleLoaded = true;
    } catch (sourceError) {
      console.error('❌ AppUltraMinimalModule:', sourceError.message);
      console.log('\n💡 Try running: npm run build');
    }
  }
  
  if (!moduleLoaded) {
    process.exit(1);
  }
} catch (error) {
  console.error('❌ Module import failed:', error.message);
  process.exit(1);
}

// Step 4: Environment Variables
console.log('\n4️⃣ Environment Variables...');

// Set defaults
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment configured:');
console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`- PORT: ${process.env.PORT}`);
console.log(`- DB_ENABLED: ${process.env.DB_ENABLED}`);
console.log(`- MOCK_DATA: ${process.env.ENABLE_MOCK_DATA}`);

// Step 5: Application Creation Test
console.log('\n5️⃣ Application Creation Test...');

async function testAppCreation() {
  try {
    const { NestFactory } = require('@nestjs/core');
    
    // Load module
    let AppModule;
    try {
      AppModule = require('./dist/apps/api/src/app-ultra-minimal.module').AppUltraMinimalModule;
    } catch (error) {
      require('ts-node/register');
      AppModule = require('./src/app-ultra-minimal.module').AppUltraMinimalModule;
    }

    console.log('Creating test application...');
    const app = await NestFactory.create(AppModule, {
      logger: false,
    });

    console.log('✅ Application created successfully');
    
    // Test basic configuration
    app.enableCors();
    console.log('✅ CORS enabled');
    
    await app.close();
    console.log('✅ Application closed cleanly');
    
    return true;
  } catch (error) {
    console.error('❌ Application creation failed:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Step 6: Start Server
async function startServer() {
  console.log('\n6️⃣ Starting Server...');
  
  try {
    const { NestFactory } = require('@nestjs/core');
    const { ValidationPipe } = require('@nestjs/common');
    
    // Load module
    let AppModule;
    try {
      AppModule = require('./dist/apps/api/src/app-ultra-minimal.module').AppUltraMinimalModule;
    } catch (error) {
      require('ts-node/register');
      AppModule = require('./src/app-ultra-minimal.module').AppUltraMinimalModule;
    }

    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log'],
    });

    // Configure app
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
      credentials: true,
    });

    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );

    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log('\n🎉 SUCCESS! Server is running!');
    console.log(`🌐 Server URL: http://localhost:${port}`);
    console.log(`❤️ Health Check: http://localhost:${port}/health`);
    console.log(`🏠 Home: http://localhost:${port}/`);
    console.log(`📚 API Docs: http://localhost:${port}/api`);
    console.log('\nPress Ctrl+C to stop the server');

    // Test endpoints after startup
    setTimeout(async () => {
      try {
        const response = await fetch(`http://localhost:${port}/health`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health endpoint working:', data);
        }
      } catch (fetchError) {
        console.log('⚠️ Could not test health endpoint');
      }
    }, 2000);

  } catch (error) {
    console.error('\n❌ Server startup failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Run all tests and start server
async function runAllTests() {
  try {
    const appCreationSuccess = await testAppCreation();
    
    if (appCreationSuccess) {
      console.log('\n✅ All tests passed! Starting server...');
      await startServer();
    } else {
      console.error('\n❌ Tests failed. Server will not start.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Unexpected error:', error.message);
    process.exit(1);
  }
}

runAllTests();
