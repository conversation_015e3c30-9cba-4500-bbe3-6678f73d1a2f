// Basic test to check if the application can be imported
console.log('Testing basic module imports...');

try {
  // Test if we can require the main modules
  console.log('✅ Testing basic Node.js functionality...');
  
  // Test if TypeScript compilation works
  const { execSync } = require('child_process');
  
  console.log('✅ Testing if main dependencies are available...');
  
  // Check if main packages are installed
  try {
    require('@nestjs/common');
    console.log('✅ @nestjs/common available');
  } catch (e) {
    console.log('❌ @nestjs/common not available:', e.message);
  }
  
  try {
    require('@nestjs/core');
    console.log('✅ @nestjs/core available');
  } catch (e) {
    console.log('❌ @nestjs/core not available:', e.message);
  }
  
  try {
    require('typeorm');
    console.log('✅ typeorm available');
  } catch (e) {
    console.log('❌ typeorm not available:', e.message);
  }
  
  try {
    require('bull');
    console.log('✅ bull available');
  } catch (e) {
    console.log('❌ bull not available:', e.message);
  }
  
  try {
    require('@nestjs/bull');
    console.log('✅ @nestjs/bull available');
  } catch (e) {
    console.log('❌ @nestjs/bull not available:', e.message);
  }
  
  try {
    require('@nestjs/terminus');
    console.log('✅ @nestjs/terminus available');
  } catch (e) {
    console.log('❌ @nestjs/terminus not available:', e.message);
  }
  
  console.log('✅ Basic dependency check completed');
  
} catch (error) {
  console.error('❌ Basic test failed:', error.message);
  process.exit(1);
}
