#!/usr/bin/env node

/**
 * Basic test - just try to import modules step by step
 */

console.log('🔍 Basic Import Test\n');

// Test 1: Basic Node.js modules
console.log('1️⃣ Testing basic Node.js modules...');
try {
  const fs = require('fs');
  const path = require('path');
  console.log('✅ fs and path modules work');
} catch (error) {
  console.error('❌ Basic Node.js modules failed:', error.message);
  process.exit(1);
}

// Test 2: Check if we're in the right directory
console.log('\n2️⃣ Checking directory...');
const fs = require('fs');
const path = require('path');

console.log('Current directory:', process.cwd());
console.log('Script directory:', __dirname);

const requiredFiles = ['package.json', 'src', 'tsconfig.json'];
for (const file of requiredFiles) {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file} exists`);
  } else {
    console.error(`❌ ${file} missing`);
  }
}

// Test 3: Check node_modules
console.log('\n3️⃣ Checking node_modules...');
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ node_modules exists');
  
  // Check critical packages
  const packages = ['@nestjs/core', '@nestjs/common', 'reflect-metadata', 'typescript', 'ts-node'];
  for (const pkg of packages) {
    const pkgPath = path.join(nodeModulesPath, pkg);
    if (fs.existsSync(pkgPath)) {
      console.log(`✅ ${pkg} installed`);
    } else {
      console.log(`❌ ${pkg} missing`);
    }
  }
} else {
  console.error('❌ node_modules missing - run npm install');
  process.exit(1);
}

// Test 4: Try to load reflect-metadata
console.log('\n4️⃣ Testing reflect-metadata...');
try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata loaded');
} catch (error) {
  console.error('❌ reflect-metadata failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

// Test 5: Try to load NestJS core
console.log('\n5️⃣ Testing @nestjs/core...');
try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core loaded');
  console.log('NestFactory type:', typeof NestFactory);
} catch (error) {
  console.error('❌ @nestjs/core failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

// Test 6: Try to load @nestjs/common
console.log('\n6️⃣ Testing @nestjs/common...');
try {
  const { Module, Injectable, Controller, Get } = require('@nestjs/common');
  console.log('✅ @nestjs/common loaded');
  console.log('Module type:', typeof Module);
  console.log('Injectable type:', typeof Injectable);
} catch (error) {
  console.error('❌ @nestjs/common failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

// Test 7: Try to register ts-node
console.log('\n7️⃣ Testing ts-node registration...');
try {
  require('ts-node/register');
  console.log('✅ ts-node registered');
} catch (error) {
  console.error('❌ ts-node registration failed:', error.message);
  console.error('Stack:', error.stack);
  // Don't exit here, we might still be able to use compiled JS
}

// Test 8: Try to load our TypeScript files
console.log('\n8️⃣ Testing our TypeScript files...');

// Test app.service.ts
try {
  console.log('Loading app.service.ts...');
  const { AppService } = require('./src/app.service.ts');
  console.log('✅ AppService loaded');
  console.log('AppService type:', typeof AppService);
} catch (error) {
  console.error('❌ AppService failed:', error.message);
  console.error('Stack:', error.stack);
}

// Test app.controller.ts
try {
  console.log('Loading app.controller.ts...');
  const { AppController } = require('./src/app.controller.ts');
  console.log('✅ AppController loaded');
  console.log('AppController type:', typeof AppController);
} catch (error) {
  console.error('❌ AppController failed:', error.message);
  console.error('Stack:', error.stack);
}

// Test app-ultra-minimal.module.ts
try {
  console.log('Loading app-ultra-minimal.module.ts...');
  const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module.ts');
  console.log('✅ AppUltraMinimalModule loaded');
  console.log('AppUltraMinimalModule type:', typeof AppUltraMinimalModule);
} catch (error) {
  console.error('❌ AppUltraMinimalModule failed:', error.message);
  console.error('Stack:', error.stack);
}

// Test 9: Try to create a NestJS application
console.log('\n9️⃣ Testing NestJS application creation...');
async function testApp() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppUltraMinimalModule } = require('./src/app-ultra-minimal.module.ts');
    
    console.log('Creating NestJS application...');
    const app = await NestFactory.create(AppUltraMinimalModule, {
      logger: false,
    });
    
    console.log('✅ NestJS application created successfully!');
    
    await app.close();
    console.log('✅ Application closed successfully');
    
    console.log('\n🎉 ALL TESTS PASSED! Your backend should work!');
    
  } catch (error) {
    console.error('\n❌ NestJS application creation failed:', error.message);
    console.error('Error type:', error.constructor.name);
    console.error('Stack:', error.stack);
    
    // Detailed error analysis
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('\n🔍 DEPENDENCY INJECTION ERROR:');
      console.error('- Check @Injectable() decorators');
      console.error('- Check module providers');
      console.error('- Check constructor parameters');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.error('\n🔍 CIRCULAR DEPENDENCY ERROR:');
      console.error('- Modules importing each other');
      console.error('- Use forwardRef() or restructure');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('\n🔍 MODULE NOT FOUND ERROR:');
      console.error('- Check import paths');
      console.error('- Check file existence');
    }
    
    console.error('\n💡 Try these fixes:');
    console.error('1. npm install');
    console.error('2. npm run build');
    console.error('3. Check Node.js version (should be v16+)');
  }
}

// Run the test
testApp().catch((error) => {
  console.error('\n💥 Test execution failed:', error.message);
});

console.log('\n📊 Test Summary:');
console.log('- Basic modules: ✅');
console.log('- Directory structure: ✅');
console.log('- Dependencies: ✅');
console.log('- NestJS core: ✅');
console.log('- TypeScript files: Testing...');
console.log('- Application creation: Testing...');

console.log('\nNode.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);
