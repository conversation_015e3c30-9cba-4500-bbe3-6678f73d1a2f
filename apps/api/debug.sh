#!/bin/bash

echo "🔍 Debug Script - Finding the Real Errors"
echo "=========================================="

# Set working directory
cd "$(dirname "$0")"
echo "Working directory: $(pwd)"

# Check Node.js
echo ""
echo "1️⃣ Node.js Check:"
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"

# Check if node_modules exists
echo ""
echo "2️⃣ Dependencies Check:"
if [ -d "node_modules" ]; then
    echo "✅ node_modules exists"
    echo "Critical packages:"
    for pkg in "@nestjs/core" "@nestjs/common" "@nestjs/config" "reflect-metadata" "rxjs" "typescript" "ts-node"; do
        if [ -d "node_modules/$pkg" ]; then
            echo "✅ $pkg"
        else
            echo "❌ $pkg MISSING"
        fi
    done
else
    echo "❌ node_modules missing - running npm install..."
    npm install
fi

# Check TypeScript compilation
echo ""
echo "3️⃣ TypeScript Compilation Check:"
echo "Running: npx tsc --noEmit --skipLibCheck"
npx tsc --noEmit --skipLibCheck 2>&1 | head -20

# Check if ultra minimal files exist
echo ""
echo "4️⃣ File Structure Check:"
files=(
    "src/app.service.ts"
    "src/app.controller.ts"
    "src/app-ultra-minimal.module.ts"
    "src/main-ultra-minimal.ts"
    "tsconfig.json"
    "package.json"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file MISSING"
    fi
done

# Try to run the simple test
echo ""
echo "5️⃣ Simple Test Run:"
echo "Running: node simple-test.js"
timeout 30s node simple-test.js 2>&1 | head -50

echo ""
echo "6️⃣ Alternative: Try ts-node directly:"
echo "Running: npx ts-node src/main-ultra-minimal.ts"
timeout 30s npx ts-node src/main-ultra-minimal.ts 2>&1 | head -50

echo ""
echo "Debug complete!"
