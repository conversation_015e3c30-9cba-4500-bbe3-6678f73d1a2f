#!/usr/bin/env node

/**
 * Test script to identify specific startup errors
 */

console.log('🔍 Testing Backend Startup - Error Detection\n');

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.DB_ENABLED = 'false';
process.env.ENABLE_MOCK_DATA = 'true';

console.log('Environment configured:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- PORT:', process.env.PORT);
console.log('- DB_ENABLED:', process.env.DB_ENABLED);

// Test 1: Basic imports
console.log('\n1️⃣ Testing Basic Imports...');

try {
  require('reflect-metadata');
  console.log('✅ reflect-metadata');
} catch (error) {
  console.error('❌ reflect-metadata:', error.message);
  process.exit(1);
}

try {
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core');
} catch (error) {
  console.error('❌ @nestjs/core:', error.message);
  process.exit(1);
}

// Test 2: Individual module imports
console.log('\n2️⃣ Testing Individual Modules...');

const modules = [
  { name: 'CacheModule', path: './src/cache/cache.module' },
  { name: 'QueueModule', path: './src/queue/queue.module' },
  { name: 'ThirdPartyModule', path: './src/third-party/third-party.module' },
  { name: 'AhrefsService', path: './src/third-party/ahrefs/ahrefs.service' },
  { name: 'SimilarWebService', path: './src/third-party/similarweb/similarweb.service' },
  { name: 'WhoisXmlService', path: './src/third-party/whoisxml/whoisxml.service' },
  { name: 'AppMinimalModule', path: './src/app-minimal.module' },
  { name: 'AppModule', path: './src/app.module' },
];

for (const module of modules) {
  try {
    require(module.path);
    console.log(`✅ ${module.name}`);
  } catch (error) {
    console.error(`❌ ${module.name}: ${error.message}`);
    if (error.stack) {
      console.error('   Stack:', error.stack.split('\n')[1]?.trim());
    }
  }
}

// Test 3: Try to create NestJS app
console.log('\n3️⃣ Testing NestJS App Creation...');

async function testAppCreation() {
  try {
    const { NestFactory } = require('@nestjs/core');
    
    // Try minimal module first
    try {
      const { AppMinimalModule } = require('./src/app-minimal.module');
      console.log('Creating app with minimal module...');
      
      const app = await NestFactory.create(AppMinimalModule, {
        logger: false,
      });
      
      console.log('✅ Minimal app created successfully');
      await app.close();
      
    } catch (minimalError) {
      console.error('❌ Minimal app creation failed:', minimalError.message);
      
      // Try full module
      try {
        const { AppModule } = require('./src/app.module');
        console.log('Creating app with full module...');
        
        const app = await NestFactory.create(AppModule, {
          logger: false,
        });
        
        console.log('✅ Full app created successfully');
        await app.close();
        
      } catch (fullError) {
        console.error('❌ Full app creation failed:', fullError.message);
        throw fullError;
      }
    }
    
  } catch (error) {
    console.error('\n💥 App creation completely failed:');
    console.error('Error:', error.message);
    console.error('Type:', error.constructor.name);
    
    if (error.stack) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }
    
    // Provide specific guidance based on error type
    console.error('\n🔧 Troubleshooting suggestions:');
    
    if (error.message.includes('Cannot resolve dependency')) {
      console.error('- Dependency injection issue: Check service constructors and providers');
    }
    
    if (error.message.includes('Cannot find module')) {
      console.error('- Missing module: Run npm install');
    }
    
    if (error.message.includes('Circular dependency')) {
      console.error('- Circular dependency: Check module imports');
    }
    
    if (error.message.includes('ECONNREFUSED')) {
      console.error('- Connection refused: Database or Redis not available');
    }
    
    process.exit(1);
  }
}

// Test 4: Try to start server
console.log('\n4️⃣ Testing Server Startup...');

async function testServerStartup() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppMinimalModule } = require('./src/app-minimal.module');
    
    console.log('Creating and starting server...');
    
    const app = await NestFactory.create(AppMinimalModule, {
      logger: ['error', 'warn'],
    });
    
    // Enable CORS
    app.enableCors();
    
    // Start listening
    await app.listen(3001);
    
    console.log('✅ Server started successfully on port 3001!');
    console.log('🌐 Visit: http://localhost:3001');
    
    // Test health endpoint
    setTimeout(async () => {
      try {
        const response = await fetch('http://localhost:3001/health');
        if (response.ok) {
          console.log('✅ Health endpoint working');
        } else {
          console.log('❌ Health endpoint failed');
        }
      } catch (fetchError) {
        console.log('❌ Could not test health endpoint:', fetchError.message);
      }
      
      console.log('\n🎉 All tests passed! Server is working.');
      console.log('Press Ctrl+C to stop the server');
    }, 2000);
    
  } catch (error) {
    console.error('\n💥 Server startup failed:');
    console.error('Error:', error.message);
    console.error('Type:', error.constructor.name);
    
    if (error.stack) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }
    
    process.exit(1);
  }
}

// Run tests
async function runAllTests() {
  try {
    await testAppCreation();
    await testServerStartup();
  } catch (error) {
    console.error('\n💥 Tests failed:', error.message);
    process.exit(1);
  }
}

runAllTests();
