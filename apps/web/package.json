{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fastify/static": "latest", "@google-cloud/spanner": "latest", "@hookform/resolvers": "^3.9.1", "@nestjs/axios": "latest", "@nestjs/common": "latest", "@nestjs/config": "latest", "@nestjs/core": "latest", "@nestjs/jwt": "latest", "@nestjs/microservices": "latest", "@nestjs/passport": "latest", "@nestjs/platform-express": "latest", "@nestjs/schedule": "latest", "@nestjs/swagger": "latest", "@nestjs/throttler": "latest", "@nestjs/typeorm": "latest", "@nestjs/websockets": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reduxjs/toolkit": "^2.8.2", "@sap/hana-client": "latest", "autoprefixer": "^10.4.20", "axios": "latest", "bcryptjs": "latest", "better-sqlite3": "latest", "class-transformer": "latest", "class-validator": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "compression": "latest", "cookie-parser": "latest", "crypto": "latest", "csv-parser": "latest", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "hdb-pool": "latest", "helmet": "latest", "input-otp": "1.4.1", "ioredis": "latest", "jwt-decode": "^4.0.0", "lucide-react": "^0.454.0", "mongodb": "latest", "mssql": "latest", "mysql2": "latest", "next": "15.2.4", "next-themes": "latest", "oracledb": "latest", "passport": "latest", "passport-jwt": "latest", "passport-local": "latest", "pg": "latest", "pg-native": "latest", "pg-query-stream": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "latest", "reflect-metadata": "latest", "rxjs": "latest", "shared-types": "latest", "sonner": "^1.7.1", "sql.js": "latest", "sqlite3": "latest", "stream": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "ts-node": "latest", "typeorm": "latest", "typeorm-aurora-data-api-driver": "latest", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}