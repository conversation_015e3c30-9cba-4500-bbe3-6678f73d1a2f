/**
 * Type definitions for the frontend application
 */

// Auth types
export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
}

export interface TokenPayload {
  sub: string;
  email: string;
  exp: number;
  iat: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name?: string;
}

// User types
export interface User {
  id: string;
  email: string;
  name?: string;
  role?: UserRole;
  createdAt?: string;
  updatedAt?: string;
}

export type UserRole = "admin" | "editor" | "viewer";

// Domain types
export interface Domain {
  id: string;
  domain: string;
  title?: string;
  description?: string;
  verified: boolean;
  verificationMethod?: "dns" | "meta" | "file";
  verificationCode?: string;
  verifiedAt?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDomainDto {
  domain: string;
  title?: string;
  description?: string;
}

export interface UpdateDomainDto {
  title?: string;
  description?: string;
  verified?: boolean;
  verificationMethod?: "dns" | "meta" | "file";
  verificationCode?: string;
  verifiedAt?: string;
}

// Target domain types
export interface TargetDomain {
  id: string;
  domain: string;
  title?: string;
  description?: string;
  relevanceScore?: number;
  domainAuthority?: number;
  backlinks?: number;
  traffic?: number;
  trafficTrend?: "upward" | "downward" | "stable";
  contactEmail?: string;
  contactPage?: string;
  categories?: string[];
  keywords?: string[];
  lastUpdated?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTargetDomainDto {
  domain: string;
  title?: string;
  description?: string;
}

export interface UpdateTargetDomainDto {
  title?: string;
  description?: string;
  relevanceScore?: number;
  domainAuthority?: number;
  backlinks?: number;
  traffic?: number;
  trafficTrend?: "upward" | "downward" | "stable";
  contactEmail?: string;
  contactPage?: string;
  categories?: string[];
  keywords?: string[];
  lastUpdated?: string;
}

// Analysis types
export interface AnalysisResult {
  id: string;
  domain: string;
  title: string;
  description: string;
  relevanceScore: number;
  domainAuthority: number;
  backlinks: number;
  traffic: number;
  trafficTrend: "upward" | "downward" | "stable";
  topKeywords: string[];
  topPages: string[];
  industry: string;
  category: string;
  topCountry: string;
  websiteAge: number;
  language: string;
  contactEmail?: string;
  contactPage?: string;
  categories: string[];
  keywords: string[];
  lastUpdated: string;
}

// Email types
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables?: string[];
  createdAt: string;
  updatedAt?: string;
}

export interface CreateEmailTemplateDto {
  name: string;
  subject: string;
  body: string;
  variables?: string[];
}

export interface UpdateEmailTemplateDto {
  name?: string;
  subject?: string;
  body?: string;
  variables?: string[];
}

export interface OutreachEmail {
  id: string;
  targetDomainId: string;
  templateId: string;
  subject: string;
  body: string;
  status: "draft" | "sent" | "opened" | "clicked" | "replied" | "bounced";
  sentAt?: string;
  openedAt?: string;
  clickedAt?: string;
  repliedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// User settings types
export interface UserSettings {
  emailNotifications: {
    newDomainAnalysis: boolean;
    emailOpened: boolean;
    emailClicked: boolean;
    emailReplied: boolean;
    weeklyReport: boolean;
  };
  apiKeys: {
    mozApiKey?: string;
    semrushApiKey?: string;
    ahrefsApiKey?: string;
  };
  emailSettings: {
    fromName: string;
    signature: string;
    replyTo: string;
  };
  appearance: {
    theme: "light" | "dark" | "system";
    compactMode: boolean;
  };
}

// Dashboard types
export interface DashboardStats {
  domains: {
    total: number;
    active: number;
    pending: number;
  };
  targetDomains: {
    total: number;
    analyzed: number;
    contacted: number;
    highRelevance: number;
  };
  emails: {
    total: number;
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
  };
  campaigns: {
    total: number;
    active: number;
    completed: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
  };
  recentActivity: Array<{
    date: string;
    action: string;
    details: string;
  }>;
  topPerformingDomains: Array<{
    domain: string;
    relevanceScore: number;
    emailsSent: number;
    emailsOpened: number;
  }>;
  emailPerformance: Array<{
    date: string;
    sent: number;
    opened: number;
    clicked: number;
  }>;
}
