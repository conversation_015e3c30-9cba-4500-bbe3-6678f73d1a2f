import {
  createA<PERSON>,
  fetchBaseQuery,
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { shouldShowDemoData } from "./demo-data";
import {
  mockDashboardStats,
  mockAnalysisData,
  mockTeamMembers,
  mockEmailTemplates,
  mockSentEmails,
  mockUserSettings,
} from "./demo-data";
import {
  User,
  DashboardStats,
  AuthResponse,
  EmailTemplate,
  AnalysisResult,
  OutreachEmail,
  LoginCredentials,
  RegisterCredentials,
  UserSettings,
  Domain,
  CreateDomainDto,
  UpdateDomainDto,
} from "./types";

// Create the base query with auth header
const baseQueryWithAuth = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3050",
  prepareHeaders: (headers) => {
    // Only run on client-side
    if (typeof window !== "undefined") {
      // Get token from localStorage
      const accessToken = localStorage.getItem("accessToken");

      // If we have a token, add it to the headers
      if (accessToken) {
        console.log("Adding Authorization header with token");
        headers.set("Authorization", `Bearer ${accessToken}`);
      } else {
        console.log("No access token found in localStorage");
      }
    }

    return headers;
  },
  // Custom response handler for demo data
  responseHandler: async (response) => {
    // If demo mode is enabled, don't process the response
    if (shouldShowDemoData()) {
      // Return a placeholder that will be replaced by the transformResponse
      return { _demoMode: true };
    }

    // Otherwise, process the response normally
    const text = await response.text();
    return text.length ? JSON.parse(text) : null;
  },
});

// Track if we're currently refreshing to prevent multiple refresh attempts
let isRefreshing = false;
// Queue of requests that are waiting for token refresh
interface PendingRequest {
  resolve: (value: any) => void;
  reject: (error: any) => void;
  args: string | FetchArgs;
  api: any;
  extraOptions: any;
}

let pendingRequests: PendingRequest[] = [];

// Create a wrapper for the base query with token refresh logic
const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  // Only run token checks on client-side
  if (typeof window === "undefined") {
    return baseQueryWithAuth(args, api, extraOptions);
  }

  // Skip token refresh for auth endpoints to prevent loops
  const isAuthEndpoint =
    typeof args !== "string" &&
    args.url &&
    (args.url.includes("/auth/login") ||
      args.url.includes("/auth/register") ||
      args.url.includes("/auth/refresh"));

  // If we're already refreshing and this isn't an auth endpoint, queue the request
  if (isRefreshing && !isAuthEndpoint) {
    return new Promise((resolve, reject) => {
      pendingRequests.push({ resolve, reject, args, api, extraOptions });
    });
  }

  let result = await baseQueryWithAuth(args, api, extraOptions);

  // If we get a 401 Unauthorized response, try to refresh the token
  if (result.error && result.error.status === 401 && !isAuthEndpoint) {
    // Check if we have a refresh token
    const refreshToken = localStorage.getItem("refreshToken");

    if (refreshToken && !isRefreshing) {
      console.log("Token expired, attempting to refresh...");
      isRefreshing = true;

      try {
        // Try to get a new token
        const refreshResult = await baseQueryWithAuth(
          {
            url: "/auth/refresh",
            method: "POST",
            body: { refreshToken },
          },
          api,
          extraOptions
        );

        if (refreshResult.data) {
          // Store the new tokens
          const data = refreshResult.data as {
            accessToken?: string;
            refreshToken?: string;
            token?: string;
          };
          const newAccessToken = data.accessToken || data.token || "";
          const newRefreshToken =
            data.refreshToken || data.token || newAccessToken;

          if (!newAccessToken) {
            throw new Error("No access token returned from refresh endpoint");
          }

          console.log("Token refresh successful, storing new tokens");
          localStorage.setItem("accessToken", newAccessToken);
          localStorage.setItem("refreshToken", newRefreshToken);

          // Create a new headers object with the updated token
          const newHeaders = new Headers();
          if (args && typeof args !== "string" && args.headers) {
            // Copy existing headers
            if (args.headers instanceof Headers) {
              for (const [key, value] of args.headers.entries()) {
                newHeaders.set(key, value);
              }
            } else if (args.headers instanceof Object) {
              for (const [key, value] of Object.entries(args.headers)) {
                if (typeof value === "string") {
                  newHeaders.set(key, value);
                }
              }
            }
          }

          // Set the new Authorization header
          newHeaders.set("Authorization", `Bearer ${newAccessToken}`);

          // Create a new request with the updated headers
          const newArgs =
            typeof args === "string"
              ? args
              : {
                  ...args,
                  headers: newHeaders,
                };

          // Process all pending requests with the new token
          const pendingPromises = pendingRequests.map(
            ({
              resolve,
              reject,
              args: pendingArgs,
              api: pendingApi,
              extraOptions: pendingExtraOptions,
            }) => {
              // Create new headers with the updated token
              const newHeaders = new Headers();
              if (typeof pendingArgs !== "string" && pendingArgs.headers) {
                // Copy existing headers
                if (pendingArgs.headers instanceof Headers) {
                  for (const [key, value] of pendingArgs.headers.entries()) {
                    newHeaders.set(key, value);
                  }
                } else if (pendingArgs.headers instanceof Object) {
                  for (const [key, value] of Object.entries(
                    pendingArgs.headers
                  )) {
                    if (typeof value === "string") {
                      newHeaders.set(key, value);
                    }
                  }
                }
              }

              // Set the new Authorization header
              newHeaders.set("Authorization", `Bearer ${newAccessToken}`);

              // Create a new request with the updated headers
              const newPendingArgs =
                typeof pendingArgs === "string"
                  ? pendingArgs
                  : {
                      ...pendingArgs,
                      headers: newHeaders,
                    };

              // Execute the request and handle the promise
              return new Promise<void>(async (resolveRequest) => {
                try {
                  const result = await baseQueryWithAuth(
                    newPendingArgs,
                    pendingApi,
                    pendingExtraOptions
                  );
                  resolve(result);
                  resolveRequest();
                } catch (error: unknown) {
                  reject(error);
                  resolveRequest();
                }
              });
            }
          );

          // Execute all pending requests
          await Promise.all(pendingPromises);
          pendingRequests = [];

          // Retry the original query with new token
          console.log("Retrying original request with new token");
          const retryResult = await baseQueryWithAuth(
            newArgs,
            api,
            extraOptions
          );
          isRefreshing = false;
          return retryResult;
        } else {
          // If refresh fails, clear tokens
          console.error("Token refresh failed: No data returned");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("refreshToken");

          // Reject all pending requests
          pendingRequests.forEach(({ reject }) => reject(result.error));
          pendingRequests = [];
          isRefreshing = false;
        }
      } catch (error) {
        // If refresh throws an error, clear tokens
        console.error("Token refresh failed:", error);
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");

        // Reject all pending requests
        pendingRequests.forEach(({ reject }) => reject(error));
        pendingRequests = [];
        isRefreshing = false;
      }
    } else {
      console.log(
        "No refresh token available or already refreshing, clearing auth state"
      );
      localStorage.removeItem("accessToken");
      if (!isRefreshing) {
        localStorage.removeItem("refreshToken");
      }
    }
  }

  return result;
};

// Create API slice
export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    "User",
    "DashboardStats",
    "Analysis",
    "Team",
    "Settings",
    "EmailTemplates",
    "Domains",
  ],
  endpoints: (builder) => ({
    // Auth endpoints
    login: builder.mutation<AuthResponse, LoginCredentials>({
      query: (credentials) => ({
        url: "/auth/login",
        method: "POST",
        body: credentials,
      }),
      // Handle the response to store tokens
      transformResponse: (response: AuthResponse & { token?: string }) => {
        if (!response) {
          throw new Error("No response received from server");
        }

        // Handle both token formats (for backward compatibility)
        const accessToken = response.accessToken || response.token || "";
        const refreshToken =
          response.refreshToken || response.token || accessToken;

        if (!accessToken) {
          console.error("No access token in response:", response);
          throw new Error("Authentication failed: No access token received");
        }

        if (typeof window !== "undefined") {
          localStorage.setItem("accessToken", accessToken);
          localStorage.setItem("refreshToken", refreshToken);
        }

        // Normalize the response format
        return {
          user: response.user,
          accessToken,
          refreshToken,
        };
      },
    }),

    register: builder.mutation<AuthResponse, RegisterCredentials>({
      query: (userData) => ({
        url: "/auth/register",
        method: "POST",
        body: userData,
      }),
      // Handle the response to store tokens
      transformResponse: (response: AuthResponse & { token?: string }) => {
        if (!response) {
          throw new Error("No response received from server");
        }

        // Handle both token formats (for backward compatibility)
        const accessToken = response.accessToken || response.token || "";
        const refreshToken =
          response.refreshToken || response.token || accessToken;

        if (!accessToken) {
          console.error("No access token in response:", response);
          throw new Error("Authentication failed: No access token received");
        }

        if (typeof window !== "undefined") {
          localStorage.setItem("accessToken", accessToken);
          localStorage.setItem("refreshToken", refreshToken);
        }

        // Normalize the response format
        return {
          user: response.user,
          accessToken,
          refreshToken,
        };
      },
    }),

    logout: builder.mutation<{ success: boolean }, void>({
      query: () => ({
        url: "/auth/logout",
        method: "POST",
        body: {
          refreshToken:
            typeof window !== "undefined"
              ? localStorage.getItem("refreshToken")
              : null,
        },
      }),
      // Always clear tokens on logout
      async onQueryStarted(_, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } finally {
          if (typeof window !== "undefined") {
            localStorage.removeItem("accessToken");
            localStorage.removeItem("refreshToken");
          }
        }
      },
    }),

    // User endpoints
    getCurrentUser: builder.query<User, void>({
      query: () => "/users/me",
      providesTags: ["User"],
    }),

    // Dashboard endpoints
    getDashboardStats: builder.query<DashboardStats, void>({
      query: () => "/dashboard/stats",
      providesTags: ["DashboardStats"],
      // Handle demo data
      transformResponse: (
        response: DashboardStats | { _demoMode: boolean }
      ) => {
        if (response && "_demoMode" in response) {
          return mockDashboardStats;
        }
        return response as DashboardStats;
      },
    }),

    // Analysis endpoints
    getAnalyses: builder.query<AnalysisResult[], void>({
      query: () => "/analysis/results",
      providesTags: ["Analysis"],
      // Handle demo data
      transformResponse: (
        response: AnalysisResult[] | { _demoMode: boolean }
      ) => {
        if (response && "_demoMode" in response) {
          return mockAnalysisData;
        }
        return response as AnalysisResult[];
      },
    }),

    // Team endpoints
    getTeamMembers: builder.query<User[], void>({
      query: () => "/users/team",
      providesTags: ["Team"],
      // Handle demo data
      transformResponse: (response: unknown) => {
        if (
          response &&
          typeof response === "object" &&
          "_demoMode" in response
        ) {
          return mockTeamMembers;
        }
        return response as User[];
      },
    }),

    inviteTeamMember: builder.mutation<
      { success: boolean },
      { email: string; role?: string }
    >({
      query: (data) => ({
        url: "/users/invite",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Team"],
    }),

    // Settings endpoints
    getSettings: builder.query<UserSettings, void>({
      query: () => "/users/settings",
      providesTags: ["Settings"],
      // Handle demo data
      transformResponse: (response: UserSettings | { _demoMode: boolean }) => {
        if (response && "_demoMode" in response) {
          return mockUserSettings;
        }
        return response as UserSettings;
      },
    }),

    updateSettings: builder.mutation<{ success: boolean }, UserSettings>({
      query: (settings) => ({
        url: "/users/settings",
        method: "PATCH",
        body: settings,
      }),
      invalidatesTags: ["Settings"],
    }),

    // Email templates endpoints
    getEmailTemplates: builder.query<EmailTemplate[], void>({
      query: () => "/emails/templates",
      providesTags: ["EmailTemplates"],
      // Handle demo data
      transformResponse: (
        response: EmailTemplate[] | { _demoMode: boolean }
      ) => {
        if (response && "_demoMode" in response) {
          return mockEmailTemplates;
        }
        return response as EmailTemplate[];
      },
    }),

    getEmailTemplate: builder.query<EmailTemplate, string>({
      query: (id) => `/emails/templates/${id}`,
      providesTags: (_result, _error, id) => [{ type: "EmailTemplates", id }],
    }),

    createEmailTemplate: builder.mutation<
      EmailTemplate,
      { name: string; subject: string; body: string; variables?: string[] }
    >({
      query: (template) => ({
        url: "/emails/templates",
        method: "POST",
        body: template,
      }),
      invalidatesTags: ["EmailTemplates"],
    }),

    updateEmailTemplate: builder.mutation<
      EmailTemplate,
      {
        id: string;
        template: {
          name?: string;
          subject?: string;
          body?: string;
          variables?: string[];
        };
      }
    >({
      query: ({ id, template }) => ({
        url: `/emails/templates/${id}`,
        method: "PATCH",
        body: template,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "EmailTemplates", id },
      ],
    }),

    deleteEmailTemplate: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/emails/templates/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["EmailTemplates"],
    }),

    // Sent emails endpoints
    getSentEmails: builder.query<OutreachEmail[], void>({
      query: () => "/emails/outreach",
      // Handle demo data
      transformResponse: (
        response: OutreachEmail[] | { _demoMode: boolean }
      ) => {
        if (response && "_demoMode" in response) {
          return mockSentEmails;
        }
        return response as OutreachEmail[];
      },
    }),

    // Domain endpoints
    getDomains: builder.query<Domain[], void>({
      query: () => "/domains",
      providesTags: ["Domains"],
    }),

    getDomain: builder.query<Domain, string>({
      query: (id) => `/domains/${id}`,
      providesTags: (_result, _error, id) => [{ type: "Domains", id }],
    }),

    getDomainByName: builder.query<Domain, string>({
      query: (domainName) =>
        `/domains?domain=${encodeURIComponent(domainName)}`,
      transformResponse: (response: Domain[]) => {
        if (response && Array.isArray(response) && response.length > 0) {
          return response[0];
        }
        throw new Error("Domain not found");
      },
      providesTags: (result) =>
        result ? [{ type: "Domains", id: result.id }] : ["Domains"],
    }),

    createDomain: builder.mutation<Domain, CreateDomainDto>({
      query: (domain) => ({
        url: "/domains",
        method: "POST",
        body: domain,
      }),
      invalidatesTags: ["Domains"],
    }),

    updateDomain: builder.mutation<
      Domain,
      { id: string; domain: UpdateDomainDto }
    >({
      query: ({ id, domain }) => ({
        url: `/domains/${id}`,
        method: "PATCH",
        body: domain,
      }),
      invalidatesTags: (_result, _error, { id }) => [{ type: "Domains", id }],
    }),

    verifyDomain: builder.mutation<
      Domain,
      {
        domain: string;
        verificationMethod: string;
        dnsValue?: string;
        metaContent?: string;
        fileContent?: string;
      }
    >({
      query: (data) => ({
        url: "/domains/verify",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Domains"],
    }),

    deleteDomain: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/domains/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Domains"],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useGetDashboardStatsQuery,
  useGetAnalysesQuery,
  useGetTeamMembersQuery,
  useInviteTeamMemberMutation,
  useGetSettingsQuery,
  useUpdateSettingsMutation,
  useGetEmailTemplatesQuery,
  useGetEmailTemplateQuery,
  useCreateEmailTemplateMutation,
  useUpdateEmailTemplateMutation,
  useDeleteEmailTemplateMutation,
  useGetSentEmailsQuery,
  // Domain hooks
  useGetDomainsQuery,
  useGetDomainQuery,
  useGetDomainByNameQuery,
  useCreateDomainMutation,
  useUpdateDomainMutation,
  useVerifyDomainMutation,
  useDeleteDomainMutation,
} = apiSlice;
