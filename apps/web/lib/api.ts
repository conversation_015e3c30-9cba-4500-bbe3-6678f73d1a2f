import axios, { AxiosInstance } from "axios";
import { jwtDecode } from "jwt-decode";
import {
  User,
  Domain,
  TargetDomain,
  EmailTemplate,
  OutreachEmail,
  AnalysisResult,
  AuthResponse,
  TokenPayload,
  DashboardStats,
  UserSettings,
} from "./types";

// API Client
class ApiClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private refreshPromise: Promise<string> | null = null;
  private isRefreshing = false;

  constructor() {
    const baseURL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3050";

    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Initialize tokens from localStorage if available (client-side only)
    if (typeof window !== "undefined") {
      this.accessToken = localStorage.getItem("accessToken");
      this.refreshToken = localStorage.getItem("refreshToken");
    }

    // Add request interceptor to include auth token
    this.client.interceptors.request.use(async (config) => {
      // If we have an access token, add it to the request
      if (this.accessToken) {
        // Check if token is expired
        if (this.isTokenExpired(this.accessToken)) {
          // Try to refresh the token
          const newToken = await this.refreshAccessToken();
          if (newToken) {
            config.headers.Authorization = `Bearer ${newToken}`;
          }
        } else {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
      }
      return config;
    });

    // Add response interceptor to handle token refresh on 401 errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // If the error is 401 Unauthorized and we have a refresh token
        if (
          error.response?.status === 401 &&
          this.refreshToken &&
          !originalRequest._retry
        ) {
          originalRequest._retry = true;

          try {
            // Try to refresh the token
            const newToken = await this.refreshAccessToken();
            if (newToken) {
              // Update the authorization header
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              // Retry the original request
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // If refresh fails, clear tokens and redirect to login
            this.clearTokens();
            if (typeof window !== "undefined") {
              window.location.href = "/login";
            }
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private isTokenExpired(token: string): boolean {
    try {
      const decoded = jwtDecode<TokenPayload>(token);
      // Check if the token is expired (with 30 seconds buffer)
      return decoded.exp * 1000 < Date.now() + 30000;
    } catch (error) {
      console.error("Error decoding token:", error);
      return true;
    }
  }

  private async refreshAccessToken(): Promise<string | null> {
    // If we're already refreshing, return the existing promise
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    // If we don't have a refresh token, we can't refresh
    if (!this.refreshToken) {
      return null;
    }

    this.isRefreshing = true;
    this.refreshPromise = new Promise<string>(async (resolve, reject) => {
      try {
        const response = await axios.post(
          `${
            process.env.NEXT_PUBLIC_API_URL || "http://localhost:3050"
          }/auth/refresh`,
          { refreshToken: this.refreshToken }
        );

        const { accessToken, refreshToken } = response.data;
        this.setTokens(accessToken, refreshToken);
        resolve(accessToken);
      } catch (error) {
        this.clearTokens();
        reject(error);
      } finally {
        this.isRefreshing = false;
        this.refreshPromise = null;
      }
    });

    return this.refreshPromise;
  }

  setTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;

    if (typeof window !== "undefined") {
      localStorage.setItem("accessToken", accessToken);
      localStorage.setItem("refreshToken", refreshToken);
    }
  }

  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;

    if (typeof window !== "undefined") {
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
    }
  }

  // Auth endpoints
  async register(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>("/auth/register", {
      email,
      password,
      name,
    });
    const { accessToken, refreshToken } = response.data;
    this.setTokens(accessToken, refreshToken);
    return response.data;
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>("/auth/login", {
      email,
      password,
    });
    const { accessToken, refreshToken } = response.data;
    this.setTokens(accessToken, refreshToken);
    return response.data;
  }

  async logout() {
    // Call the logout endpoint if available
    try {
      if (this.refreshToken) {
        await this.client.post("/auth/logout", {
          refreshToken: this.refreshToken,
        });
      }
    } catch (error) {
      console.error("Error during logout:", error);
    } finally {
      // Always clear tokens locally
      this.clearTokens();
    }
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>("/users/me");
    return response.data;
  }

  async updateUser(data: Partial<User>): Promise<User> {
    const response = await this.client.patch<User>("/users/me", data);
    return response.data;
  }

  // Domain endpoints
  async getDomains(): Promise<Domain[]> {
    const response = await this.client.get<Domain[]>("/domains");
    return response.data;
  }

  async getDomain(id: string): Promise<Domain> {
    const response = await this.client.get<Domain>(`/domains/${id}`);
    return response.data;
  }

  async createDomain(data: Partial<Domain>): Promise<Domain> {
    const response = await this.client.post<Domain>("/domains", data);
    return response.data;
  }

  async updateDomain(id: string, data: Partial<Domain>): Promise<Domain> {
    const response = await this.client.patch<Domain>(`/domains/${id}`, data);
    return response.data;
  }

  async verifyDomain(data: {
    domain: string;
    verificationMethod: string;
  }): Promise<Domain> {
    const response = await this.client.post<Domain>("/domains/verify", data);
    return response.data;
  }

  async getDomainByName(domainName: string): Promise<Domain> {
    const response = await this.client.get<Domain[]>(
      `/domains?domain=${encodeURIComponent(domainName)}`
    );
    if (
      response.data &&
      Array.isArray(response.data) &&
      response.data.length > 0
    ) {
      return response.data[0];
    }
    throw new Error("Domain not found");
  }

  // Target Domain endpoints
  async getTargetDomains(): Promise<TargetDomain[]> {
    const response = await this.client.get<TargetDomain[]>("/target-domains");
    return response.data;
  }

  async getTargetDomain(id: string): Promise<TargetDomain> {
    const response = await this.client.get<TargetDomain>(
      `/target-domains/${id}`
    );
    return response.data;
  }

  async createTargetDomain(data: Partial<TargetDomain>): Promise<TargetDomain> {
    const response = await this.client.post<TargetDomain>(
      "/target-domains",
      data
    );
    return response.data;
  }

  async bulkCreateTargetDomains(data: {
    domains: Partial<TargetDomain>[];
  }): Promise<TargetDomain[]> {
    const response = await this.client.post<TargetDomain[]>(
      "/target-domains/bulk",
      data
    );
    return response.data;
  }

  async uploadTargetDomainsCSV(file: File): Promise<TargetDomain[]> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await this.client.post<TargetDomain[]>(
      "/target-domains/upload",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  }

  // Analysis endpoints
  async getAnalyses(): Promise<AnalysisResult[]> {
    const response = await this.client.get<AnalysisResult[]>("/analysis");
    return response.data;
  }

  async analyzeTargetDomain(
    targetDomainId: string
  ): Promise<{ success: boolean }> {
    const response = await this.client.post<{ success: boolean }>(
      `/analysis/${targetDomainId}/analyze`
    );
    return response.data;
  }

  // Email endpoints
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    const response = await this.client.get<EmailTemplate[]>(
      "/emails/templates"
    );
    return response.data;
  }

  async getEmailTemplate(id: string): Promise<EmailTemplate> {
    const response = await this.client.get<EmailTemplate>(
      `/emails/templates/${id}`
    );
    return response.data;
  }

  async createEmailTemplate(
    data: Partial<EmailTemplate>
  ): Promise<EmailTemplate> {
    const response = await this.client.post<EmailTemplate>(
      "/emails/templates",
      data
    );
    return response.data;
  }

  async createOutreachEmail(data: {
    targetDomainIds: string[];
    templateId: string;
    customizations?: Record<string, string>;
  }): Promise<OutreachEmail[]> {
    const response = await this.client.post<OutreachEmail[]>(
      "/emails/outreach",
      data
    );
    return response.data;
  }

  // Dashboard endpoints
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.client.get<DashboardStats>("/dashboard/stats");
    return response.data;
  }

  // Analysis results endpoints
  async getAnalysisResults(): Promise<AnalysisResult[]> {
    const response = await this.client.get<AnalysisResult[]>(
      "/analysis/results"
    );
    return response.data;
  }

  // Team endpoints
  async getTeamMembers(): Promise<User[]> {
    const response = await this.client.get<User[]>("/users/team");
    return response.data;
  }

  async inviteTeamMember(
    email: string,
    role: string
  ): Promise<{ success: boolean; message: string }> {
    const response = await this.client.post<{
      success: boolean;
      message: string;
    }>("/users/invite", {
      email,
      role,
    });
    return response.data;
  }

  // Settings endpoints
  async getSettings(): Promise<UserSettings> {
    const response = await this.client.get<UserSettings>("/users/settings");
    return response.data;
  }

  async updateSettings(settings: Partial<UserSettings>): Promise<UserSettings> {
    const response = await this.client.patch<UserSettings>(
      "/users/settings",
      settings
    );
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.client.get<{
      status: string;
      timestamp: string;
    }>("/health");
    return response.data;
  }
}

// Export a singleton instance
const api = new ApiClient();
export default api;
