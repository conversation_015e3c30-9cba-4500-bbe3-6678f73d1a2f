import { useState, useEffect, useCallback, useRef } from "react";
import api from "../api";
import { withDemoDataFallback } from "../demo-data";
import { DashboardStats, AnalysisResult } from "../types";

/**
 * Custom hook for making API calls with demo data fallback
 * @param apiCallFn - Function that makes the API call
 * @param demoData - Demo data to use if API call fails or demo mode is enabled
 * @returns Object with data, loading state, error state, and a refresh function
 */
export function useApiWithDemoData<T>(
  apiCallFn: () => Promise<T>,
  demoData: T
): {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
} {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Use refs to prevent infinite loops
  const apiCallFnRef = useRef<() => Promise<T>>(apiCallFn);
  const demoDataRef = useRef<T>(demoData);

  // Update refs when inputs change
  useEffect(() => {
    apiCallFnRef.current = apiCallFn;
    demoDataRef.current = demoData;
  }, [apiCallFn, demoData]);

  const fetchData = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await withDemoDataFallback(
        apiCallFnRef.current,
        demoDataRef.current
      );
      setData(result);
    } catch (err) {
      console.error("API call failed:", err);
      setError(
        err instanceof Error ? err : new Error("Unknown error occurred")
      );
    } finally {
      setIsLoading(false);
    }
  }, []); // No dependencies to prevent infinite loops

  // Only fetch data on mount and when manually refreshed
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { data, isLoading, error, refresh: fetchData };
}

/**
 * Custom hook for the dashboard statistics
 */
export function useDashboardStats() {
  return useApiWithDemoData<DashboardStats>(() => api.getDashboardStats(), {
    domains: {
      total: 5,
      active: 3,
      pending: 2,
    },
    targetDomains: {
      total: 120,
      analyzed: 95,
      contacted: 45,
      highRelevance: 32,
    },
    emails: {
      total: 150,
      sent: 120,
      opened: 75,
      clicked: 30,
      replied: 15,
    },
    campaigns: {
      total: 8,
      active: 3,
      completed: 5,
      openRate: 62.5,
      clickRate: 25,
      replyRate: 12.5,
    },
    recentActivity: [
      {
        date: "2023-06-01",
        action: "Email Sent",
        details: "Campaign: Summer Outreach",
      },
      { date: "2023-06-01", action: "Domain Analyzed", details: "example.com" },
      {
        date: "2023-05-31",
        action: "Email Opened",
        details: "by <EMAIL>",
      },
      {
        date: "2023-05-30",
        action: "Campaign Created",
        details: "Summer Outreach",
      },
    ],
    topPerformingDomains: [
      {
        domain: "example.com",
        relevanceScore: 92,
        emailsSent: 25,
        emailsOpened: 18,
      },
      {
        domain: "sample.org",
        relevanceScore: 88,
        emailsSent: 20,
        emailsOpened: 15,
      },
      {
        domain: "test.net",
        relevanceScore: 85,
        emailsSent: 18,
        emailsOpened: 12,
      },
      {
        domain: "demo.io",
        relevanceScore: 82,
        emailsSent: 15,
        emailsOpened: 10,
      },
    ],
    emailPerformance: [
      { date: "2023-05-01", sent: 20, opened: 12, clicked: 5 },
      { date: "2023-05-08", sent: 25, opened: 15, clicked: 7 },
      { date: "2023-05-15", sent: 30, opened: 18, clicked: 8 },
      { date: "2023-05-22", sent: 22, opened: 14, clicked: 6 },
      { date: "2023-05-29", sent: 23, opened: 16, clicked: 7 },
    ],
  });
}

/**
 * Custom hook for domain analysis data
 */
export function useAnalysisData() {
  return useApiWithDemoData<AnalysisResult[]>(
    () => api.getAnalyses(),
    [
      {
        id: "1",
        domain: "techblog.com",
        title: "Tech Blog - Latest Technology News and Reviews",
        description:
          "A blog covering the latest technology news, reviews, and insights.",
        relevanceScore: 92,
        domainAuthority: 76,
        backlinks: 1240,
        traffic: 45000,
        trafficTrend: "upward",
        topKeywords: ["technology", "gadgets", "reviews", "tech news"],
        topPages: [
          "https://techblog.com/best-laptops-2023",
          "https://techblog.com/iphone-15-review",
        ],
        industry: "Technology",
        category: "Blog",
        topCountry: "United States",
        websiteAge: 48,
        language: "en",
        contactEmail: "<EMAIL>",
        contactPage: "https://techblog.com/contact",
        categories: ["Technology", "Reviews"],
        keywords: ["technology", "gadgets", "reviews", "tech news"],
        lastUpdated: "2023-04-15",
      },
      {
        id: "2",
        domain: "marketingpro.com",
        title: "Marketing Pro - Digital Marketing Strategies",
        description:
          "Professional marketing strategies and tips for businesses.",
        relevanceScore: 88,
        domainAuthority: 68,
        backlinks: 980,
        traffic: 32000,
        trafficTrend: "stable",
        topKeywords: [
          "marketing",
          "digital marketing",
          "SEO",
          "content strategy",
        ],
        topPages: [
          "https://marketingpro.com/seo-guide-2023",
          "https://marketingpro.com/content-marketing-tips",
        ],
        industry: "Marketing",
        category: "Services",
        topCountry: "United States",
        websiteAge: 36,
        language: "en",
        contactEmail: "<EMAIL>",
        contactPage: "https://marketingpro.com/contact",
        categories: ["Marketing", "Business"],
        keywords: ["marketing", "digital marketing", "SEO", "content strategy"],
        lastUpdated: "2023-04-12",
      },
      {
        id: "3",
        domain: "webdesignhub.net",
        title: "Web Design Hub - Creative Web Solutions",
        description: "Web design inspiration, tutorials, and resources.",
        relevanceScore: 85,
        domainAuthority: 62,
        backlinks: 750,
        traffic: 28000,
        trafficTrend: "upward",
        topKeywords: [
          "web design",
          "UI/UX",
          "responsive design",
          "design trends",
        ],
        topPages: [
          "https://webdesignhub.net/responsive-design-guide",
          "https://webdesignhub.net/ui-trends-2023",
        ],
        industry: "Design",
        category: "Resources",
        topCountry: "United Kingdom",
        websiteAge: 24,
        language: "en",
        contactEmail: "<EMAIL>",
        contactPage: "https://webdesignhub.net/contact",
        categories: ["Design", "Web Development"],
        keywords: ["web design", "UI/UX", "responsive design", "design trends"],
        lastUpdated: "2023-04-10",
      },
    ]
  );
}
