/**
 * Utility functions for handling demo data in the application
 */
import {
  DashboardStats,
  AnalysisResult,
  EmailTemplate,
  OutreachEmail,
  User,
  UserRole,
  UserSettings,
} from "./types";

/**
 * Check if demo data should be shown based on environment variable
 */
export const shouldShowDemoData = (): boolean => {
  // Check if the environment variable is set to 'true'
  return process.env.NEXT_PUBLIC_SHOW_DEMO_DATA === "true";
};

/**
 * Helper function to handle API data with demo data fallback
 * @param apiCallback - Function that makes the API call
 * @param demoData - Demo data to use if API call fails or demo mode is enabled
 * @returns The result of the API call or demo data
 */
export async function withDemoDataFallback<T>(
  apiCallback: () => Promise<T>,
  demoData: T
): Promise<T> {
  // If demo data is enabled, return demo data immediately
  if (shouldShowDemoData()) {
    console.log("Using demo data (NEXT_PUBLIC_SHOW_DEMO_DATA=true)");
    return demoData;
  }

  try {
    // Attempt to fetch real data from API
    return await apiCallback();
  } catch (error) {
    console.error("API call failed, falling back to demo data:", error);
    return demoData;
  }
}

/**
 * Mock data for dashboard statistics
 */
export const mockDashboardStats: DashboardStats = {
  domains: {
    total: 5,
    active: 3,
    pending: 2,
  },
  targetDomains: {
    total: 120,
    analyzed: 95,
    contacted: 45,
    highRelevance: 32,
  },
  emails: {
    total: 150,
    sent: 120,
    opened: 75,
    clicked: 30,
    replied: 15,
  },
  campaigns: {
    total: 8,
    active: 3,
    completed: 5,
    openRate: 62.5,
    clickRate: 25,
    replyRate: 12.5,
  },
  recentActivity: [
    {
      date: "2023-06-01",
      action: "Email Sent",
      details: "Campaign: Summer Outreach",
    },
    { date: "2023-06-01", action: "Domain Analyzed", details: "example.com" },
    {
      date: "2023-05-31",
      action: "Email Opened",
      details: "by <EMAIL>",
    },
    {
      date: "2023-05-30",
      action: "Campaign Created",
      details: "Summer Outreach",
    },
  ],
  topPerformingDomains: [
    {
      domain: "example.com",
      relevanceScore: 92,
      emailsSent: 25,
      emailsOpened: 18,
    },
    {
      domain: "sample.org",
      relevanceScore: 88,
      emailsSent: 20,
      emailsOpened: 15,
    },
    {
      domain: "test.net",
      relevanceScore: 85,
      emailsSent: 18,
      emailsOpened: 12,
    },
    { domain: "demo.io", relevanceScore: 82, emailsSent: 15, emailsOpened: 10 },
  ],
  emailPerformance: [
    { date: "2023-05-01", sent: 20, opened: 12, clicked: 5 },
    { date: "2023-05-08", sent: 25, opened: 15, clicked: 7 },
    { date: "2023-05-15", sent: 30, opened: 18, clicked: 8 },
    { date: "2023-05-22", sent: 22, opened: 14, clicked: 6 },
    { date: "2023-05-29", sent: 23, opened: 16, clicked: 7 },
  ],
};

/**
 * Mock data for domain analysis
 */
export const mockAnalysisData: AnalysisResult[] = [
  {
    id: "1",
    domain: "techblog.com",
    title: "Tech Blog - Latest Technology News and Reviews",
    description:
      "A blog covering the latest technology news, reviews, and insights.",
    relevanceScore: 92,
    domainAuthority: 76,
    backlinks: 1240,
    traffic: 45000,
    trafficTrend: "upward",
    topKeywords: ["technology", "gadgets", "reviews", "tech news"],
    topPages: [
      "https://techblog.com/best-laptops-2023",
      "https://techblog.com/iphone-15-review",
    ],
    industry: "Technology",
    category: "Blog",
    topCountry: "United States",
    websiteAge: 48,
    language: "en",
    contactEmail: "<EMAIL>",
    contactPage: "https://techblog.com/contact",
    categories: ["Technology", "Reviews"],
    keywords: ["technology", "gadgets", "reviews", "tech news"],
    lastUpdated: "2023-04-15",
  },
  {
    id: "2",
    domain: "marketingpro.com",
    title: "Marketing Pro - Digital Marketing Strategies",
    description: "Professional marketing strategies and tips for businesses.",
    relevanceScore: 88,
    domainAuthority: 68,
    backlinks: 980,
    traffic: 32000,
    trafficTrend: "stable",
    topKeywords: ["marketing", "digital marketing", "SEO", "content strategy"],
    topPages: [
      "https://marketingpro.com/seo-guide-2023",
      "https://marketingpro.com/content-marketing-tips",
    ],
    industry: "Marketing",
    category: "Services",
    topCountry: "United States",
    websiteAge: 36,
    language: "en",
    contactEmail: "<EMAIL>",
    contactPage: "https://marketingpro.com/contact",
    categories: ["Marketing", "Business"],
    keywords: ["marketing", "digital marketing", "SEO", "content strategy"],
    lastUpdated: "2023-04-12",
  },
  {
    id: "3",
    domain: "webdesignhub.net",
    title: "Web Design Hub - Creative Web Solutions",
    description: "Web design inspiration, tutorials, and resources.",
    relevanceScore: 85,
    domainAuthority: 62,
    backlinks: 750,
    traffic: 28000,
    trafficTrend: "upward",
    topKeywords: ["web design", "UI/UX", "responsive design", "design trends"],
    topPages: [
      "https://webdesignhub.net/responsive-design-guide",
      "https://webdesignhub.net/ui-trends-2023",
    ],
    industry: "Design",
    category: "Resources",
    topCountry: "United Kingdom",
    websiteAge: 24,
    language: "en",
    contactEmail: "<EMAIL>",
    contactPage: "https://webdesignhub.net/contact",
    categories: ["Design", "Web Development"],
    keywords: ["web design", "UI/UX", "responsive design", "design trends"],
    lastUpdated: "2023-04-10",
  },
];

/**
 * Mock data for email templates
 */
export const mockEmailTemplates: EmailTemplate[] = [
  {
    id: "1",
    name: "Initial Outreach",
    subject: "Collaboration Opportunity with {{domain}}",
    body: `Hello,

I recently came across your website {{domain}} and was impressed by your content on {{topic}}.

I run a website in a similar niche and thought there might be an opportunity for us to collaborate on a content exchange or backlink partnership.

Would you be interested in discussing this further?

Best regards,
{{name}}`,
    createdAt: "2023-03-15",
  },
  {
    id: "2",
    name: "Follow-up",
    subject: "Following up on our collaboration opportunity",
    body: `Hello again,

I wanted to follow up on my previous email about a potential collaboration between our websites.

I believe there's a great opportunity for us to work together and benefit both our audiences.

Please let me know if you're interested in discussing this further.

Best regards,
{{name}}`,
    createdAt: "2023-03-20",
  },
];

/**
 * Mock data for sent emails
 */
export const mockSentEmails: OutreachEmail[] = [
  {
    id: "1",
    targetDomainId: "1",
    templateId: "1",
    subject: "Collaboration Opportunity with techblog.com",
    body: "Hello,\n\nI recently came across your website techblog.com and was impressed by your content on technology.\n\nI run a website in a similar niche and thought there might be an opportunity for us to collaborate on a content exchange or backlink partnership.\n\nWould you be interested in discussing this further?\n\nBest regards,\nJohn",
    status: "opened",
    sentAt: "2023-04-20T10:30:00Z",
    openedAt: "2023-04-20T14:15:00Z",
    createdAt: "2023-04-20T09:30:00Z",
    updatedAt: "2023-04-20T14:15:00Z",
  },
  {
    id: "2",
    targetDomainId: "2",
    templateId: "1",
    subject: "Collaboration Opportunity with marketingpro.com",
    body: "Hello,\n\nI recently came across your website marketingpro.com and was impressed by your content on marketing.\n\nI run a website in a similar niche and thought there might be an opportunity for us to collaborate on a content exchange or backlink partnership.\n\nWould you be interested in discussing this further?\n\nBest regards,\nJohn",
    status: "sent",
    sentAt: "2023-04-19T09:45:00Z",
    createdAt: "2023-04-19T09:30:00Z",
    updatedAt: "2023-04-19T09:45:00Z",
  },
  {
    id: "3",
    targetDomainId: "3",
    templateId: "1",
    subject: "Collaboration Opportunity with webdesignhub.net",
    body: "Hello,\n\nI recently came across your website webdesignhub.net and was impressed by your content on web design.\n\nI run a website in a similar niche and thought there might be an opportunity for us to collaborate on a content exchange or backlink partnership.\n\nWould you be interested in discussing this further?\n\nBest regards,\nJohn",
    status: "clicked",
    sentAt: "2023-04-18T11:20:00Z",
    openedAt: "2023-04-18T13:10:00Z",
    clickedAt: "2023-04-18T13:15:00Z",
    createdAt: "2023-04-18T11:00:00Z",
    updatedAt: "2023-04-18T13:15:00Z",
  },
];

/**
 * Mock data for team members
 */
export const mockTeamMembers: User[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "admin" as UserRole,
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-04-20T15:30:00Z",
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "editor" as UserRole,
    createdAt: "2023-02-10T14:45:00Z",
    updatedAt: "2023-04-19T14:45:00Z",
  },
  {
    id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    role: "viewer" as UserRole,
    createdAt: "2023-03-05T10:15:00Z",
    updatedAt: "2023-04-18T10:15:00Z",
  },
];

/**
 * Mock data for user settings
 */
export const mockUserSettings: UserSettings = {
  id: "settings-1",
  userId: "1",
  emailNotifications: {
    newDomainAnalysis: true,
    emailOpened: true,
    emailClicked: true,
    emailReplied: true,
    weeklyReport: true,
  },
  apiKeys: {
    mozApiKey: "demo-moz-api-key",
    semrushApiKey: "demo-semrush-api-key",
    ahrefsApiKey: "demo-ahrefs-api-key",
  },
  emailSettings: {
    fromName: "John Doe",
    signature: "Best regards,\nJohn Doe\nBacklink Project",
    replyTo: "<EMAIL>",
  },
  appearance: {
    theme: "system",
    compactMode: false,
  },
  createdAt: "2023-01-15T10:30:00Z",
  updatedAt: "2023-04-20T15:30:00Z",
};
