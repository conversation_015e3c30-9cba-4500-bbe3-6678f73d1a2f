# BackLink Frontend

This is the frontend application for the BackLink platform, built with Next.js, React, and Tailwind CSS.

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 8+

### Installation

1. Install dependencies:
```bash
pnpm install
```

2. Set up environment variables:

Create a `.env.local` file in the `apps/web` directory:
```
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3050

# Feature Flags
NEXT_PUBLIC_SHOW_DEMO_DATA=true
```

3. Start the development server:
```bash
pnpm dev
```

## Environment Variables

### API Configuration

- `NEXT_PUBLIC_API_URL`: The URL of the backend API server. Default: `http://localhost:3050`

### Feature Flags

- `NEXT_PUBLIC_SHOW_DEMO_DATA`: When set to `true`, the application will display demo/mock data instead of making actual API calls. This is useful for development and demonstration purposes. Default: `false`

## Project Structure

```
apps/web/
├── app/                 # Next.js app router
│   ├── dashboard/       # Dashboard pages
│   ├── auth/            # Authentication pages
│   └── ...
├── components/          # React components
│   ├── ui/              # UI components (shadcn/ui)
│   └── ...
├── lib/                 # Utility functions and hooks
│   ├── api.ts           # API client
│   ├── demo-data.ts     # Demo data utilities
│   └── ...
├── public/              # Static assets
└── ...
```

## Features

- **Authentication**: User registration, login, and session management
- **Dashboard**: Overview of backlink strategy and performance
- **Domain Management**: Verify and manage your domains
- **Target Domain Upload**: Upload and manage target domains for analysis
- **Analysis**: Analyze target domains for backlink opportunities
- **Outreach**: Send and track outreach emails to target domains
- **Team Management**: Invite and manage team members
- **Settings**: Configure application settings

## Demo Mode

The application includes a demo mode that can be enabled by setting the `NEXT_PUBLIC_SHOW_DEMO_DATA` environment variable to `true`. When enabled, the application will display realistic mock data instead of making actual API calls.

This is useful for:
- Development without a backend
- Demonstrations and presentations
- Testing UI components with consistent data

To use demo mode:

1. Set `NEXT_PUBLIC_SHOW_DEMO_DATA=true` in your `.env.local` file
2. Restart the development server

To disable demo mode and use real API data:

1. Set `NEXT_PUBLIC_SHOW_DEMO_DATA=false` in your `.env.local` file (or remove the variable)
2. Restart the development server

## API Integration

The application uses a centralized API client (`lib/api.ts`) to communicate with the backend. The client handles authentication, request formatting, and error handling.

For components that need to fetch data from the API, we provide custom hooks in `lib/hooks/useApiWithDemoData.ts` that automatically handle loading states, error handling, and fallback to demo data when needed.

Example usage:

```tsx
import { useDashboardStats } from "@/lib/hooks/useApiWithDemoData";

function DashboardPage() {
  const { data, isLoading, error } = useDashboardStats();
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <div>
      <h1>Dashboard</h1>
      <p>Total Domains: {data.domains.total}</p>
      {/* ... */}
    </div>
  );
}
```
