# API Integration Improvements

This document outlines the improvements made to the API integration in the frontend application.

## 1. Fixed Infinite API Call Loop

The original implementation had an issue with the `useApiWithDemoData` hook that caused infinite API calls. This was fixed by:

- Using refs to store API call functions and demo data to prevent recreating functions on each render
- Removing dependencies from the `fetchData` useCallback to prevent it from changing on each render
- Only fetching data on mount and when manually refreshed

## 2. Implemented RTK Query for API Integration

We've replaced the custom API client with RTK Query for better caching, loading states, and error handling:

- Created an API slice with endpoints for all backend services
- Implemented proper caching with tag invalidation
- Added automatic loading and error states
- Integrated demo data support through transform responses

### Key Benefits

- **Automatic Caching**: RTK Query automatically caches responses and provides mechanisms to invalidate the cache when data changes.
- **Loading & Error States**: Each query automatically tracks loading and error states.
- **Optimistic Updates**: Mutations can be configured to optimistically update the UI before the server responds.
- **Automatic Refetching**: Queries can be configured to automatically refetch data when the user refocuses the window or reconnects.
- **Deduplicated Requests**: Multiple components requesting the same data will only trigger a single request.

## 3. Implemented Refresh Token Mechanism

We've implemented a robust token refresh mechanism that:

- Automatically refreshes the access token when it expires
- Properly stores both access and refresh tokens securely
- Handles token rotation and invalidation correctly
- Includes appropriate error handling for refresh token failures

### How It Works

1. **Token Storage**:
   - Access token and refresh token are stored in localStorage
   - Tokens are automatically included in API requests

2. **Token Expiration Check**:
   - Before making API requests, the system checks if the access token is expired
   - If expired, it automatically attempts to refresh the token

3. **Token Refresh Process**:
   - The system sends the refresh token to the server to get a new access token
   - If successful, it updates the stored tokens and continues with the original request
   - If unsuccessful, it clears the tokens and redirects to the login page

4. **Error Handling**:
   - If an API request returns a 401 Unauthorized error, the system attempts to refresh the token
   - If the refresh fails, the user is logged out and redirected to the login page

## Usage Examples

### Fetching Data

```tsx
import { useGetDashboardStatsQuery } from "@/lib/api-slice";

function DashboardPage() {
  const { data, isLoading, error } = useGetDashboardStatsQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading data</div>;
  
  return (
    <div>
      <h1>Dashboard</h1>
      <p>Total Domains: {data.domains.total}</p>
    </div>
  );
}
```

### Mutating Data

```tsx
import { useInviteTeamMemberMutation } from "@/lib/api-slice";

function InviteForm() {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("viewer");
  const [inviteTeamMember, { isLoading }] = useInviteTeamMemberMutation();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await inviteTeamMember({ email, role }).unwrap();
      toast.success("Invitation sent successfully");
    } catch (error) {
      toast.error("Failed to send invitation");
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={isLoading}>
        {isLoading ? "Sending..." : "Send Invitation"}
      </button>
    </form>
  );
}
```

## Demo Data Integration

The system supports displaying demo data when the `NEXT_PUBLIC_SHOW_DEMO_DATA` environment variable is set to `true`. This is implemented through:

1. A custom response handler in the base query that detects demo mode
2. Transform response functions that return mock data when in demo mode

This allows for easy demonstration and testing without requiring a backend.

## Configuration

To configure the API integration, set the following environment variables:

- `NEXT_PUBLIC_API_URL`: The URL of the backend API (default: http://localhost:3050)
- `NEXT_PUBLIC_SHOW_DEMO_DATA`: Set to `true` to use demo data instead of making real API calls
