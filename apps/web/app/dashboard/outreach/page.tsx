"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Send, Eye, RefreshCw, AlertCircle, Clock } from "lucide-react";
import api, { EmailTemplate, TargetDomain, OutreachEmail } from "@/lib/api";

interface SentEmail {
  id: string;
  domain: string;
  recipient: string;
  subject: string;
  sentDate: string;
  status: string;
  openDate: string | null;
}

export default function OutreachPage() {
  const searchParams = useSearchParams();
  const initialTemplateId = searchParams.get("template");

  const { toast } = useToast();
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [targetDomains, setTargetDomains] = useState<TargetDomain[]>([]);
  const [sentEmails, setSentEmails] = useState<SentEmail[]>([]);

  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState("");
  const [selectedDomainIds, setSelectedDomainIds] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch email templates
        const templates = await api.getEmailTemplates();
        setEmailTemplates(templates);

        // Fetch target domains
        const domains = await api.getTargetDomains();
        setTargetDomains(domains);

        // Set initial template if provided in URL
        if (initialTemplateId && templates.length > 0) {
          const template = templates.find((t) => t.id === initialTemplateId);
          if (template) {
            setSelectedTemplate(template.id);
            setSubject(template.subject);
            setBody(template.body);
          } else if (templates.length > 0) {
            setSelectedTemplate(templates[0].id);
            setSubject(templates[0].subject);
            setBody(templates[0].body);
          }
        } else if (templates.length > 0) {
          setSelectedTemplate(templates[0].id);
          setSubject(templates[0].subject);
          setBody(templates[0].body);
        }

        // TODO: Fetch sent emails when API endpoint is available
        // For now, we'll use the mock data
      } catch (error) {
        console.error("Failed to fetch data:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load data. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [initialTemplateId, toast]);

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = emailTemplates.find((t) => t.id === templateId);
    if (template) {
      setSubject(template.subject);
      setBody(template.body);
    }
  };

  const handleSelectDomain = (id: string) => {
    if (selectedDomainIds.includes(id)) {
      setSelectedDomainIds(
        selectedDomainIds.filter((domainId) => domainId !== id)
      );
    } else {
      setSelectedDomainIds([...selectedDomainIds, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedDomainIds.length === targetDomains.length) {
      setSelectedDomainIds([]);
    } else {
      setSelectedDomainIds(targetDomains.map((domain) => domain.id));
    }
  };

  const handleSendEmails = async () => {
    if (selectedDomainIds.length === 0) {
      toast({
        title: "No domains selected",
        description:
          "Please select at least one domain to send outreach emails.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      await api.createOutreachEmail({
        templateId: selectedTemplate,
        targetDomainIds: selectedDomainIds,
        subject,
        body,
      });

      toast({
        title: "Emails sent",
        description: `Successfully sent ${selectedDomainIds.length} outreach emails.`,
      });

      // Reset selection
      setSelectedDomainIds([]);

      // TODO: Refresh sent emails list when API endpoint is available
    } catch (error) {
      console.error("Failed to send emails:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to send emails. Please try again.",
      });
    } finally {
      setIsSending(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="container py-6">
      <h1 className="text-3xl font-bold mb-6">Email Outreach</h1>

      <Tabs defaultValue="compose">
        <TabsList className="mb-4">
          <TabsTrigger value="compose">Compose</TabsTrigger>
          <TabsTrigger value="sent">Sent Emails</TabsTrigger>
        </TabsList>

        <TabsContent value="compose">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Email Template</CardTitle>
                <CardDescription>
                  Select a template and customize your outreach email
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="template">Template</Label>
                  <Select
                    value={selectedTemplate}
                    onValueChange={handleTemplateChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {emailTemplates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="body">Email Body</Label>
                  <Textarea
                    id="body"
                    rows={12}
                    value={body}
                    onChange={(e) => setBody(e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    Use variables like name , domain , sender_name , etc.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Target Domains</CardTitle>
                <CardDescription>
                  Select domains to send your outreach emails
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={
                      selectedDomainIds.length === targetDomains.length &&
                      targetDomains.length > 0
                    }
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="select-all">Select All</Label>
                </div>

                <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
                  {targetDomains.length === 0 ? (
                    <p className="text-sm text-gray-500">
                      No target domains available. Add some domains first.
                    </p>
                  ) : (
                    targetDomains.map((domain) => (
                      <div
                        key={domain.id}
                        className="flex items-center space-x-2 p-2 border rounded"
                      >
                        <Checkbox
                          id={`domain-${domain.id}`}
                          checked={selectedDomainIds.includes(domain.id)}
                          onCheckedChange={() => handleSelectDomain(domain.id)}
                        />
                        <div>
                          <Label
                            htmlFor={`domain-${domain.id}`}
                            className="font-medium"
                          >
                            {domain.name}
                          </Label>
                          <p className="text-sm text-gray-500">{domain.url}</p>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <Button
                  className="w-full mt-4"
                  onClick={handleSendEmails}
                  disabled={isSending || selectedDomainIds.length === 0}
                >
                  {isSending ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Emails ({selectedDomainIds.length})
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sent">
          <Card>
            <CardHeader>
              <CardTitle>Sent Emails</CardTitle>
              <CardDescription>
                Track the status of your outreach emails
              </CardDescription>
            </CardHeader>
            <CardContent>
              {sentEmails.length === 0 ? (
                <p className="text-center py-4 text-gray-500">
                  No emails sent yet.
                </p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Domain</th>
                        <th className="text-left py-2">Recipient</th>
                        <th className="text-left py-2">Subject</th>
                        <th className="text-left py-2">Sent Date</th>
                        <th className="text-left py-2">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sentEmails.map((email) => (
                        <tr key={email.id} className="border-b">
                          <td className="py-2">{email.domain}</td>
                          <td className="py-2">{email.recipient}</td>
                          <td className="py-2">{email.subject}</td>
                          <td className="py-2">{email.sentDate}</td>
                          <td className="py-2">
                            <div className="flex items-center">
                              {email.status === "Opened" ? (
                                <>
                                  <Eye className="mr-1 h-4 w-4 text-green-500" />
                                  <span className="text-green-500">
                                    Opened {email.openDate}
                                  </span>
                                </>
                              ) : email.status === "Failed" ? (
                                <>
                                  <AlertCircle className="mr-1 h-4 w-4 text-red-500" />
                                  <span className="text-red-500">Failed</span>
                                </>
                              ) : (
                                <>
                                  <Clock className="mr-1 h-4 w-4 text-gray-500" />
                                  <span className="text-gray-500">Sent</span>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
