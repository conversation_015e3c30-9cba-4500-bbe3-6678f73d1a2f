"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, SlidersHorizontal, ArrowUpDown, Mail } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useGetAnalysesQuery } from "@/lib/api-slice";
import { AnalysisResult } from "@/lib/types";

// Mock data for domain analysis
const mockDomains = [
  {
    id: "1",
    domain: "techblog.com",
    title: "Tech Blog - Latest Technology News and Reviews",
    description:
      "A blog covering the latest technology news, reviews, and insights.",
    relevanceScore: 92,
    domainAuthority: 76,
    backlinks: 1240,
    traffic: 45000,
    contactEmail: "<EMAIL>",
    contactPage: "https://techblog.com/contact",
    categories: ["Technology", "Reviews"],
    keywords: ["technology", "gadgets", "reviews", "tech news"],
    lastUpdated: "2023-04-15",
  },
  {
    id: "2",
    domain: "digitalmarketing.org",
    title: "Digital Marketing Strategies and Tips",
    description:
      "Learn about digital marketing strategies, SEO, and content marketing.",
    relevanceScore: 87,
    domainAuthority: 68,
    backlinks: 980,
    traffic: 32000,
    contactEmail: "<EMAIL>",
    contactPage: "https://digitalmarketing.org/contact",
    categories: ["Marketing", "SEO"],
    keywords: ["digital marketing", "SEO", "content marketing", "social media"],
    lastUpdated: "2023-05-02",
  },
  {
    id: "3",
    domain: "webstrategy.net",
    title: "Web Strategy - Web Development and Design",
    description: "Professional web development, design, and strategy services.",
    relevanceScore: 81,
    domainAuthority: 62,
    backlinks: 750,
    traffic: 28000,
    contactEmail: "<EMAIL>",
    contactPage: "https://webstrategy.net/contact",
    categories: ["Web Development", "Design"],
    keywords: ["web development", "web design", "strategy", "UX"],
    lastUpdated: "2023-03-28",
  },
  {
    id: "4",
    domain: "contentcreators.io",
    title: "Content Creators - Professional Content Creation",
    description:
      "Professional content creation services for businesses and individuals.",
    relevanceScore: 76,
    domainAuthority: 58,
    backlinks: 620,
    traffic: 22000,
    contactEmail: "<EMAIL>",
    contactPage: "https://contentcreators.io/contact",
    categories: ["Content", "Writing"],
    keywords: ["content creation", "writing", "blogging", "copywriting"],
    lastUpdated: "2023-04-10",
  },
  {
    id: "5",
    domain: "seoexperts.com",
    title: "SEO Experts - Search Engine Optimization Services",
    description:
      "Expert SEO services to improve your website's search engine rankings.",
    relevanceScore: 72,
    domainAuthority: 65,
    backlinks: 890,
    traffic: 30000,
    contactEmail: "<EMAIL>",
    contactPage: "https://seoexperts.com/contact",
    categories: ["SEO", "Marketing"],
    keywords: ["SEO", "search engine optimization", "rankings", "keywords"],
    lastUpdated: "2023-05-08",
  },
  {
    id: "6",
    domain: "bloggingtips.com",
    title: "Blogging Tips - How to Start and Grow Your Blog",
    description: "Tips and advice for starting and growing a successful blog.",
    relevanceScore: 68,
    domainAuthority: 72,
    backlinks: 1100,
    traffic: 38000,
    contactEmail: "<EMAIL>",
    contactPage: "https://bloggingtips.com/contact",
    categories: ["Blogging", "Content"],
    keywords: ["blogging", "blog tips", "content creation", "monetization"],
    lastUpdated: "2023-04-22",
  },
  {
    id: "7",
    domain: "socialmediatoday.net",
    title: "Social Media Today - Social Media News and Strategies",
    description:
      "The latest social media news, trends, and strategies for businesses.",
    relevanceScore: 65,
    domainAuthority: 70,
    backlinks: 950,
    traffic: 42000,
    contactEmail: "<EMAIL>",
    contactPage: "https://socialmediatoday.net/contact",
    categories: ["Social Media", "Marketing"],
    keywords: ["social media", "marketing", "trends", "strategies"],
    lastUpdated: "2023-05-05",
  },
  {
    id: "8",
    domain: "marketingpro.com",
    title: "Marketing Pro - Professional Marketing Services",
    description: "Professional marketing services for businesses of all sizes.",
    relevanceScore: 61,
    domainAuthority: 59,
    backlinks: 680,
    traffic: 25000,
    contactEmail: "<EMAIL>",
    contactPage: "https://marketingpro.com/contact",
    categories: ["Marketing", "Business"],
    keywords: ["marketing", "business", "services", "strategy"],
    lastUpdated: "2023-03-15",
  },
];

export default function AnalysisPage() {
  const { toast } = useToast();
  const { data: analysisData, isLoading, error } = useGetAnalysesQuery();
  const [domains, setDomains] = useState<AnalysisResult[]>([]);
  const [allDomains, setAllDomains] = useState<AnalysisResult[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDomains, setSelectedDomains] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("relevanceScore");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState<AnalysisResult | null>(
    null
  );

  // Set domains when analysis data is loaded
  useEffect(() => {
    if (analysisData) {
      setDomains([...analysisData]);
      setAllDomains([...analysisData]);

      // Sort by relevance score by default
      const sorted = [...analysisData].sort((a, b) =>
        sortOrder === "desc"
          ? b.relevanceScore - a.relevanceScore
          : a.relevanceScore - b.relevanceScore
      );
      setDomains(sorted);
    }
  }, [analysisData, sortOrder]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Filter domains based on search term
    if (searchTerm) {
      const filtered = allDomains.filter(
        (domain) =>
          domain.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (domain.title &&
            domain.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (domain.description &&
            domain.description
              .toLowerCase()
              .includes(searchTerm.toLowerCase())) ||
          (domain.keywords &&
            domain.keywords.some((keyword: string) =>
              keyword.toLowerCase().includes(searchTerm.toLowerCase())
            ))
      );
      setDomains(filtered);
    } else {
      setDomains(allDomains);
    }
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      // Toggle sort order
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }

    // Sort domains
    const sorted = [...domains].sort((a, b) => {
      const aValue = a[field as keyof typeof a];
      const bValue = b[field as keyof typeof b];

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortOrder === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

    setDomains(sorted);
  };

  const handleSelectDomain = (id: string) => {
    if (selectedDomains.includes(id)) {
      setSelectedDomains(selectedDomains.filter((domainId) => domainId !== id));
    } else {
      setSelectedDomains([...selectedDomains, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedDomains.length === domains.length) {
      setSelectedDomains([]);
    } else {
      setSelectedDomains(domains.map((domain) => domain.id));
    }
  };

  const handleFilterCategory = (category: string | null) => {
    setFilterCategory(category);

    if (category) {
      const filtered = allDomains.filter(
        (domain) => domain.categories && domain.categories.includes(category)
      );
      setDomains(filtered);
    } else {
      setDomains(allDomains);
    }
  };

  const handleViewDetails = (domain: AnalysisResult) => {
    setSelectedDomain(domain);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  const uniqueCategories = Array.from(
    new Set(allDomains.flatMap((domain) => domain.categories || []))
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Domain Analysis</h1>
        <p className="text-muted-foreground">
          Analyze domains for backlink opportunities and compatibility with your
          website.
        </p>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <form
          onSubmit={handleSearch}
          className="flex w-full max-w-sm items-center gap-2"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search domains..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button type="submit">Search</Button>
        </form>

        <div className="flex flex-wrap items-center gap-2">
          <Select
            value={filterCategory || "all"}
            onValueChange={(value) =>
              handleFilterCategory(value === "all" ? null : value)
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {uniqueCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <SlidersHorizontal className="h-4 w-4" />
                <span>Filters</span>
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Domains</SheetTitle>
                <SheetDescription>
                  Apply filters to narrow down your domain analysis results.
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div className="space-y-2">
                  <Label>Relevance Score</Label>
                  <div className="flex items-center gap-4">
                    <Input type="number" placeholder="Min" className="w-20" />
                    <span>to</span>
                    <Input type="number" placeholder="Max" className="w-20" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Domain Authority</Label>
                  <div className="flex items-center gap-4">
                    <Input type="number" placeholder="Min" className="w-20" />
                    <span>to</span>
                    <Input type="number" placeholder="Max" className="w-20" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Categories</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {uniqueCategories.map((category) => (
                      <div key={category} className="flex items-center gap-2">
                        <Checkbox id={`category-${category}`} />
                        <Label htmlFor={`category-${category}`}>
                          {category}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Has Contact Information</Label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Checkbox id="has-email" />
                      <Label htmlFor="has-email">Email</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Checkbox id="has-contact-page" />
                      <Label htmlFor="has-contact-page">Contact Page</Label>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Reset</Button>
                  <Button>Apply Filters</Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {selectedDomains.length > 0 && (
            <Button asChild size="sm" className="flex items-center gap-2">
              <Link href="/dashboard/outreach">
                <Mail className="h-4 w-4" />
                <span>Outreach ({selectedDomains.length})</span>
              </Link>
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="cards">Card View</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Domain Analysis Results</CardTitle>
              <CardDescription>
                {isLoading
                  ? "Loading domains..."
                  : `${domains.length} domains analyzed for backlink opportunities.`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  <p className="mt-4 text-sm text-muted-foreground">
                    Loading domain analysis data...
                  </p>
                </div>
              ) : domains.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <p className="text-sm text-muted-foreground">
                    No domains found. Try uploading some domains first.
                  </p>
                  <Button asChild className="mt-4">
                    <Link href="/dashboard/upload">Upload Domains</Link>
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={
                              selectedDomains.length === domains.length &&
                              domains.length > 0
                            }
                            onCheckedChange={handleSelectAll}
                            aria-label="Select all domains"
                          />
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center gap-1">
                            <span>Domain</span>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                >
                                  <ArrowUpDown className="h-3 w-3" />
                                  <span className="sr-only">
                                    Sort by domain
                                  </span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="start">
                                <DropdownMenuItem
                                  onClick={() => handleSort("domain")}
                                >
                                  Sort by Domain
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center gap-1">
                            <span>Relevance</span>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                >
                                  <ArrowUpDown className="h-3 w-3" />
                                  <span className="sr-only">
                                    Sort by relevance
                                  </span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="start">
                                <DropdownMenuItem
                                  onClick={() => handleSort("relevanceScore")}
                                >
                                  Sort by Relevance
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center gap-1">
                            <span>DA</span>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                >
                                  <ArrowUpDown className="h-3 w-3" />
                                  <span className="sr-only">
                                    Sort by domain authority
                                  </span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="start">
                                <DropdownMenuItem
                                  onClick={() => handleSort("domainAuthority")}
                                >
                                  Sort by Domain Authority
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableHead>
                        <TableHead>Categories</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {domains.map((domain) => (
                        <TableRow key={domain.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedDomains.includes(domain.id)}
                              onCheckedChange={() =>
                                handleSelectDomain(domain.id)
                              }
                              aria-label={`Select ${domain.domain}`}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{domain.domain}</div>
                            <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                              {domain.title || domain.domain}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="font-medium">
                                {domain.relevanceScore}%
                              </div>
                              <div className="h-2 w-16 overflow-hidden rounded-full bg-muted">
                                <div
                                  className={`h-full ${getScoreColor(
                                    domain.relevanceScore
                                  )}`}
                                  style={{ width: `${domain.relevanceScore}%` }}
                                />
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{domain.domainAuthority}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {domain.categories &&
                                domain.categories.map((category: string) => (
                                  <Badge
                                    key={category}
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    {category}
                                  </Badge>
                                ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            {domain.contactEmail && (
                              <Badge
                                variant="secondary"
                                className="mr-1 text-xs"
                              >
                                Email
                              </Badge>
                            )}
                            {domain.contactPage && (
                              <Badge variant="secondary" className="text-xs">
                                Page
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewDetails(domain)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cards" className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {domains.map((domain) => (
              <Card key={domain.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-base">
                        {domain.domain}
                      </CardTitle>
                      <CardDescription className="line-clamp-1">
                        {domain.title}
                      </CardDescription>
                    </div>
                    <Checkbox
                      checked={selectedDomains.includes(domain.id)}
                      onCheckedChange={() => handleSelectDomain(domain.id)}
                      aria-label={`Select ${domain.domain}`}
                    />
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-3">
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Relevance Score</span>
                        <span className="font-medium">
                          {domain.relevanceScore}%
                        </span>
                      </div>
                      <Progress value={domain.relevanceScore} className="h-1" />
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">
                          Domain Authority:
                        </span>
                        <span className="ml-1 font-medium">
                          {domain.domainAuthority}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">
                          Backlinks:
                        </span>
                        <span className="ml-1 font-medium">
                          {domain.backlinks.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Traffic:</span>
                        <span className="ml-1 font-medium">
                          {domain.traffic.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Updated:</span>
                        <span className="ml-1 font-medium">
                          {domain.lastUpdated}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {domain.categories.map((category) => (
                        <Badge
                          key={category}
                          variant="outline"
                          className="text-xs"
                        >
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
                <div className="flex items-center justify-between border-t p-3">
                  <div className="flex gap-1">
                    {domain.contactEmail && (
                      <Badge variant="secondary" className="text-xs">
                        Email
                      </Badge>
                    )}
                    {domain.contactPage && (
                      <Badge variant="secondary" className="text-xs">
                        Contact Page
                      </Badge>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewDetails(domain)}
                  >
                    View Details
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {selectedDomain && (
        <Sheet
          open={!!selectedDomain}
          onOpenChange={() => setSelectedDomain(null)}
        >
          <SheetContent className="sm:max-w-xl">
            <SheetHeader>
              <SheetTitle>{selectedDomain.domain}</SheetTitle>
              <SheetDescription>{selectedDomain.title}</SheetDescription>
            </SheetHeader>
            <div className="mt-6 space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Description</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedDomain.description}
                </p>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Compatibility Analysis</h3>
                <div className="space-y-3">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>Relevance Score</span>
                      <span className="font-medium">
                        {selectedDomain.relevanceScore}%
                      </span>
                    </div>
                    <Progress
                      value={selectedDomain.relevanceScore}
                      className="h-2"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4 rounded-lg border p-3">
                    <div>
                      <div className="text-xs text-muted-foreground">
                        Domain Authority
                      </div>
                      <div className="text-lg font-medium">
                        {selectedDomain.domainAuthority}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">
                        Backlinks
                      </div>
                      <div className="text-lg font-medium">
                        {selectedDomain.backlinks.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">
                        Monthly Traffic
                      </div>
                      <div className="text-lg font-medium">
                        {selectedDomain.traffic.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">
                        Last Updated
                      </div>
                      <div className="text-lg font-medium">
                        {selectedDomain.lastUpdated}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Categories</h3>
                <div className="flex flex-wrap gap-1">
                  {selectedDomain.categories.map((category) => (
                    <Badge key={category} className="text-xs">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Keywords</h3>
                <div className="flex flex-wrap gap-1">
                  {selectedDomain.keywords.map((keyword) => (
                    <Badge key={keyword} variant="outline" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Contact Information</h3>
                <div className="rounded-lg border p-3 space-y-2">
                  {selectedDomain.contactEmail && (
                    <div>
                      <div className="text-xs text-muted-foreground">Email</div>
                      <div className="text-sm font-medium">
                        {selectedDomain.contactEmail}
                      </div>
                    </div>
                  )}
                  {selectedDomain.contactPage && (
                    <div>
                      <div className="text-xs text-muted-foreground">
                        Contact Page
                      </div>
                      <a
                        href={selectedDomain.contactPage}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm font-medium text-primary hover:underline"
                      >
                        {selectedDomain.contactPage}
                      </a>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setSelectedDomain(null)}
                >
                  Close
                </Button>
                <Button asChild>
                  <Link
                    href={`/dashboard/outreach?domain=${selectedDomain.id}`}
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    Send Outreach
                  </Link>
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      )}
    </div>
  );
}

function Link({
  href,
  children,
  ...props
}: React.ComponentProps<"a"> & { href: string }) {
  return (
    <a href={href} {...props}>
      {children}
    </a>
  );
}
