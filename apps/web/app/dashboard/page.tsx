"use client";

import { cn } from "@/lib/utils";
import { useAuth } from "@/components/auth-provider";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BarChart3, FileUp, Globe, Mail, TrendingUp } from "lucide-react";
import Link from "next/link";
import { useGetDashboardStatsQuery } from "@/lib/api-slice";

export default function DashboardPage() {
  const { user } = useAuth();
  const { data: stats, isLoading, error } = useGetDashboardStatsQuery();

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back, {user?.name || "User"}! Here&apos;s an overview of your
          backlink strategy.
        </p>
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <p className="text-destructive">
              Failed to load dashboard data. Please try again later.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Domains
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.targetDomains.total || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats?.domains.active || 0} active domains
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Analyzed Domains
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.targetDomains.analyzed || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats?.targetDomains.total
                  ? `${Math.round(
                      (stats.targetDomains.analyzed /
                        stats.targetDomains.total) *
                        100
                    )}% of total`
                  : "0% of total"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Outreach Emails
              </CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.emails.sent || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats?.emails.opened || 0} emails opened
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Success Rate
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.emails?.sent &&
                stats.emails.sent > 0 &&
                stats.emails.opened !== undefined
                  ? `${Math.round(
                      (stats.emails.opened / stats.emails.sent) * 100
                    )}%`
                  : "0%"}
              </div>
              <p className="text-xs text-muted-foreground">Email open rate</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center p-4">
                    <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {stats?.recentActivity ? (
                      stats.recentActivity.map(
                        (
                          activity: {
                            action: string;
                            details: string;
                            date: string;
                          },
                          index: number
                        ) => (
                          <div key={index} className="flex items-center">
                            <div className="mr-4 space-y-1">
                              <p className="text-sm font-medium leading-none">
                                {activity.action}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {activity.details}
                              </p>
                            </div>
                            <div className="ml-auto font-medium">
                              {new Date(activity.date).toLocaleDateString()}
                            </div>
                          </div>
                        )
                      )
                    ) : (
                      <p className="text-center text-muted-foreground">
                        No recent activity
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Top Opportunities</CardTitle>
                <CardDescription>
                  Domains with highest compatibility scores
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center p-4">
                    <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {stats?.topPerformingDomains ? (
                      stats.topPerformingDomains.map(
                        (
                          domain: {
                            domain: string;
                            relevanceScore: number;
                            emailsSent: number;
                            emailsOpened: number;
                          },
                          index: number
                        ) => (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center">
                              <span className="font-medium">
                                {domain.domain}
                              </span>
                              <span className="ml-auto">
                                {domain.relevanceScore}%
                              </span>
                            </div>
                            <Progress
                              value={domain.relevanceScore}
                              className="h-2"
                            />
                          </div>
                        )
                      )
                    ) : (
                      <p className="text-center text-muted-foreground">
                        No domains analyzed yet
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>
                Detailed analytics will be displayed here.
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <p className="text-muted-foreground">
                Analytics data visualization will appear here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>
                Your generated reports will be displayed here.
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <p className="text-muted-foreground">No reports generated yet.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4 sm:grid-cols-2">
            <Button asChild className="flex items-center gap-2 h-auto py-4">
              <Link href="/dashboard/upload">
                <FileUp className="h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span>Upload Domains</span>
                  <span className="text-xs font-normal text-muted-foreground">
                    Add new domains for analysis
                  </span>
                </div>
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="flex items-center gap-2 h-auto py-4"
            >
              <Link href="/dashboard/analysis">
                <BarChart3 className="h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span>View Analysis</span>
                  <span className="text-xs font-normal text-muted-foreground">
                    Check domain compatibility
                  </span>
                </div>
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="flex items-center gap-2 h-auto py-4"
            >
              <Link href="/dashboard/outreach">
                <Mail className="h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span>Send Outreach</span>
                  <span className="text-xs font-normal text-muted-foreground">
                    Contact potential partners
                  </span>
                </div>
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="flex items-center gap-2 h-auto py-4"
            >
              <Link href="/dashboard/domain">
                <Globe className="h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span>Manage Domain</span>
                  <span className="text-xs font-normal text-muted-foreground">
                    Update your domain settings
                  </span>
                </div>
              </Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Recent Outreach</CardTitle>
            <CardDescription>
              Status of your recent outreach emails
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center p-4">
                <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {stats?.emails?.sent && stats.emails.sent > 0 ? (
                  // Mock data for recent outreach since we don't have this in our stats model
                  [
                    {
                      domain: "techblog.com",
                      email: "<EMAIL>",
                      status: "Opened",
                    },
                    {
                      domain: "marketingpro.com",
                      email: "<EMAIL>",
                      status: "Sent",
                    },
                    {
                      domain: "webdesignhub.net",
                      email: "<EMAIL>",
                      status: "Sent",
                    },
                    {
                      domain: "contentcreators.io",
                      email: "<EMAIL>",
                      status: "Failed",
                    },
                  ].map(
                    (
                      outreach: {
                        domain: string;
                        email: string;
                        status: string;
                      },
                      index: number
                    ) => (
                      <div key={index} className="flex items-center">
                        <div className="mr-4 space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {outreach.domain}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {outreach.email}
                          </p>
                        </div>
                        <div className="ml-auto">
                          <span
                            className={cn(
                              "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                              outreach.status === "Sent" &&
                                "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                              outreach.status === "Opened" &&
                                "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                              outreach.status === "Failed" &&
                                "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                            )}
                          >
                            {outreach.status}
                          </span>
                        </div>
                      </div>
                    )
                  )
                ) : (
                  <p className="text-center text-muted-foreground">
                    No outreach emails sent yet
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
