"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth-provider";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import {
  CheckCircle,
  Copy,
  ExternalLink,
  Globe,
  RefreshCw,
} from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  useGetDomainByNameQuery,
  useCreateDomainMutation,
  useVerifyDomainMutation,
} from "@/lib/api-slice";

// Debounce function to limit API calls
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function DomainPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [domain, setDomain] = useState(user?.domain || "");
  const [verificationMethod, setVerificationMethod] = useState("dns");
  const [dnsValue, setDnsValue] = useState("");
  const [metaContent, setMetaContent] = useState("");
  const [fileContent, setFileContent] = useState("");

  // Debounce the domain input to prevent excessive API calls
  const debouncedDomain = useDebounce(domain, 500);

  // RTK Query hooks
  const {
    data: domainData,
    isLoading: isDomainLoading,
    error: domainError,
    refetch: refetchDomain,
  } = useGetDomainByNameQuery(debouncedDomain, {
    // Skip the query if domain is empty
    skip: !debouncedDomain,
    // Refetch on mount to ensure we have the latest data
    refetchOnMountOrArgChange: true,
  });

  const [createDomain, { isLoading: isCreating }] = useCreateDomainMutation();
  const [verifyDomain, { isLoading: isVerifying }] = useVerifyDomainMutation();

  // Derived state
  const isVerified = domainData?.verified || false;
  const verificationCode = domainData?.verificationCode || "";

  // Set verification method from domain data if available
  useEffect(() => {
    if (domainData?.verificationMethod) {
      setVerificationMethod(domainData.verificationMethod);
    }
  }, [domainData]);

  // Reset verification details when verification method changes
  useEffect(() => {
    // Clear all verification details
    setDnsValue("");
    setMetaContent("");
    setFileContent("");

    // Set the appropriate verification detail if verification code is available
    if (verificationCode) {
      if (verificationMethod === "dns") {
        setDnsValue(verificationCode);
      } else if (verificationMethod === "meta") {
        setMetaContent(verificationCode);
      } else if (verificationMethod === "file") {
        setFileContent(verificationCode);
      }
    }
  }, [verificationMethod, verificationCode]);

  // Create domain if it doesn't exist
  const handleCreateDomain = async () => {
    if (!domain) return;

    try {
      const newDomain = await createDomain({
        domain: domain,
        description: "My website domain",
      }).unwrap();

      // Explicitly refetch the domain data to get the verification code
      await refetchDomain();

      // If we still don't have the verification code, try one more time
      if (!domainData?.verificationCode) {
        setTimeout(async () => {
          await refetchDomain();
        }, 500);
      }

      toast({
        title: "Domain added",
        description: `${domain} has been added to your account. Please verify ownership using one of the methods below.`,
      });
    } catch (error) {
      console.error("Failed to add domain:", error);
      toast({
        variant: "destructive",
        title: "Failed to add domain",
        description: "Could not add domain to your account. Please try again.",
      });
    }
  };

  // Verify domain ownership
  const handleVerify = async () => {
    if (!domain) return;

    // Make sure we have a verification code
    if (!verificationCode) {
      toast({
        variant: "destructive",
        title: "Verification failed",
        description:
          "No verification code available. Please try adding the domain again.",
      });
      return;
    }

    // Validate verification details based on the selected method
    let verificationDetails = {};
    let isValid = true;
    let errorMessage = "";

    if (verificationMethod === "dns") {
      if (!dnsValue.trim()) {
        isValid = false;
        errorMessage = "Please enter the DNS TXT record value.";
      } else if (dnsValue.trim() !== verificationCode) {
        // Optionally validate that the entered value matches the verification code
        // This is also checked on the backend, but providing immediate feedback is better UX
        isValid = false;
        errorMessage =
          "The entered DNS TXT record value doesn't match the verification code.";
      } else {
        verificationDetails = { dnsValue };
      }
    } else if (verificationMethod === "meta") {
      if (!metaContent.trim()) {
        isValid = false;
        errorMessage = "Please enter the meta tag content.";
      } else if (metaContent.trim() !== verificationCode) {
        isValid = false;
        errorMessage =
          "The entered meta tag content doesn't match the verification code.";
      } else {
        verificationDetails = { metaContent };
      }
    } else if (verificationMethod === "file") {
      if (!fileContent.trim()) {
        isValid = false;
        errorMessage =
          "Please enter the file content or download and upload the verification file.";
      } else if (fileContent.trim() !== verificationCode) {
        isValid = false;
        errorMessage =
          "The entered file content doesn't match the verification code.";
      } else {
        verificationDetails = { fileContent };
      }
    }

    if (!isValid) {
      toast({
        variant: "destructive",
        title: "Verification failed",
        description: errorMessage,
      });
      return;
    }

    try {
      await verifyDomain({
        domain,
        verificationMethod,
        ...verificationDetails,
      }).unwrap();

      toast({
        title: "Domain verified successfully!",
        description: `${domain} has been verified using the ${verificationMethod} method. You can now use this domain for analysis.`,
      });
    } catch (error: unknown) {
      console.error("Verification failed:", error);

      // Extract more specific error message if available
      let displayErrorMessage =
        "Could not verify domain ownership. Please try again.";

      // Type guard for RTK Query error structure
      interface RTKQueryError {
        data?: { message?: string };
        error?: string;
      }

      // Check if error matches our expected RTK Query error structure
      const isRTKQueryError = (err: unknown): err is RTKQueryError => {
        return (
          typeof err === "object" &&
          err !== null &&
          ("data" in err || "error" in err)
        );
      };

      if (isRTKQueryError(error)) {
        if (error.data?.message) {
          displayErrorMessage = error.data.message;
        } else if (error.error && typeof error.error === "string") {
          if (error.error.includes("DNS verification failed")) {
            displayErrorMessage =
              "DNS verification failed. Please ensure you've added the correct TXT record and wait for DNS propagation (can take up to 24 hours).";
          } else if (error.error.includes("Meta tag verification failed")) {
            displayErrorMessage =
              "Meta tag verification failed. Please ensure you've added the meta tag to your website's homepage.";
          } else if (error.error.includes("File verification failed")) {
            displayErrorMessage =
              "File verification failed. Please ensure you've uploaded the verification file to your website's root directory.";
          }
        }
      } else if (error instanceof Error) {
        displayErrorMessage = error.message;
      }

      toast({
        variant: "destructive",
        title: "Verification failed",
        description: displayErrorMessage,
      });
    }
  };

  const handleCopyCode = () => {
    if (!verificationCode) return;

    navigator.clipboard.writeText(verificationCode);

    // Automatically populate the verification details based on the selected method
    if (verificationMethod === "dns") {
      setDnsValue(verificationCode);
    } else if (verificationMethod === "meta") {
      setMetaContent(verificationCode);
    } else if (verificationMethod === "file") {
      setFileContent(verificationCode);
    }

    toast({
      title: "Verification code copied",
      description:
        "The verification code has been copied to your clipboard and added to the confirmation field.",
    });
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Domain Setup</h1>
        <p className="text-muted-foreground">
          Add and verify your website domain to start analyzing backlink
          opportunities.
        </p>
      </div>

      <Tabs defaultValue="setup" className="space-y-4">
        <TabsList>
          <TabsTrigger value="setup">Domain Setup</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Your Domain</CardTitle>
              <CardDescription>
                Enter your website domain to get started with backlink analysis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="domain">Domain</Label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Globe className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="domain"
                      placeholder="example.com"
                      className="pl-9"
                      value={domain}
                      onChange={(e) => setDomain(e.target.value)}
                      disabled={isVerified}
                    />
                  </div>
                  {isVerified ? (
                    <Button
                      variant="outline"
                      onClick={() => {
                        // We can't directly modify the RTK Query state, so we'll just change the domain
                        // which will trigger a new query with the updated domain
                        setDomain(domain + " ");
                        setTimeout(() => setDomain(domain.trim()), 10);
                      }}
                    >
                      Change
                    </Button>
                  ) : domainData ? (
                    <Button
                      onClick={handleVerify}
                      disabled={!domain || isVerifying}
                    >
                      {isVerifying ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Verifying...
                        </>
                      ) : (
                        "Verify Domain"
                      )}
                    </Button>
                  ) : (
                    <Button
                      onClick={handleCreateDomain}
                      disabled={!domain || isCreating}
                    >
                      {isCreating ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Domain"
                      )}
                    </Button>
                  )}
                </div>
              </div>

              {isVerified && (
                <div className="flex items-center gap-2 rounded-md bg-green-50 p-3 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                  <CheckCircle className="h-5 w-5" />
                  <span>Domain verified successfully</span>
                </div>
              )}

              {!isVerified && domain && (
                <div className="space-y-4">
                  {/* Debug information */}
                  <div className="rounded-md border border-gray-200 p-4 text-xs">
                    <h4 className="font-semibold mb-2">Debug Information</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div>Domain:</div>
                      <div>{domain}</div>
                      <div>Domain Data:</div>
                      <div>{domainData ? "Loaded" : "Not loaded"}</div>
                      <div>Verification Code:</div>
                      <div>{verificationCode || "Not available"}</div>
                      <div>Selected Method:</div>
                      <div>{verificationMethod}</div>
                    </div>
                  </div>

                  {!verificationCode && domainData && (
                    <div className="flex items-center gap-2 rounded-md bg-yellow-50 p-3 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400">
                      <RefreshCw className="h-5 w-5" />
                      <span>
                        Loading verification code... If this persists, please
                        try refreshing.
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-auto"
                        onClick={() => refetchDomain()}
                      >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Refresh Code
                      </Button>
                    </div>
                  )}
                  <div className="space-y-2">
                    <Label>Verification Method</Label>
                    <RadioGroup
                      value={verificationMethod}
                      onValueChange={(value) => {
                        console.log("Changing verification method to:", value);
                        setVerificationMethod(value as "dns" | "meta" | "file");
                      }}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="dns" id="dns" />
                        <Label htmlFor="dns">DNS Record</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="meta" id="meta" />
                        <Label htmlFor="meta">Meta Tag</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="file" id="file" />
                        <Label htmlFor="file">HTML File</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {verificationMethod === "dns" && (
                    <div className="space-y-2 rounded-md border p-4">
                      <h3 className="font-medium">DNS Verification</h3>
                      <p className="text-sm text-muted-foreground">
                        Add the following TXT record to your domain&apos;s DNS
                        settings:
                      </p>
                      <div className="mt-2 space-y-2">
                        <div className="space-y-1">
                          <Label>Record Type</Label>
                          <div className="flex items-center justify-between rounded-md bg-muted p-2">
                            <code>TXT</code>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Label>Host</Label>
                          <div className="flex items-center justify-between rounded-md bg-muted p-2">
                            <code>_backlink-verify</code>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Label>Value</Label>
                          <div className="flex items-center justify-between rounded-md bg-muted p-2">
                            <code>
                              {verificationCode ||
                                "verification-code-not-available"}
                            </code>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleCopyCode}
                            >
                              <Copy className="h-4 w-4" />
                              <span className="sr-only">Copy code</span>
                            </Button>
                          </div>
                        </div>
                        <div className="space-y-1 mt-4">
                          <Label htmlFor="dns-value">Confirmation</Label>
                          <p className="text-xs text-muted-foreground mb-2">
                            After adding the TXT record, confirm that you've
                            added it by entering the value below:
                          </p>
                          <div className="flex gap-2">
                            <Input
                              id="dns-value"
                              placeholder="Enter the TXT record value you added"
                              value={dnsValue}
                              onChange={(e) => setDnsValue(e.target.value)}
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setDnsValue(verificationCode || "")
                              }
                            >
                              Auto-fill
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {verificationMethod === "meta" && (
                    <div className="space-y-2 rounded-md border p-4">
                      <h3 className="font-medium">Meta Tag Verification</h3>
                      <p className="text-sm text-muted-foreground">
                        Add the following meta tag to the{" "}
                        <code>&lt;head&gt;</code> section of your website&apos;s
                        homepage:
                      </p>
                      <div className="mt-2 flex items-center justify-between rounded-md bg-muted p-2">
                        <code>
                          &lt;meta name=&quot;backlink-verification&quot;
                          content=&quot;
                          {verificationCode ||
                            "verification-code-not-available"}
                          &quot;&gt;
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleCopyCode}
                        >
                          <Copy className="h-4 w-4" />
                          <span className="sr-only">Copy code</span>
                        </Button>
                      </div>
                      <div className="space-y-1 mt-4">
                        <Label htmlFor="meta-content">Confirmation</Label>
                        <p className="text-xs text-muted-foreground mb-2">
                          After adding the meta tag, confirm that you've added
                          it by entering the content value below:
                        </p>
                        <div className="flex gap-2">
                          <Input
                            id="meta-content"
                            placeholder="Enter the meta tag content value you added"
                            value={metaContent}
                            onChange={(e) => setMetaContent(e.target.value)}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setMetaContent(verificationCode || "")
                            }
                          >
                            Auto-fill
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {verificationMethod === "file" && (
                    <div className="space-y-2 rounded-md border p-4">
                      <h3 className="font-medium">HTML File Verification</h3>
                      <p className="text-sm text-muted-foreground">
                        Download the verification file and upload it to the root
                        directory of your website:
                      </p>
                      <div className="mt-2 space-y-2">
                        <div className="space-y-1">
                          <Label>File Name</Label>
                          <div className="flex items-center justify-between rounded-md bg-muted p-2">
                            <code>backlink-verify.html</code>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Label>File Path</Label>
                          <div className="flex items-center justify-between rounded-md bg-muted p-2">
                            <code>https://{domain}/backlink-verify.html</code>
                          </div>
                        </div>
                        <Button
                          className="mt-2"
                          onClick={() => {
                            // Create verification file content
                            const fileContent = `<!DOCTYPE html>
<html>
<head>
  <title>Domain Verification</title>
</head>
<body>
  <p>This file is used to verify domain ownership for Backlink.</p>
  <p>Verification code: ${
    verificationCode || "verification-code-not-available"
  }</p>
</body>
</html>`;

                            // Create a blob and download link
                            const blob = new Blob([fileContent], {
                              type: "text/html",
                            });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement("a");
                            a.href = url;
                            a.download = "backlink-verify.html";
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);

                            // Set the file content to the verification code
                            setFileContent(verificationCode || "");

                            toast({
                              title: "File downloaded",
                              description:
                                "Upload this file to your website's root directory.",
                            });
                          }}
                        >
                          Download Verification File
                        </Button>
                        <div className="space-y-1 mt-4">
                          <Label htmlFor="file-content">Confirmation</Label>
                          <p className="text-xs text-muted-foreground mb-2">
                            After uploading the file, confirm that you've
                            uploaded it by entering the verification code below:
                          </p>
                          <div className="flex gap-2">
                            <Input
                              id="file-content"
                              placeholder="Enter the verification code from the file"
                              value={fileContent}
                              onChange={(e) => setFileContent(e.target.value)}
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setFileContent(verificationCode || "")
                              }
                            >
                              Auto-fill
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {isVerified && (
            <Card>
              <CardHeader>
                <CardTitle>Domain Information</CardTitle>
                <CardDescription>
                  Basic information about your verified domain.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Domain</Label>
                    <div className="flex items-center gap-2 rounded-md border p-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span>{domain}</span>
                      <a
                        href={`https://${domain}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-auto text-primary hover:underline"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span className="sr-only">Visit website</span>
                      </a>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Verification Status</Label>
                    <div className="flex items-center gap-2 rounded-md border p-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Verified</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Verification Date</Label>
                    <div className="rounded-md border p-2">
                      {new Date().toLocaleDateString()}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Verification Method</Label>
                    <div className="rounded-md border p-2">
                      {verificationMethod === "dns" && "DNS Record"}
                      {verificationMethod === "meta" && "Meta Tag"}
                      {verificationMethod === "file" && "HTML File"}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Domain Settings</CardTitle>
              <CardDescription>
                Configure settings for your domain.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="domain-description">Domain Description</Label>
                <Textarea
                  id="domain-description"
                  placeholder="Enter a brief description of your website..."
                  rows={4}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="domain-keywords">Keywords</Label>
                <Textarea
                  id="domain-keywords"
                  placeholder="Enter keywords related to your website (comma separated)..."
                  rows={2}
                />
                <p className="text-xs text-muted-foreground">
                  These keywords will be used to improve domain compatibility
                  analysis.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
