"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth-provider";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  useGetSettingsQuery,
  useUpdateSettingsMutation,
} from "@/lib/api-slice";
import { UserSettings } from "@/lib/types";

const defaultSettings: UserSettings = {
  emailNotifications: {
    newDomainAnalysis: true,
    emailOpened: true,
    emailClicked: true,
    emailReplied: true,
    weeklyReport: true,
  },
  apiKeys: {
    mozApiKey: "",
    semrushApiKey: "",
    ahrefsApiKey: "",
  },
  emailSettings: {
    fromName: "",
    signature: "",
    replyTo: "",
  },
  appearance: {
    theme: "system",
    compactMode: false,
  },
};

export default function SettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();

  // RTK Query hooks
  const { data: settings, isLoading, error } = useGetSettingsQuery();
  const [updateSettings, { isLoading: isSaving }] = useUpdateSettingsMutation();

  const [formData, setFormData] = useState<UserSettings>(defaultSettings);

  useEffect(() => {
    if (settings) {
      setFormData(settings);
    }
  }, [settings]);

  const handleChange = (
    section: keyof UserSettings,
    field: string,
    value: string | boolean | Record<string, string | boolean>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleSave = async () => {
    try {
      await updateSettings(formData).unwrap();
      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully.",
      });
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save settings. Please try again.",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      <Tabs defaultValue="account" className="space-y-4">
        <TabsList>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Update your account details and personal information.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" defaultValue={user?.name || ""} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  defaultValue={user?.email || ""}
                  disabled
                />
                <p className="text-xs text-muted-foreground">
                  Your email address is used for login and notifications.
                </p>
              </div>
              <Separator className="my-4" />
              <div className="space-y-2">
                <Label htmlFor="current-password">Current Password</Label>
                <Input id="current-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password">New Password</Label>
                <Input id="new-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm New Password</Label>
                <Input id="confirm-password" type="password" />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Configure which notifications you want to receive.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>New Domain Analysis</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when domain analysis is completed.
                  </p>
                </div>
                <Switch
                  checked={formData.emailNotifications.newDomainAnalysis}
                  onCheckedChange={(value) =>
                    handleChange(
                      "emailNotifications",
                      "newDomainAnalysis",
                      value
                    )
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Opened</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when your outreach emails are opened.
                  </p>
                </div>
                <Switch
                  checked={formData.emailNotifications.emailOpened}
                  onCheckedChange={(value) =>
                    handleChange("emailNotifications", "emailOpened", value)
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Clicked</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when links in your emails are clicked.
                  </p>
                </div>
                <Switch
                  checked={formData.emailNotifications.emailClicked}
                  onCheckedChange={(value) =>
                    handleChange("emailNotifications", "emailClicked", value)
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Replied</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when someone replies to your emails.
                  </p>
                </div>
                <Switch
                  checked={formData.emailNotifications.emailReplied}
                  onCheckedChange={(value) =>
                    handleChange("emailNotifications", "emailReplied", value)
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Weekly Report</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive a weekly summary of your backlink activities.
                  </p>
                </div>
                <Switch
                  checked={formData.emailNotifications.weeklyReport}
                  onCheckedChange={(value) =>
                    handleChange("emailNotifications", "weeklyReport", value)
                  }
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="api-keys">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Configure API keys for third-party services used for domain
                analysis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="moz-api-key">Moz API Key</Label>
                <Input
                  id="moz-api-key"
                  type="password"
                  value={formData.apiKeys.mozApiKey || ""}
                  onChange={(e) =>
                    handleChange("apiKeys", "mozApiKey", e.target.value)
                  }
                />
                <p className="text-xs text-muted-foreground">
                  Used for domain authority and backlink data.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="semrush-api-key">SEMrush API Key</Label>
                <Input
                  id="semrush-api-key"
                  type="password"
                  value={formData.apiKeys.semrushApiKey || ""}
                  onChange={(e) =>
                    handleChange("apiKeys", "semrushApiKey", e.target.value)
                  }
                />
                <p className="text-xs text-muted-foreground">
                  Used for keyword and traffic data.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ahrefs-api-key">Ahrefs API Key</Label>
                <Input
                  id="ahrefs-api-key"
                  type="password"
                  value={formData.apiKeys.ahrefsApiKey || ""}
                  onChange={(e) =>
                    handleChange("apiKeys", "ahrefsApiKey", e.target.value)
                  }
                />
                <p className="text-xs text-muted-foreground">
                  Used for comprehensive backlink analysis.
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="email">
          <Card>
            <CardHeader>
              <CardTitle>Email Settings</CardTitle>
              <CardDescription>
                Configure your email settings for outreach campaigns.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="from-name">From Name</Label>
                <Input
                  id="from-name"
                  value={formData.emailSettings.fromName}
                  onChange={(e) =>
                    handleChange("emailSettings", "fromName", e.target.value)
                  }
                  placeholder="Your Name or Company Name"
                />
                <p className="text-xs text-muted-foreground">
                  This name will appear in the "From" field of your emails.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="reply-to">Reply-To Email</Label>
                <Input
                  id="reply-to"
                  type="email"
                  value={formData.emailSettings.replyTo}
                  onChange={(e) =>
                    handleChange("emailSettings", "replyTo", e.target.value)
                  }
                  placeholder="<EMAIL>"
                />
                <p className="text-xs text-muted-foreground">
                  Replies to your outreach emails will be sent to this address.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="signature">Email Signature</Label>
                <Textarea
                  id="signature"
                  rows={5}
                  value={formData.emailSettings.signature}
                  onChange={(e) =>
                    handleChange("emailSettings", "signature", e.target.value)
                  }
                  placeholder="Your email signature"
                />
                <p className="text-xs text-muted-foreground">
                  This signature will be automatically added to your outreach
                  emails.
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>
                Customize the appearance of the application.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="theme">Theme</Label>
                <Select
                  value={formData.appearance.theme}
                  onValueChange={(value) =>
                    handleChange(
                      "appearance",
                      "theme",
                      value as "light" | "dark" | "system"
                    )
                  }
                >
                  <SelectTrigger id="theme">
                    <SelectValue placeholder="Select a theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose your preferred color theme.
                </p>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Compact Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Use a more compact layout to fit more content on screen.
                  </p>
                </div>
                <Switch
                  checked={formData.appearance.compactMode}
                  onCheckedChange={(value) =>
                    handleChange("appearance", "compactMode", value)
                  }
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
