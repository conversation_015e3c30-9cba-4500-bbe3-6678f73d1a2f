"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { AlertCircle, FileUp, Trash2, Upload } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
// Import types
import type { TargetDomain } from "@/lib/types";

export default function UploadPage() {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewData, setPreviewData] = useState<string[][]>([]);
  const [uploadedDomains, setUploadedDomains] = useState<string[]>([]);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // Reset states
    setValidationErrors([]);
    setIsValidating(true);

    if (
      selectedFile.type !== "text/csv" &&
      !selectedFile.name.endsWith(".csv")
    ) {
      toast({
        title: "Invalid file type",
        description: "Please upload a CSV file.",
        variant: "destructive",
      });
      setIsValidating(false);
      return;
    }

    setFile(selectedFile);

    // Read and preview the CSV file
    const reader = new FileReader();
    reader.onload = (event) => {
      const text = event.target?.result as string;
      const lines = text.split("\n").filter((line) => line.trim() !== "");

      if (lines.length === 0) {
        setValidationErrors(["CSV file is empty"]);
        setIsValidating(false);
        return;
      }

      // Parse CSV properly handling quoted values
      const data = lines.map((line) => {
        const result = [];
        let cell = "";
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
          const char = line[i];

          if (char === '"' && (i === 0 || line[i - 1] !== "\\")) {
            inQuotes = !inQuotes;
          } else if (char === "," && !inQuotes) {
            result.push(cell.trim());
            cell = "";
          } else {
            cell += char;
          }
        }

        result.push(cell.trim());
        return result;
      });

      // Extract headers
      const headers = data[0];
      setCsvHeaders(headers);

      // Validate if domain column exists
      const domainColumnIndex = headers.findIndex(
        (h) =>
          h.toLowerCase() === "domain" ||
          h.toLowerCase() === "domain name" ||
          h.toLowerCase() === "url"
      );

      if (domainColumnIndex === -1) {
        setValidationErrors(["CSV file must have a 'domain' column"]);
      }

      // Only show first 10 rows for preview (including header)
      setPreviewData(data.slice(0, Math.min(10, data.length)));
      setIsValidating(false);
    };

    reader.onerror = () => {
      setValidationErrors(["Failed to read the CSV file"]);
      setIsValidating(false);
    };

    reader.readAsText(selectedFile);
  };

  const handleUpload = async () => {
    if (!file) return;

    // Check for validation errors
    if (validationErrors.length > 0) {
      toast({
        variant: "destructive",
        title: "Validation errors",
        description: validationErrors.join(". "),
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(10); // Start progress

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append("file", file);

      // Simulate progress during upload
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const next = prev + Math.floor(Math.random() * 15);
          return next > 90 ? 90 : next; // Cap at 90% until complete
        });
      }, 500);

      // Upload the file using fetch directly to better handle multipart/form-data
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:3050"
        }/target-domains/upload`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
          },
          body: formData,
        }
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Upload failed with status: ${response.status}`
        );
      }

      const domains = (await response.json()) as TargetDomain[];

      // Extract domain names for display
      const domainNames = domains
        .map((d: TargetDomain) => {
          // Handle different possible domain property names
          return d.domain || "";
        })
        .filter(Boolean);

      setUploadedDomains(domainNames);

      toast({
        title: "Upload successful",
        description: `${domainNames.length} domains have been uploaded for analysis.`,
      });
    } catch (error) {
      console.error("Upload failed:", error);

      // Extract error message if available
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Could not upload domains. Please try again.";

      toast({
        variant: "destructive",
        title: "Upload failed",
        description: errorMessage,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setPreviewData([]);
    setCsvHeaders([]);
    setValidationErrors([]);
    setUploadProgress(0);
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Upload Domains</h1>
        <p className="text-muted-foreground">
          Upload a CSV file containing domains for analysis.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upload CSV File</CardTitle>
          <CardDescription>
            Upload a CSV file with domains to analyze for backlink
            opportunities.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>CSV Format Requirements</AlertTitle>
            <AlertDescription>
              <p className="mb-2">Your CSV file must include:</p>
              <ul className="list-disc pl-5 mb-2">
                <li>A header row with column names</li>
                <li>A column named "domain" or "domain name" (required)</li>
                <li>One domain per row (without http:// or https://)</li>
              </ul>
              <p className="mb-2">Optional columns that will be imported:</p>
              <ul className="list-disc pl-5">
                <li>
                  <strong>title</strong> - Website title
                </li>
                <li>
                  <strong>description</strong> - Website description
                </li>
                <li>
                  <strong>categories</strong> - Semicolon-separated categories
                  (e.g., "tech;blog;marketing")
                </li>
                <li>
                  <strong>notes</strong> - Additional notes
                </li>
              </ul>
              <div className="mt-3 flex items-center justify-between">
                <Button variant="outline" size="sm" asChild>
                  <a href="/sample-domains.csv" download>
                    Download Sample CSV Template
                  </a>
                </Button>
                <p className="text-xs text-muted-foreground">
                  Use our template to get started quickly
                </p>
              </div>
            </AlertDescription>
          </Alert>

          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Validation Errors</AlertTitle>
              <AlertDescription>
                <ul className="list-disc pl-4 mt-2">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {isValidating && (
            <Alert variant="default">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Validating CSV</AlertTitle>
              <AlertDescription>
                Checking your CSV file format...
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col items-center justify-center gap-4 rounded-lg border border-dashed p-8">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <FileUp className="h-6 w-6 text-primary" />
            </div>
            <div className="space-y-1 text-center">
              <p className="text-sm font-medium">
                {file ? file.name : "Drag and drop your CSV file here"}
              </p>
              <p className="text-xs text-muted-foreground">
                {file
                  ? `${(file.size / 1024).toFixed(2)} KB`
                  : "CSV files only, up to 5MB"}
              </p>
            </div>
            <div className="flex gap-2">
              {!file ? (
                <div>
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".csv"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                  <Label
                    htmlFor="file-upload"
                    className="cursor-pointer rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
                  >
                    Select File
                  </Label>
                </div>
              ) : (
                <>
                  <Button
                    onClick={handleUpload}
                    disabled={isUploading}
                    className="flex items-center gap-2"
                  >
                    {isUploading ? (
                      <>
                        <Upload className="h-4 w-4 animate-pulse" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Upload
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleRemoveFile}
                    disabled={isUploading}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                </>
              )}
            </div>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {previewData.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Preview</h3>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {csvHeaders.length > 0 ? (
                        // Use actual CSV headers
                        csvHeaders
                          .slice(0, 5)
                          .map((header, index) => (
                            <TableHead key={index}>
                              {header || `Column ${index + 1}`}
                            </TableHead>
                          ))
                      ) : (
                        // Fallback headers
                        <>
                          <TableHead>Domain</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Notes</TableHead>
                        </>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Skip header row if we have headers */}
                    {(csvHeaders.length > 0
                      ? previewData.slice(1)
                      : previewData
                    ).map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {row.slice(0, 5).map((cell, cellIndex) => (
                          <TableCell key={cellIndex}>{cell || "-"}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-xs text-muted-foreground">
                Showing preview of first {previewData.length} rows.
              </p>
            </div>
          )}
        </CardContent>
        {uploadedDomains.length > 0 && (
          <CardFooter className="flex flex-col items-start gap-4">
            <div className="space-y-2 w-full">
              <h3 className="text-sm font-medium">Uploaded Domains</h3>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Domain</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {uploadedDomains.map((domain, index) => (
                      <TableRow key={index}>
                        <TableCell>{domain}</TableCell>
                        <TableCell>
                          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                            Pending Analysis
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            <Button asChild>
              <a href="/dashboard/analysis">View Analysis</a>
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
