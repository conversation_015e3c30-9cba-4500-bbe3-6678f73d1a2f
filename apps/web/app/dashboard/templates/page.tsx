"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Textarea } from "@/components/ui/textarea";
import {
  PlusCircle,
  Trash2,
  Edit,
  Copy,
  AlertCircle,
  Info,
} from "lucide-react";
import { TemplateVariablesHelper } from "@/components/email/template-variables-helper";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  useGetEmailTemplatesQuery,
  useCreateEmailTemplateMutation,
  useUpdateEmailTemplateMutation,
  useDeleteEmailTemplateMutation,
} from "@/lib/api-slice";
import { EmailTemplate } from "@/lib/types";

export default function TemplatesPage() {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentTemplateId, setCurrentTemplateId] = useState<string | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] =
    useState<EmailTemplate | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    subject: "",
    body: "",
    variables: [] as string[],
  });

  const bodyTextareaRef = useRef<HTMLTextAreaElement>(null);

  // RTK Query hooks
  const { data: templates = [], isLoading } = useGetEmailTemplatesQuery();
  const [createTemplate, { isLoading: isCreating }] =
    useCreateEmailTemplateMutation();
  const [updateTemplate, { isLoading: isUpdating }] =
    useUpdateEmailTemplateMutation();
  const [deleteTemplate, { isLoading: isDeleting }] =
    useDeleteEmailTemplateMutation();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      subject: "",
      body: "",
      variables: [],
    });
    setCurrentTemplateId(null);
    setIsEditing(false);
  };

  const handleCreateOrUpdate = async () => {
    try {
      if (isEditing && currentTemplateId) {
        await updateTemplate({
          id: currentTemplateId,
          template: formData,
        }).unwrap();
        toast({
          title: "Template updated",
          description: "Email template has been updated successfully.",
        });
      } else {
        await createTemplate(formData).unwrap();
        toast({
          title: "Template created",
          description: "New email template has been created successfully.",
        });
      }
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error("Failed to save template:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save template. Please try again.",
      });
    }
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setFormData({
      name: template.name,
      subject: template.subject,
      body: template.body,
      variables: template.variables || [],
    });
    setCurrentTemplateId(template.id);
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  const handleDuplicateTemplate = (template: EmailTemplate) => {
    setFormData({
      name: `${template.name} (Copy)`,
      subject: template.subject,
      body: template.body,
      variables: template.variables || [],
    });
    setIsEditing(false);
    setCurrentTemplateId(null);
    setIsDialogOpen(true);
  };

  const handleDeleteTemplate = (template: EmailTemplate) => {
    setTemplateToDelete(template);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTemplate = async () => {
    if (!templateToDelete) return;

    try {
      await deleteTemplate(templateToDelete.id).unwrap();
      toast({
        title: "Template deleted",
        description: "Email template has been deleted successfully.",
      });
    } catch (error) {
      console.error("Failed to delete template:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete template. Please try again.",
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setTemplateToDelete(null);
    }
  };

  // Handle inserting a variable into the template body or subject
  const handleInsertVariable = (variable: string) => {
    if (bodyTextareaRef.current) {
      const textarea = bodyTextareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      const newText = `${before}{{${variable}}}${after}`;

      setFormData((prev) => ({
        ...prev,
        body: newText,
      }));

      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        const newCursorPos = start + variable.length + 4; // +4 for the {{ and }}
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    }
  };

  // Add a custom variable to the template
  const handleAddCustomVariable = (variable: string) => {
    if (!formData.variables.includes(variable)) {
      setFormData((prev) => ({
        ...prev,
        variables: [...prev.variables, variable],
      }));
    }
  };

  // Remove a custom variable from the template
  const handleRemoveCustomVariable = (variable: string) => {
    setFormData((prev) => ({
      ...prev,
      variables: prev.variables.filter((v) => v !== variable),
    }));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Email Templates</h1>
        <p className="text-muted-foreground">
          Create and manage email templates for your outreach campaigns.
        </p>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <PlusCircle className="mr-2 h-4 w-4" />
                New Template
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {isEditing ? "Edit Template" : "Create Template"}
                </DialogTitle>
                <DialogDescription>
                  {isEditing
                    ? "Update your email template details below."
                    : "Create a new email template for your outreach campaigns."}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g., Initial Outreach"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="subject">Email Subject</Label>
                  <Input
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    placeholder="e.g., Collaboration opportunity with {{domain}}"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="body">Email Body</Label>
                  <Textarea
                    id="body"
                    name="body"
                    ref={bodyTextareaRef}
                    value={formData.body}
                    onChange={handleInputChange}
                    placeholder="Dear {{name}},

I came across your website {{domain}} and was impressed by your content about {{industry}}.

Would you be interested in a collaboration opportunity?

Best regards,
Your Name"
                    className="min-h-[200px]"
                  />
                </div>

                <div className="mt-4 border rounded-md p-3 bg-muted/30">
                  <TemplateVariablesHelper
                    onInsertVariable={handleInsertVariable}
                    customVariables={formData.variables}
                    onAddCustomVariable={handleAddCustomVariable}
                    onRemoveCustomVariable={handleRemoveCustomVariable}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateOrUpdate}
                  disabled={isCreating || isUpdating}
                >
                  {isEditing ? "Update Template" : "Create Template"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {templates.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-medium">No templates yet</h3>
              <p className="text-sm text-muted-foreground">
                Create your first email template to get started with outreach.
              </p>
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(true)}
                className="mt-4"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Template
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Your Email Templates</CardTitle>
            <CardDescription>
              Manage your saved email templates for outreach campaigns.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead className="w-[150px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">
                      {template.name}
                    </TableCell>
                    <TableCell>{template.subject}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditTemplate(template)}
                          title="Edit template"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDuplicateTemplate(template)}
                          title="Duplicate template"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteTemplate(template)}
                          title="Delete template"
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the template "
              {templateToDelete?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTemplate}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
