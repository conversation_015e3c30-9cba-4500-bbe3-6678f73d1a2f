import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="container pt-24 pb-12 md:pt-32 md:pb-16">
      <div className="flex flex-col items-center gap-4 text-center">
        <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
          Supercharge Your <span className="text-primary">Backlink</span> Strategy
        </h1>
        <p className="max-w-[42rem] text-lg text-muted-foreground sm:text-xl">
          Analyze domains, find opportunities, and reach out to potential partners all in one platform.
        </p>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Link href="/register">
            <Button size="lg">Get Started</Button>
          </Link>
          <Link href="/login">
            <Button variant="outline" size="lg">
              Sign In
            </Button>
          </Link>
        </div>
      </div>
      <div className="mt-16 flex justify-center">
        <div className="relative w-full max-w-5xl overflow-hidden rounded-xl border bg-background shadow-xl">
          <div className="aspect-[16/9] bg-muted/50">
            {/* Dashboard preview image would go here */}
            <div className="flex h-full items-center justify-center text-2xl font-semibold text-muted-foreground">
              Dashboard Preview
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
