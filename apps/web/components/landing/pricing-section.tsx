import { Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export function PricingSection() {
  return (
    <section className="container py-16 md:py-24 bg-muted/50">
      <div className="mx-auto flex max-w-[58rem] flex-col items-center justify-center gap-4 text-center">
        <h2 className="text-3xl font-bold leading-[1.1] sm:text-3xl md:text-5xl">Simple, Transparent Pricing</h2>
        <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
          Choose the plan that's right for you and start finding backlink opportunities today.
        </p>
      </div>
      <div className="mx-auto grid max-w-5xl gap-6 py-8 lg:grid-cols-3 lg:gap-8">
        {pricingPlans.map((plan) => (
          <Card key={plan.name} className={plan.featured ? "border-primary shadow-lg" : ""}>
            {plan.featured && (
              <div className="absolute -top-5 left-0 right-0 mx-auto w-fit rounded-full bg-primary px-3 py-1 text-xs font-medium text-primary-foreground">
                Most Popular
              </div>
            )}
            <CardHeader className="flex flex-col gap-1">
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-4 flex items-baseline text-3xl font-bold">
                ${plan.price}
                <span className="ml-1 text-sm font-medium text-muted-foreground">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="grid gap-2 text-sm">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-primary" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Link href="/register" className="w-full">
                <Button size="lg" className="w-full" variant={plan.featured ? "default" : "outline"}>
                  Get Started
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </section>
  )
}

const pricingPlans = [
  {
    name: "Starter",
    description: "Perfect for individuals and small websites",
    price: 29,
    featured: false,
    features: [
      "100 domains per month",
      "Basic domain analysis",
      "Email finder",
      "10 outreach emails per day",
      "Email templates",
      "Basic reporting",
    ],
  },
  {
    name: "Professional",
    description: "For growing websites and businesses",
    price: 79,
    featured: true,
    features: [
      "500 domains per month",
      "Advanced domain analysis",
      "Email & contact finder",
      "50 outreach emails per day",
      "Custom email templates",
      "Advanced reporting",
      "Team collaboration",
    ],
  },
  {
    name: "Enterprise",
    description: "For large websites and agencies",
    price: 199,
    featured: false,
    features: [
      "Unlimited domains",
      "Premium domain analysis",
      "Advanced contact discovery",
      "Unlimited outreach emails",
      "AI-powered templates",
      "Custom reporting",
      "Priority support",
      "API access",
    ],
  },
]
