import { BarChart3, Upload, Search, Mail, LineChart, UserChe<PERSON> } from "lucide-react"

export function FeaturesSection() {
  return (
    <section className="container py-16 md:py-24">
      <div className="mx-auto flex max-w-[58rem] flex-col items-center justify-center gap-4 text-center">
        <h2 className="text-3xl font-bold leading-[1.1] sm:text-3xl md:text-5xl">Features</h2>
        <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
          Everything you need to analyze domains and reach out to potential partners.
        </p>
      </div>
      <div className="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:grid-cols-3 lg:max-w-5xl lg:gap-8 mt-12">
        {features.map((feature) => (
          <div key={feature.title} className="relative overflow-hidden rounded-lg border bg-background p-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
              <feature.icon className="h-6 w-6" />
            </div>
            <div className="mt-4 space-y-2">
              <h3 className="font-bold">{feature.title}</h3>
              <p className="text-sm text-muted-foreground">{feature.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}

const features = [
  {
    title: "Domain Analysis",
    description: "Analyze domains for backlink opportunities with our advanced algorithms.",
    icon: BarChart3,
  },
  {
    title: "CSV Upload",
    description: "Easily upload and process lists of domains for analysis.",
    icon: Upload,
  },
  {
    title: "Contact Discovery",
    description: "Automatically find contact information for potential partners.",
    icon: Search,
  },
  {
    title: "Email Outreach",
    description: "Send personalized outreach emails directly from the platform.",
    icon: Mail,
  },
  {
    title: "Performance Tracking",
    description: "Track the performance of your outreach campaigns.",
    icon: LineChart,
  },
  {
    title: "User Management",
    description: "Manage your team and permissions with ease.",
    icon: UserCheck,
  },
]
