"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  FileUp,
  Globe,
  Home,
  Mail,
  Settings,
  Users,
} from "lucide-react";

const sidebarLinks = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "Domain Setup",
    href: "/dashboard/domain",
    icon: Globe,
  },
  {
    title: "Upload Domains",
    href: "/dashboard/upload",
    icon: FileUp,
  },
  {
    title: "Analysis",
    href: "/dashboard/analysis",
    icon: BarChart3,
  },
  {
    title: "Outreach",
    href: "/dashboard/outreach",
    icon: Mail,
  },
  {
    title: "Team",
    href: "/dashboard/team",
    icon: Users,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
];

export function DashboardSidebar() {
  const pathname = usePathname();

  return (
    <aside className="hidden w-64 flex-shrink-0 border-r md:block">
      <div className="flex h-full flex-col gap-2 p-4">
        <nav className="grid gap-1 py-4">
          {sidebarLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent",
                pathname === link.href
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground"
              )}
            >
              <link.icon className="h-4 w-4" />
              {link.title}
            </Link>
          ))}
        </nav>
      </div>
    </aside>
  );
}
