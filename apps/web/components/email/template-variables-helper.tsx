import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Info, Plus } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Common template variables that are available in the system
const COMMON_VARIABLES = [
  { name: "domain", description: "The target domain name" },
  { name: "name", description: "The contact's name if available" },
  { name: "website", description: "The full website URL" },
  { name: "industry", description: "The industry of the target domain" },
  { name: "category", description: "The category of the target domain" },
  { name: "company", description: "The company name if available" },
  { name: "date", description: "The current date" },
];

interface TemplateVariablesHelperProps {
  onInsertVariable: (variable: string) => void;
  customVariables?: string[];
  onAddCustomVariable?: (variable: string) => void;
  onRemoveCustomVariable?: (variable: string) => void;
}

export function TemplateVariablesHelper({
  onInsertVariable,
  customVariables = [],
  onAddCustomVariable,
  onRemoveCustomVariable,
}: TemplateVariablesHelperProps) {
  const [newVariable, setNewVariable] = React.useState("");

  const handleAddCustomVariable = () => {
    if (
      newVariable &&
      !customVariables.includes(newVariable) &&
      onAddCustomVariable
    ) {
      onAddCustomVariable(newVariable);
      setNewVariable("");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h4 className="text-sm font-medium">Template Variables</h4>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs text-xs">
                  Variables will be replaced with actual values when sending
                  emails. Use them in your template by wrapping them in double
                  curly braces, e.g., {{ domain }}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-xs">Common Variables</Label>
        <div className="flex flex-wrap gap-2">
          {COMMON_VARIABLES.map((variable) => (
            <TooltipProvider key={variable.name}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary"
                    onClick={() => onInsertVariable(variable.name)}
                  >
                    {`{{${variable.name}}}`}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">{variable.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </div>

      {onAddCustomVariable && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Custom Variables</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Plus className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">
                      Add Custom Variable
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Create a custom variable to use in your templates.
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Name
                      </Label>
                      <Input
                        id="name"
                        value={newVariable}
                        onChange={(e) => setNewVariable(e.target.value)}
                        className="col-span-3"
                        placeholder="e.g., signature"
                      />
                    </div>
                  </div>
                  <Button size="sm" onClick={handleAddCustomVariable}>
                    Add Variable
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-wrap gap-2">
            {customVariables.length === 0 ? (
              <p className="text-xs text-muted-foreground">
                No custom variables added yet.
              </p>
            ) : (
              customVariables.map((variable) => (
                <Badge
                  key={variable}
                  variant="secondary"
                  className="cursor-pointer group flex items-center gap-1"
                >
                  <span
                    onClick={() => onInsertVariable(variable)}
                  >{`{{${variable}}}`}</span>
                  {onRemoveCustomVariable && (
                    <button
                      className="ml-1 rounded-full bg-muted p-0.5 opacity-70 hover:opacity-100"
                      onClick={() => onRemoveCustomVariable(variable)}
                    >
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9 3L3 9M3 3L9 9"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  )}
                </Badge>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
