"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { User } from "@/lib/api";
import {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
} from "@/lib/api-slice";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name?: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const initialAuthCheckDone = useRef(false);
  const router = useRouter();
  const pathname = usePathname();

  // RTK Query hooks
  const [loginMutation] = useLoginMutation();
  const [registerMutation] = useRegisterMutation();
  const [logoutMutation] = useLogoutMutation();

  // Check if we have a valid token
  const checkAuth = useCallback((): boolean => {
    if (typeof window === "undefined") return false;

    const accessToken = localStorage.getItem("accessToken");
    const refreshToken = localStorage.getItem("refreshToken");

    return !!accessToken && !!refreshToken;
  }, []);

  // Set initial authentication state based on token presence
  useEffect(() => {
    if (!initialAuthCheckDone.current) {
      const hasTokens = checkAuth();
      setIsAuthenticated(hasTokens);
      initialAuthCheckDone.current = true;

      // If no tokens, we can immediately set loading to false
      if (!hasTokens) {
        setIsLoading(false);
      }
    }
  }, [checkAuth]);

  // Skip the query if no token is present to avoid unnecessary API calls
  const skipQuery = !checkAuth();

  const {
    data: userData,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser,
  } = useGetCurrentUserQuery(undefined, {
    skip: skipQuery,
    // Ensure the query is refetched when the component mounts
    refetchOnMountOrArgChange: true,
  });

  // Set user data when it's loaded
  useEffect(() => {
    if (userData && !userError) {
      setUser(userData);
      setIsAuthenticated(true);
      setIsLoading(false);
    } else if (userError) {
      console.error("Error fetching user data:", userError);
      // Only clear tokens if we get a 401 error
      if ("status" in userError && userError.status === 401) {
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        setUser(null);
        setIsAuthenticated(false);
      }
      setIsLoading(false);
    } else if (skipQuery) {
      // If we're skipping the query because there's no token, we're not authenticated
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, [userData, userError, skipQuery]);

  // Redirect based on auth status, but only after initial loading is complete
  useEffect(() => {
    // Don't redirect during initial loading or if we're still fetching user data
    if (isLoading || isUserLoading) {
      return;
    }

    const isAuthRoute = pathname === "/login" || pathname === "/register";
    const isPublicRoute =
      pathname === "/" || pathname === "/about" || pathname === "/pricing";

    // Add a small delay to prevent rapid redirects that can cause loops
    const redirectTimer = setTimeout(() => {
      // Only redirect if we're sure about authentication status
      if (!isAuthenticated && !isAuthRoute && !isPublicRoute) {
        // Store the current path to redirect back after login
        if (typeof window !== "undefined" && pathname !== "/login") {
          sessionStorage.setItem("redirectAfterLogin", pathname);
        }
        router.push("/login");
      } else if (isAuthenticated && user && isAuthRoute) {
        // Check if we have a stored redirect path
        const redirectPath = sessionStorage.getItem("redirectAfterLogin");
        if (redirectPath && redirectPath !== "/login") {
          sessionStorage.removeItem("redirectAfterLogin");
          router.push(redirectPath);
        } else {
          router.push("/dashboard");
        }
      }
    }, 100); // Small delay to prevent rapid redirects

    return () => clearTimeout(redirectTimer);
  }, [isAuthenticated, user, isLoading, isUserLoading, pathname, router]);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await loginMutation({ email, password }).unwrap();

      // Store tokens in localStorage
      if (response.accessToken && response.refreshToken) {
        localStorage.setItem("accessToken", response.accessToken);
        localStorage.setItem("refreshToken", response.refreshToken);
      }

      setUser(response.user);
      setIsAuthenticated(true);

      // Check if we have a stored redirect path
      const redirectPath = sessionStorage.getItem("redirectAfterLogin");
      if (redirectPath) {
        sessionStorage.removeItem("redirectAfterLogin");
        router.push(redirectPath);
      } else {
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name?: string) => {
    setIsLoading(true);
    try {
      const response = await registerMutation({
        email,
        password,
        name,
      }).unwrap();

      // Store tokens in localStorage
      if (response.accessToken && response.refreshToken) {
        localStorage.setItem("accessToken", response.accessToken);
        localStorage.setItem("refreshToken", response.refreshToken);
      }

      setUser(response.user);
      setIsAuthenticated(true);

      // Check if we have a stored redirect path
      const redirectPath = sessionStorage.getItem("redirectAfterLogin");
      if (redirectPath) {
        sessionStorage.removeItem("redirectAfterLogin");
        router.push(redirectPath);
      } else {
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    try {
      // Call the logout mutation
      logoutMutation();
    } finally {
      // Always clear tokens and user state, even if the API call fails
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      setUser(null);
      setIsAuthenticated(false);
      router.push("/");
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        login,
        register,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
