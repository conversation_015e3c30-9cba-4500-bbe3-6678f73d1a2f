# RankMesh Backlink Intelligence API - Configuration Guide

This document provides comprehensive configuration instructions for the RankMesh Backlink Intelligence microservice.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Database Configuration](#database-configuration)
3. [Redis Configuration](#redis-configuration)
4. [Third-Party API Keys](#third-party-api-keys)
5. [Queue Configuration](#queue-configuration)
6. [Scoring Algorithm Configuration](#scoring-algorithm-configuration)
7. [Development vs Production](#development-vs-production)
8. [Docker Configuration](#docker-configuration)
9. [Troubleshooting](#troubleshooting)

## Environment Variables

### Required Variables

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=backlink
DB_SSL=false

# JWT Configuration
JWT_SECRET=your-secret-key-minimum-32-characters
JWT_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0

# Application Configuration
PORT=3050
NODE_ENV=development
```

### Optional Variables

```bash
# Third-Party API Keys (optional but recommended)
AHREFS_API_KEY=your_ahrefs_api_key
SIMILARWEB_API_KEY=your_similarweb_api_key
WHOISXML_API_KEY=your_whoisxml_api_key

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Queue Configuration
QUEUE_DEFAULT_JOB_ATTEMPTS=3
QUEUE_DEFAULT_JOB_BACKOFF_DELAY=2000

# Scoring Algorithm Weights (must sum to 1.0)
SCORING_DOMAIN_RATING_WEIGHT=0.30
SCORING_TRAFFIC_VOLUME_WEIGHT=0.20
SCORING_KEYWORD_RELEVANCE_WEIGHT=0.20
SCORING_WEBSITE_AGE_WEIGHT=0.10
SCORING_INDUSTRY_MATCH_WEIGHT=0.10
SCORING_COUNTRY_LANGUAGE_MATCH_WEIGHT=0.05
SCORING_CONTACT_INFO_PRESENT_WEIGHT=0.05

# Web Scraping Configuration
SCRAPER_USER_AGENT=Mozilla/5.0 (compatible; RankMesh-Bot/1.0)
SCRAPER_REQUEST_TIMEOUT=10000
SCRAPER_MAX_REDIRECTS=3
SCRAPER_DELAY_BETWEEN_REQUESTS=1500

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs
```

## Database Configuration

### PostgreSQL Setup

1. **Install PostgreSQL** (if not already installed):
   ```bash
   # macOS
   brew install postgresql
   
   # Ubuntu/Debian
   sudo apt-get install postgresql postgresql-contrib
   
   # Windows
   # Download from https://www.postgresql.org/download/windows/
   ```

2. **Create Database**:
   ```sql
   CREATE DATABASE backlink;
   CREATE USER backlink_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE backlink TO backlink_user;
   ```

3. **Update Environment Variables**:
   ```bash
   DB_HOST=localhost
   DB_PORT=5432
   DB_USERNAME=backlink_user
   DB_PASSWORD=your_password
   DB_DATABASE=backlink
   ```

### Database Features

- **Auto-migration**: The application automatically creates/updates database schema
- **Connection pooling**: Configured for optimal performance
- **SSL support**: Enable with `DB_SSL=true` for production

## Redis Configuration

### Redis Setup

1. **Install Redis**:
   ```bash
   # macOS
   brew install redis
   
   # Ubuntu/Debian
   sudo apt-get install redis-server
   
   # Windows
   # Use Docker or WSL
   ```

2. **Start Redis**:
   ```bash
   # macOS/Linux
   redis-server
   
   # Or as a service
   sudo systemctl start redis
   ```

3. **Configure Environment**:
   ```bash
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=""  # Set password for production
   REDIS_DB=0
   ```

### Redis Usage

- **Caching**: Domain analysis results, API responses
- **Queue Management**: Background job processing with Bull
- **Session Storage**: User sessions and temporary data

## Third-Party API Keys

### Ahrefs API

1. **Sign up** at [Ahrefs API](https://ahrefs.com/api)
2. **Get API key** from your dashboard
3. **Set environment variable**:
   ```bash
   AHREFS_API_KEY=your_ahrefs_api_key
   ```

**Features provided**:
- Domain Rating (DR)
- Backlink data
- Organic keywords
- Competitor analysis

### SimilarWeb API

1. **Sign up** at [SimilarWeb API](https://www.similarweb.com/corp/developer/)
2. **Get API key** from your account
3. **Set environment variable**:
   ```bash
   SIMILARWEB_API_KEY=your_similarweb_api_key
   ```

**Features provided**:
- Website traffic data
- Traffic trends
- Geographic distribution
- Engagement metrics

### WhoisXML API

1. **Sign up** at [WhoisXML API](https://whoisxml.com/)
2. **Get API key** from your dashboard
3. **Set environment variable**:
   ```bash
   WHOISXML_API_KEY=your_whoisxml_api_key
   ```

**Features provided**:
- Domain age information
- WHOIS data
- Domain registration details
- DNS information

## Queue Configuration

### Bull Queue Settings

```bash
# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=""
QUEUE_REDIS_DB=1  # Use different DB for queues
QUEUE_DEFAULT_JOB_ATTEMPTS=3
QUEUE_DEFAULT_JOB_BACKOFF_DELAY=2000
```

### Queue Types

1. **Domain Analysis Queue**: Individual domain processing
2. **Batch Analysis Queue**: Bulk domain processing
3. **API Rate Limit Queue**: Third-party API call management

### Queue Monitoring

Access queue dashboard at: `http://localhost:3050/api/v1/queue/health`

## Scoring Algorithm Configuration

### Weight Configuration

The scoring algorithm uses weighted factors that must sum to 1.0:

```bash
SCORING_DOMAIN_RATING_WEIGHT=0.30      # 30%
SCORING_TRAFFIC_VOLUME_WEIGHT=0.20     # 20%
SCORING_KEYWORD_RELEVANCE_WEIGHT=0.20  # 20%
SCORING_WEBSITE_AGE_WEIGHT=0.10        # 10%
SCORING_INDUSTRY_MATCH_WEIGHT=0.10     # 10%
SCORING_COUNTRY_LANGUAGE_MATCH_WEIGHT=0.05  # 5%
SCORING_CONTACT_INFO_PRESENT_WEIGHT=0.05    # 5%
```

### Customizing Weights

You can adjust weights based on your specific requirements:

- **High DR Focus**: Increase `SCORING_DOMAIN_RATING_WEIGHT` to 0.40
- **Traffic Focus**: Increase `SCORING_TRAFFIC_VOLUME_WEIGHT` to 0.30
- **Relevance Focus**: Increase `SCORING_KEYWORD_RELEVANCE_WEIGHT` to 0.30

**Important**: Ensure all weights sum to exactly 1.0.

## Development vs Production

### Development Configuration

```bash
NODE_ENV=development
LOG_LEVEL=debug
SWAGGER_ENABLED=true
DB_SSL=false
BYPASS_DOMAIN_VERIFICATION=true
```

### Production Configuration

```bash
NODE_ENV=production
LOG_LEVEL=info
SWAGGER_ENABLED=false
DB_SSL=true
BYPASS_DOMAIN_VERIFICATION=false

# Strong JWT secrets
JWT_SECRET=your-production-jwt-secret-minimum-32-characters
JWT_REFRESH_SECRET=your-production-refresh-secret-minimum-32-characters

# Secure Redis
REDIS_PASSWORD=your-secure-redis-password

# API Keys
AHREFS_API_KEY=your-production-ahrefs-key
SIMILARWEB_API_KEY=your-production-similarweb-key
WHOISXML_API_KEY=your-production-whoisxml-key
```

## Docker Configuration

### Docker Compose Example

```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3050:3050"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: backlink
      POSTGRES_USER: backlink_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass secure_redis_password

volumes:
  postgres_data:
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify connection credentials
   - Ensure database exists

2. **Redis Connection Failed**
   - Check Redis is running
   - Verify Redis configuration
   - Check firewall settings

3. **API Keys Not Working**
   - Verify API keys are correct
   - Check API quotas and limits
   - Ensure proper environment variable names

4. **Queue Jobs Failing**
   - Check Redis connection
   - Verify queue configuration
   - Monitor queue dashboard

### Health Checks

Monitor application health:
- **Basic Health**: `GET /health`
- **Detailed Health**: `GET /health/detailed`
- **Readiness**: `GET /health/ready`
- **Liveness**: `GET /health/live`

### Logs

Check application logs:
```bash
# Development
tail -f logs/app.log

# Production
docker logs container_name
```

## Support

For additional support:
1. Check the API documentation at `/api/docs`
2. Review application logs
3. Monitor health check endpoints
4. Verify environment configuration
